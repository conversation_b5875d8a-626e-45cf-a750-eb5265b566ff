import{_ as c,l as te,d as W,H as fe,ae as ye,af as be,ag as me,V as _e,C as K,i as G,p as Ee,J as ke,W as Se,X as le,Y as ce}from"./MermaidPreview-DN0CF7bz.js";import"./premium-CrQDER7x.js";import"./user-config-BrqC82sm.js";import"./merge-CG3iZ9md.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},u=new Error().stack;u&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[u]="b9fe06a4-000b-41ac-ab48-300cbccf52d7",e._sentryDebugIdIdentifier="sentry-dbid-b9fe06a4-000b-41ac-ab48-300cbccf52d7")}catch{}})();var $=function(){var e=c(function(_,s,n,a){for(n=n||{},a=_.length;a--;n[_[a]]=s);return n},"o"),u=[1,4],d=[1,13],r=[1,12],p=[1,15],E=[1,16],f=[1,20],h=[1,19],L=[6,7,8],C=[1,26],w=[1,24],N=[1,25],i=[6,7,11],H=[1,31],x=[6,7,11,24],P=[1,6,13,16,17,20,23],M=[1,35],U=[1,36],A=[1,6,7,11,13,16,17,20,23],j=[1,38],V={trace:c(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,KANBAN:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,shapeData:15,ICON:16,CLASS:17,nodeWithId:18,nodeWithoutId:19,NODE_DSTART:20,NODE_DESCR:21,NODE_DEND:22,NODE_ID:23,SHAPE_DATA:24,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"KANBAN",11:"EOF",13:"SPACELIST",16:"ICON",17:"CLASS",20:"NODE_DSTART",21:"NODE_DESCR",22:"NODE_DEND",23:"NODE_ID",24:"SHAPE_DATA"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,3],[12,2],[12,2],[12,2],[12,1],[12,2],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[19,3],[18,1],[18,4],[15,2],[15,1]],performAction:c(function(s,n,a,o,g,t,B){var l=t.length-1;switch(g){case 6:case 7:return o;case 8:o.getLogger().trace("Stop NL ");break;case 9:o.getLogger().trace("Stop EOF ");break;case 11:o.getLogger().trace("Stop NL2 ");break;case 12:o.getLogger().trace("Stop EOF2 ");break;case 15:o.getLogger().info("Node: ",t[l-1].id),o.addNode(t[l-2].length,t[l-1].id,t[l-1].descr,t[l-1].type,t[l]);break;case 16:o.getLogger().info("Node: ",t[l].id),o.addNode(t[l-1].length,t[l].id,t[l].descr,t[l].type);break;case 17:o.getLogger().trace("Icon: ",t[l]),o.decorateNode({icon:t[l]});break;case 18:case 23:o.decorateNode({class:t[l]});break;case 19:o.getLogger().trace("SPACELIST");break;case 20:o.getLogger().trace("Node: ",t[l-1].id),o.addNode(0,t[l-1].id,t[l-1].descr,t[l-1].type,t[l]);break;case 21:o.getLogger().trace("Node: ",t[l].id),o.addNode(0,t[l].id,t[l].descr,t[l].type);break;case 22:o.decorateNode({icon:t[l]});break;case 27:o.getLogger().trace("node found ..",t[l-2]),this.$={id:t[l-1],descr:t[l-1],type:o.getType(t[l-2],t[l])};break;case 28:this.$={id:t[l],descr:t[l],type:0};break;case 29:o.getLogger().trace("node found ..",t[l-3]),this.$={id:t[l-3],descr:t[l-1],type:o.getType(t[l-2],t[l])};break;case 30:this.$=t[l-1]+t[l];break;case 31:this.$=t[l];break}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:u},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:u},{6:d,7:[1,10],9:9,12:11,13:r,14:14,16:p,17:E,18:17,19:18,20:f,23:h},e(L,[2,3]),{1:[2,2]},e(L,[2,4]),e(L,[2,5]),{1:[2,6],6:d,12:21,13:r,14:14,16:p,17:E,18:17,19:18,20:f,23:h},{6:d,9:22,12:11,13:r,14:14,16:p,17:E,18:17,19:18,20:f,23:h},{6:C,7:w,10:23,11:N},e(i,[2,24],{18:17,19:18,14:27,16:[1,28],17:[1,29],20:f,23:h}),e(i,[2,19]),e(i,[2,21],{15:30,24:H}),e(i,[2,22]),e(i,[2,23]),e(x,[2,25]),e(x,[2,26]),e(x,[2,28],{20:[1,32]}),{21:[1,33]},{6:C,7:w,10:34,11:N},{1:[2,7],6:d,12:21,13:r,14:14,16:p,17:E,18:17,19:18,20:f,23:h},e(P,[2,14],{7:M,11:U}),e(A,[2,8]),e(A,[2,9]),e(A,[2,10]),e(i,[2,16],{15:37,24:H}),e(i,[2,17]),e(i,[2,18]),e(i,[2,20],{24:j}),e(x,[2,31]),{21:[1,39]},{22:[1,40]},e(P,[2,13],{7:M,11:U}),e(A,[2,11]),e(A,[2,12]),e(i,[2,15],{24:j}),e(x,[2,30]),{22:[1,41]},e(x,[2,27]),e(x,[2,29])],defaultActions:{2:[2,1],6:[2,2]},parseError:c(function(s,n){if(n.recoverable)this.trace(s);else{var a=new Error(s);throw a.hash=n,a}},"parseError"),parse:c(function(s){var n=this,a=[0],o=[],g=[null],t=[],B=this.table,l="",z=0,ie=0,ue=2,re=1,ge=t.slice.call(arguments,1),b=Object.create(this.lexer),T={yy:{}};for(var J in this.yy)Object.prototype.hasOwnProperty.call(this.yy,J)&&(T.yy[J]=this.yy[J]);b.setInput(s,T.yy),T.yy.lexer=b,T.yy.parser=this,typeof b.yylloc>"u"&&(b.yylloc={});var q=b.yylloc;t.push(q);var de=b.options&&b.options.ranges;typeof T.yy.parseError=="function"?this.parseError=T.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function pe(S){a.length=a.length-2*S,g.length=g.length-S,t.length=t.length-S}c(pe,"popStack");function ae(){var S;return S=o.pop()||b.lex()||re,typeof S!="number"&&(S instanceof Array&&(o=S,S=o.pop()),S=n.symbols_[S]||S),S}c(ae,"lex");for(var k,R,v,Q,F={},X,O,oe,Y;;){if(R=a[a.length-1],this.defaultActions[R]?v=this.defaultActions[R]:((k===null||typeof k>"u")&&(k=ae()),v=B[R]&&B[R][k]),typeof v>"u"||!v.length||!v[0]){var Z="";Y=[];for(X in B[R])this.terminals_[X]&&X>ue&&Y.push("'"+this.terminals_[X]+"'");b.showPosition?Z="Parse error on line "+(z+1)+`:
`+b.showPosition()+`
Expecting `+Y.join(", ")+", got '"+(this.terminals_[k]||k)+"'":Z="Parse error on line "+(z+1)+": Unexpected "+(k==re?"end of input":"'"+(this.terminals_[k]||k)+"'"),this.parseError(Z,{text:b.match,token:this.terminals_[k]||k,line:b.yylineno,loc:q,expected:Y})}if(v[0]instanceof Array&&v.length>1)throw new Error("Parse Error: multiple actions possible at state: "+R+", token: "+k);switch(v[0]){case 1:a.push(k),g.push(b.yytext),t.push(b.yylloc),a.push(v[1]),k=null,ie=b.yyleng,l=b.yytext,z=b.yylineno,q=b.yylloc;break;case 2:if(O=this.productions_[v[1]][1],F.$=g[g.length-O],F._$={first_line:t[t.length-(O||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-(O||1)].first_column,last_column:t[t.length-1].last_column},de&&(F._$.range=[t[t.length-(O||1)].range[0],t[t.length-1].range[1]]),Q=this.performAction.apply(F,[l,ie,z,T.yy,v[1],g,t].concat(ge)),typeof Q<"u")return Q;O&&(a=a.slice(0,-1*O*2),g=g.slice(0,-1*O),t=t.slice(0,-1*O)),a.push(this.productions_[v[1]][0]),g.push(F.$),t.push(F._$),oe=B[a[a.length-2]][a[a.length-1]],a.push(oe);break;case 3:return!0}}return!0},"parse")},m=function(){var _={EOF:1,parseError:c(function(n,a){if(this.yy.parser)this.yy.parser.parseError(n,a);else throw new Error(n)},"parseError"),setInput:c(function(s,n){return this.yy=n||this.yy||{},this._input=s,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:c(function(){var s=this._input[0];this.yytext+=s,this.yyleng++,this.offset++,this.match+=s,this.matched+=s;var n=s.match(/(?:\r\n?|\n).*/g);return n?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),s},"input"),unput:c(function(s){var n=s.length,a=s.split(/(?:\r\n?|\n)/g);this._input=s+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-n),this.offset-=n;var o=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),a.length-1&&(this.yylineno-=a.length-1);var g=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===o.length?this.yylloc.first_column:0)+o[o.length-a.length].length-a[0].length:this.yylloc.first_column-n},this.options.ranges&&(this.yylloc.range=[g[0],g[0]+this.yyleng-n]),this.yyleng=this.yytext.length,this},"unput"),more:c(function(){return this._more=!0,this},"more"),reject:c(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:c(function(s){this.unput(this.match.slice(s))},"less"),pastInput:c(function(){var s=this.matched.substr(0,this.matched.length-this.match.length);return(s.length>20?"...":"")+s.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:c(function(){var s=this.match;return s.length<20&&(s+=this._input.substr(0,20-s.length)),(s.substr(0,20)+(s.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:c(function(){var s=this.pastInput(),n=new Array(s.length+1).join("-");return s+this.upcomingInput()+`
`+n+"^"},"showPosition"),test_match:c(function(s,n){var a,o,g;if(this.options.backtrack_lexer&&(g={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(g.yylloc.range=this.yylloc.range.slice(0))),o=s[0].match(/(?:\r\n?|\n).*/g),o&&(this.yylineno+=o.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:o?o[o.length-1].length-o[o.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+s[0].length},this.yytext+=s[0],this.match+=s[0],this.matches=s,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(s[0].length),this.matched+=s[0],a=this.performAction.call(this,this.yy,this,n,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),a)return a;if(this._backtrack){for(var t in g)this[t]=g[t];return!1}return!1},"test_match"),next:c(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var s,n,a,o;this._more||(this.yytext="",this.match="");for(var g=this._currentRules(),t=0;t<g.length;t++)if(a=this._input.match(this.rules[g[t]]),a&&(!n||a[0].length>n[0].length)){if(n=a,o=t,this.options.backtrack_lexer){if(s=this.test_match(a,g[t]),s!==!1)return s;if(this._backtrack){n=!1;continue}else return!1}else if(!this.options.flex)break}return n?(s=this.test_match(n,g[o]),s!==!1?s:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:c(function(){var n=this.next();return n||this.lex()},"lex"),begin:c(function(n){this.conditionStack.push(n)},"begin"),popState:c(function(){var n=this.conditionStack.length-1;return n>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:c(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:c(function(n){return n=this.conditionStack.length-1-Math.abs(n||0),n>=0?this.conditionStack[n]:"INITIAL"},"topState"),pushState:c(function(n){this.begin(n)},"pushState"),stateStackSize:c(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:c(function(n,a,o,g){switch(o){case 0:return this.pushState("shapeData"),a.yytext="",24;case 1:return this.pushState("shapeDataStr"),24;case 2:return this.popState(),24;case 3:const t=/\n\s*/g;return a.yytext=a.yytext.replace(t,"<br/>"),24;case 4:return 24;case 5:this.popState();break;case 6:return n.getLogger().trace("Found comment",a.yytext),6;case 7:return 8;case 8:this.begin("CLASS");break;case 9:return this.popState(),17;case 10:this.popState();break;case 11:n.getLogger().trace("Begin icon"),this.begin("ICON");break;case 12:return n.getLogger().trace("SPACELINE"),6;case 13:return 7;case 14:return 16;case 15:n.getLogger().trace("end icon"),this.popState();break;case 16:return n.getLogger().trace("Exploding node"),this.begin("NODE"),20;case 17:return n.getLogger().trace("Cloud"),this.begin("NODE"),20;case 18:return n.getLogger().trace("Explosion Bang"),this.begin("NODE"),20;case 19:return n.getLogger().trace("Cloud Bang"),this.begin("NODE"),20;case 20:return this.begin("NODE"),20;case 21:return this.begin("NODE"),20;case 22:return this.begin("NODE"),20;case 23:return this.begin("NODE"),20;case 24:return 13;case 25:return 23;case 26:return 11;case 27:this.begin("NSTR2");break;case 28:return"NODE_DESCR";case 29:this.popState();break;case 30:n.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 31:return n.getLogger().trace("description:",a.yytext),"NODE_DESCR";case 32:this.popState();break;case 33:return this.popState(),n.getLogger().trace("node end ))"),"NODE_DEND";case 34:return this.popState(),n.getLogger().trace("node end )"),"NODE_DEND";case 35:return this.popState(),n.getLogger().trace("node end ...",a.yytext),"NODE_DEND";case 36:return this.popState(),n.getLogger().trace("node end (("),"NODE_DEND";case 37:return this.popState(),n.getLogger().trace("node end (-"),"NODE_DEND";case 38:return this.popState(),n.getLogger().trace("node end (-"),"NODE_DEND";case 39:return this.popState(),n.getLogger().trace("node end (("),"NODE_DEND";case 40:return this.popState(),n.getLogger().trace("node end (("),"NODE_DEND";case 41:return n.getLogger().trace("Long description:",a.yytext),21;case 42:return n.getLogger().trace("Long description:",a.yytext),21}},"anonymous"),rules:[/^(?:@\{)/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^\"]+)/i,/^(?:[^}^"]+)/i,/^(?:\})/i,/^(?:\s*%%.*)/i,/^(?:kanban\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}@]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{shapeDataEndBracket:{rules:[],inclusive:!1},shapeDataStr:{rules:[2,3],inclusive:!1},shapeData:{rules:[1,4,5],inclusive:!1},CLASS:{rules:[9,10],inclusive:!1},ICON:{rules:[14,15],inclusive:!1},NSTR2:{rules:[28,29],inclusive:!1},NSTR:{rules:[31,32],inclusive:!1},NODE:{rules:[27,30,33,34,35,36,37,38,39,40,41,42],inclusive:!1},INITIAL:{rules:[0,6,7,8,11,12,13,16,17,18,19,20,21,22,23,24,25,26],inclusive:!0}}};return _}();V.lexer=m;function I(){this.yy={}}return c(I,"Parser"),I.prototype=V,V.Parser=I,new I}();$.parser=$;var Ne=$,D=[],ne=[],ee=0,se={},xe=c(()=>{D=[],ne=[],ee=0,se={}},"clear"),ve=c(e=>{if(D.length===0)return null;const u=D[0].level;let d=null;for(let r=D.length-1;r>=0;r--)if(D[r].level===u&&!d&&(d=D[r]),D[r].level<u)throw new Error('Items without section detected, found section ("'+D[r].label+'")');return e===(d==null?void 0:d.level)?null:d},"getSection"),he=c(function(){return ne},"getSections"),De=c(function(){const e=[],u=[],d=he(),r=W();for(const p of d){const E={id:p.id,label:G(p.label??"",r),isGroup:!0,ticket:p.ticket,shape:"kanbanSection",level:p.level,look:r.look};u.push(E);const f=D.filter(h=>h.parentId===p.id);for(const h of f){const L={id:h.id,parentId:p.id,label:G(h.label??"",r),isGroup:!1,ticket:h==null?void 0:h.ticket,priority:h==null?void 0:h.priority,assigned:h==null?void 0:h.assigned,icon:h==null?void 0:h.icon,shape:"kanbanItem",level:h.level,rx:5,ry:5,cssStyles:["text-align: left"]};u.push(L)}}return{nodes:u,edges:e,other:{},config:W()}},"getData"),Le=c((e,u,d,r,p)=>{var C,w;const E=W();let f=((C=E.mindmap)==null?void 0:C.padding)??K.mindmap.padding;switch(r){case y.ROUNDED_RECT:case y.RECT:case y.HEXAGON:f*=2}const h={id:G(u,E)||"kbn"+ee++,level:e,label:G(d,E),width:((w=E.mindmap)==null?void 0:w.maxNodeWidth)??K.mindmap.maxNodeWidth,padding:f,isGroup:!1};if(p!==void 0){let N;p.includes(`
`)?N=p+`
`:N=`{
`+p+`
}`;const i=Ee(N,{schema:ke});if(i.shape&&(i.shape!==i.shape.toLowerCase()||i.shape.includes("_")))throw new Error(`No such shape: ${i.shape}. Shape names should be lowercase.`);i!=null&&i.shape&&i.shape==="kanbanItem"&&(h.shape=i==null?void 0:i.shape),i!=null&&i.label&&(h.label=i==null?void 0:i.label),i!=null&&i.icon&&(h.icon=i==null?void 0:i.icon.toString()),i!=null&&i.assigned&&(h.assigned=i==null?void 0:i.assigned.toString()),i!=null&&i.ticket&&(h.ticket=i==null?void 0:i.ticket.toString()),i!=null&&i.priority&&(h.priority=i==null?void 0:i.priority)}const L=ve(e);L?h.parentId=L.id||"kbn"+ee++:ne.push(h),D.push(h)},"addNode"),y={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},Ie=c((e,u)=>{switch(te.debug("In get type",e,u),e){case"[":return y.RECT;case"(":return u===")"?y.ROUNDED_RECT:y.CLOUD;case"((":return y.CIRCLE;case")":return y.CLOUD;case"))":return y.BANG;case"{{":return y.HEXAGON;default:return y.DEFAULT}},"getType"),Oe=c((e,u)=>{se[e]=u},"setElementForId"),Ce=c(e=>{if(!e)return;const u=W(),d=D[D.length-1];e.icon&&(d.icon=G(e.icon,u)),e.class&&(d.cssClasses=G(e.class,u))},"decorateNode"),we=c(e=>{switch(e){case y.DEFAULT:return"no-border";case y.RECT:return"rect";case y.ROUNDED_RECT:return"rounded-rect";case y.CIRCLE:return"circle";case y.CLOUD:return"cloud";case y.BANG:return"bang";case y.HEXAGON:return"hexgon";default:return"no-border"}},"type2Str"),Ae=c(()=>te,"getLogger"),Te=c(e=>se[e],"getElementById"),Re={clear:xe,addNode:Le,getSections:he,getData:De,nodeType:y,getType:Ie,setElementForId:Oe,decorateNode:Ce,type2Str:we,getLogger:Ae,getElementById:Te},Pe=Re,Ve=c(async(e,u,d,r)=>{var M,U,A,j,V;te.debug(`Rendering kanban diagram
`+e);const E=r.db.getData(),f=W();f.htmlLabels=!1;const h=fe(u),L=h.append("g");L.attr("class","sections");const C=h.append("g");C.attr("class","items");const w=E.nodes.filter(m=>m.isGroup);let N=0;const i=10,H=[];let x=25;for(const m of w){const I=((M=f==null?void 0:f.kanban)==null?void 0:M.sectionWidth)||200;N=N+1,m.x=I*N+(N-1)*i/2,m.width=I,m.y=0,m.height=I*3,m.rx=5,m.ry=5,m.cssClasses=m.cssClasses+" section-"+N;const _=await ye(L,m);x=Math.max(x,(U=_==null?void 0:_.labelBBox)==null?void 0:U.height),H.push(_)}let P=0;for(const m of w){const I=H[P];P=P+1;const _=((A=f==null?void 0:f.kanban)==null?void 0:A.sectionWidth)||200,s=-_*3/2+x;let n=s;const a=E.nodes.filter(t=>t.parentId===m.id);for(const t of a){if(t.isGroup)throw new Error("Groups within groups are not allowed in Kanban diagrams");t.x=m.x,t.width=_-1.5*i;const l=(await be(C,t,{config:f})).node().getBBox();t.y=n+l.height/2,await me(t),n=t.y+l.height/2+i/2}const o=I.cluster.select("rect"),g=Math.max(n-s+3*i,50)+(x-25);o.attr("height",g)}_e(void 0,h,((j=f.mindmap)==null?void 0:j.padding)??K.kanban.padding,((V=f.mindmap)==null?void 0:V.useMaxWidth)??K.kanban.useMaxWidth)},"draw"),Be={draw:Ve},Fe=c(e=>{let u="";for(let r=0;r<e.THEME_COLOR_LIMIT;r++)e["lineColor"+r]=e["lineColor"+r]||e["cScaleInv"+r],Se(e["lineColor"+r])?e["lineColor"+r]=le(e["lineColor"+r],20):e["lineColor"+r]=ce(e["lineColor"+r],20);const d=c((r,p)=>e.darkMode?ce(r,p):le(r,p),"adjuster");for(let r=0;r<e.THEME_COLOR_LIMIT;r++){const p=""+(17-3*r);u+=`
    .section-${r-1} rect, .section-${r-1} path, .section-${r-1} circle, .section-${r-1} polygon, .section-${r-1} path  {
      fill: ${d(e["cScale"+r],10)};
      stroke: ${d(e["cScale"+r],10)};

    }
    .section-${r-1} text {
     fill: ${e["cScaleLabel"+r]};
    }
    .node-icon-${r-1} {
      font-size: 40px;
      color: ${e["cScaleLabel"+r]};
    }
    .section-edge-${r-1}{
      stroke: ${e["cScale"+r]};
    }
    .edge-depth-${r-1}{
      stroke-width: ${p};
    }
    .section-${r-1} line {
      stroke: ${e["cScaleInv"+r]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.background};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }

  .kanban-ticket-link {
    fill: ${e.background};
    stroke: ${e.nodeBorder};
    text-decoration: underline;
  }
    `}return u},"genSections"),Ge=c(e=>`
  .edge {
    stroke-width: 3;
  }
  ${Fe(e)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${e.git0};
  }
  .section-root text {
    fill: ${e.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .cluster-label, .label {
    color: ${e.textColor};
    fill: ${e.textColor};
    }
  .kanban-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
`,"getStyles"),He=Ge,ze={db:Pe,renderer:Be,parser:Ne,styles:He};export{ze as diagram};
//# sourceMappingURL=kanban-definition-NDS4AKOZ-Br2DcmKm.js.map
