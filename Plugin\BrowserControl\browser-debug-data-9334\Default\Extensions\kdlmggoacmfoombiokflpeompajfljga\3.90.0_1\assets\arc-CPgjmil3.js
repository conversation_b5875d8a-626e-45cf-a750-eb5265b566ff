import{$ as fn,a0 as an,a1 as d,a2 as tn,a3 as C,a4 as I,a5 as _,a6 as rn,a7 as un,a8 as H,a9 as l,aa as j,ab as sn,ac as ln,ad as on}from"./MermaidPreview-DN0CF7bz.js";(function(){try{var s=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},o=new Error().stack;o&&(s._sentryDebugIds=s._sentryDebugIds||{},s._sentryDebugIds[o]="0d5f4524-258c-4af3-990e-8be8fc2333e4",s._sentryDebugIdIdentifier="sentry-dbid-0d5f4524-258c-4af3-990e-8be8fc2333e4")}catch{}})();function cn(s){return s.innerRadius}function yn(s){return s.outerRadius}function dn(s){return s.startAngle}function gn(s){return s.endAngle}function pn(s){return s&&s.padAngle}function mn(s,o,D,P,v,A,k,a){var q=D-s,i=P-o,n=k-v,p=a-A,r=p*q-n*i;if(!(r*r<d))return r=(n*(o-A)-p*(s-v))/r,[s+r*q,o+r*i]}function Q(s,o,D,P,v,A,k){var a=s-D,q=o-P,i=(k?A:-A)/H(a*a+q*q),n=i*q,p=-i*a,r=s+n,f=o+p,c=D+n,y=P+p,z=(r+c)/2,t=(f+y)/2,m=c-r,g=y-f,R=m*m+g*g,T=v-A,b=r*y-c*f,O=(g<0?-1:1)*H(on(0,T*T*R-b*b)),S=(b*g-m*O)/R,$=(-b*m-g*O)/R,w=(b*g+m*O)/R,x=(-b*m+g*O)/R,h=S-z,e=$-t,u=w-z,B=x-t;return h*h+e*e>u*u+B*B&&(S=w,$=x),{cx:S,cy:$,x01:-n,y01:-p,x11:S*(v/T-1),y11:$*(v/T-1)}}function hn(){var s=cn,o=yn,D=j(0),P=null,v=dn,A=gn,k=pn,a=null,q=fn(i);function i(){var n,p,r=+s.apply(this,arguments),f=+o.apply(this,arguments),c=v.apply(this,arguments)-an,y=A.apply(this,arguments)-an,z=rn(y-c),t=y>c;if(a||(a=n=q()),f<r&&(p=f,f=r,r=p),!(f>d))a.moveTo(0,0);else if(z>tn-d)a.moveTo(f*C(c),f*I(c)),a.arc(0,0,f,c,y,!t),r>d&&(a.moveTo(r*C(y),r*I(y)),a.arc(0,0,r,y,c,t));else{var m=c,g=y,R=c,T=y,b=z,O=z,S=k.apply(this,arguments)/2,$=S>d&&(P?+P.apply(this,arguments):H(r*r+f*f)),w=_(rn(f-r)/2,+D.apply(this,arguments)),x=w,h=w,e,u;if($>d){var B=sn($/r*I(S)),J=sn($/f*I(S));(b-=B*2)>d?(B*=t?1:-1,R+=B,T-=B):(b=0,R=T=(c+y)/2),(O-=J*2)>d?(J*=t?1:-1,m+=J,g-=J):(O=0,m=g=(c+y)/2)}var F=f*C(m),G=f*I(m),K=r*C(T),L=r*I(T);if(w>d){var M=f*C(g),N=f*I(g),U=r*C(R),V=r*I(R),E;if(z<un)if(E=mn(F,G,U,V,M,N,K,L)){var W=F-E[0],X=G-E[1],Y=M-E[0],Z=N-E[1],nn=1/I(ln((W*Y+X*Z)/(H(W*W+X*X)*H(Y*Y+Z*Z)))/2),en=H(E[0]*E[0]+E[1]*E[1]);x=_(w,(r-en)/(nn-1)),h=_(w,(f-en)/(nn+1))}else x=h=0}O>d?h>d?(e=Q(U,V,F,G,f,h,t),u=Q(M,N,K,L,f,h,t),a.moveTo(e.cx+e.x01,e.cy+e.y01),h<w?a.arc(e.cx,e.cy,h,l(e.y01,e.x01),l(u.y01,u.x01),!t):(a.arc(e.cx,e.cy,h,l(e.y01,e.x01),l(e.y11,e.x11),!t),a.arc(0,0,f,l(e.cy+e.y11,e.cx+e.x11),l(u.cy+u.y11,u.cx+u.x11),!t),a.arc(u.cx,u.cy,h,l(u.y11,u.x11),l(u.y01,u.x01),!t))):(a.moveTo(F,G),a.arc(0,0,f,m,g,!t)):a.moveTo(F,G),!(r>d)||!(b>d)?a.lineTo(K,L):x>d?(e=Q(K,L,M,N,r,-x,t),u=Q(F,G,U,V,r,-x,t),a.lineTo(e.cx+e.x01,e.cy+e.y01),x<w?a.arc(e.cx,e.cy,x,l(e.y01,e.x01),l(u.y01,u.x01),!t):(a.arc(e.cx,e.cy,x,l(e.y01,e.x01),l(e.y11,e.x11),!t),a.arc(0,0,r,l(e.cy+e.y11,e.cx+e.x11),l(u.cy+u.y11,u.cx+u.x11),t),a.arc(u.cx,u.cy,x,l(u.y11,u.x11),l(u.y01,u.x01),!t))):a.arc(0,0,r,T,R,t)}if(a.closePath(),n)return a=null,n+""||null}return i.centroid=function(){var n=(+s.apply(this,arguments)+ +o.apply(this,arguments))/2,p=(+v.apply(this,arguments)+ +A.apply(this,arguments))/2-un/2;return[C(p)*n,I(p)*n]},i.innerRadius=function(n){return arguments.length?(s=typeof n=="function"?n:j(+n),i):s},i.outerRadius=function(n){return arguments.length?(o=typeof n=="function"?n:j(+n),i):o},i.cornerRadius=function(n){return arguments.length?(D=typeof n=="function"?n:j(+n),i):D},i.padRadius=function(n){return arguments.length?(P=n==null?null:typeof n=="function"?n:j(+n),i):P},i.startAngle=function(n){return arguments.length?(v=typeof n=="function"?n:j(+n),i):v},i.endAngle=function(n){return arguments.length?(A=typeof n=="function"?n:j(+n),i):A},i.padAngle=function(n){return arguments.length?(k=typeof n=="function"?n:j(+n),i):k},i.context=function(n){return arguments.length?(a=n??null,i):a},i}export{hn as d};
//# sourceMappingURL=arc-CPgjmil3.js.map
