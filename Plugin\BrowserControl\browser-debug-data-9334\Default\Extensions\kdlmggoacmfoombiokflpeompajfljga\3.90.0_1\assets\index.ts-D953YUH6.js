import{B as a,g as n,A as i}from"./user-config-BrqC82sm.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="12bb1765-adee-49e3-b934-01716f310e1d",e._sentryDebugIdIdentifier="sentry-dbid-12bb1765-adee-49e3-b934-01716f310e1d")}catch{}})();globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};chrome.storage.session.setAccessLevel({accessLevel:"TRUSTED_AND_UNTRUSTED_CONTEXTS"});async function s(){const{startupPage:e}=await n(),t=e===i?"/":`/chat/${e}`;await a.tabs.create({url:`app.html#${t}`})}a.action.onClicked.addListener(async()=>{await s()});a.commands.onCommand.addListener(async e=>{e==="open-app"&&await s()});a.runtime.onInstalled.addListener(async e=>{e.reason==="install"&&await Promise.allSettled([a.tabs.create({url:"https://chatgpt.com/",active:!1}),a.tabs.create({url:"app.html#/setting?install=true"})])});a.runtime.setUninstallURL("https://chathub.gg/uninstall");
//# sourceMappingURL=index.ts-D953YUH6.js.map
