{"action": {"default_icon": {"16": "data/icons/ignored/16.png", "32": "data/icons/ignored/32.png", "48": "data/icons/ignored/48.png"}, "default_popup": "data/popup/index.html"}, "background": {"service_worker": "worker.js"}, "commands": {"_execute_action": {"description": "Execute Action"}}, "default_locale": "en", "description": "__MSG_extensionDescription__", "homepage_url": "https://webextension.org/listing/useragent-switcher.html", "host_permissions": ["<all_urls>"], "icons": {"128": "data/icons/active/128.png", "16": "data/icons/active/16.png", "256": "data/icons/active/256.png", "32": "data/icons/active/32.png", "48": "data/icons/active/48.png", "64": "data/icons/active/64.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyysy1IEBNm6uXGKUTr6wgh0EuhTxpYNGt0N03bm4LaBc/lR/EiQV3eZHn9HrL1cUrVnzxDf2xu7cn0GQLeb28TCBXecqZubTnn3a4mszsQYKxSMqMHHruPmGebw2snS8joQACLHoguISd9ysB2+ydIBrAkxCsocOxzTJGOEcRUXMEvOfFCQHWwA2DMuYw1cXkD1FJuhRdCbSEehCph0gFBKtAW/HH33fKXvj7N/vpH1O+HzYq2P1ZcQhN72ciA/58vBl8gar21qKY18oMQYrvznSPJwzOODgs1z74juvljK6h42M38+7Lad7ZgSMWNVSWc64nbl2SwPZ0rt8zM4xcQIDAQAB", "manifest_version": 3, "name": "__MSG_extensionName__", "options_ui": {"open_in_tab": true, "page": "data/options/index.html"}, "permissions": ["storage", "contextMenus", "scripting", "declarativeNetRequestWithHostAccess"], "storage": {"managed_schema": "schema.json"}, "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "0.6.4"}