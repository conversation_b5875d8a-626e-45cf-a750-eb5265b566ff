(()=>{"use strict";var e,t,r,o,n,a,i={},l={};function u(e){var t=l[e];if(void 0!==t)return t.exports;var r=l[e]={id:e,loaded:!1,exports:{}};return i[e].call(r.exports,r,r.exports,u),r.loaded=!0,r.exports}u.m=i,e=[],u.O=(t,r,o,n)=>{if(!r){var a=1/0;for(c=0;c<e.length;c++){for(var[r,o,n]=e[c],i=!0,l=0;l<r.length;l++)(!1&n||a>=n)&&Object.keys(u.O).every((e=>u.O[e](r[l])))?r.splice(l--,1):(i=!1,n<a&&(a=n));if(i){e.splice(c--,1);var d=o();void 0!==d&&(t=d)}}return t}n=n||0;for(var c=e.length;c>0&&e[c-1][2]>n;c--)e[c]=e[c-1];e[c]=[r,o,n]},u.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return u.d(t,{a:t}),t},r=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,u.t=function(e,o){if(1&o&&(e=this(e)),8&o)return e;if("object"==typeof e&&e){if(4&o&&e.__esModule)return e;if(16&o&&"function"==typeof e.then)return e}var n=Object.create(null);u.r(n);var a={};t=t||[null,r({}),r([]),r(r)];for(var i=2&o&&e;"object"==typeof i&&!~t.indexOf(i);i=r(i))Object.getOwnPropertyNames(i).forEach((t=>a[t]=()=>e[t]));return a.default=()=>e,u.d(n,a),n},u.d=(e,t)=>{for(var r in t)u.o(t,r)&&!u.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},u.f={},u.e=e=>Promise.all(Object.keys(u.f).reduce(((t,r)=>(u.f[r](e,t),t)),[])),u.u=e=>502===e?"vendor.bundle.js":"wallet-webui-"+e+"."+{101:"079f5d74a18127cd9d6a",227:"bb2c3c84778e2589775f",708:"de49febeeb0e9c77883f",792:"b1180305c186d50631a2",992:"268aa821c3090dce03cb"}[e]+".chunk.js",u.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),u.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),u.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o={},n="@xpay-wallet/hub:",u.l=(e,t,r,a)=>{if(o[e])o[e].push(t);else{var i,l;if(void 0!==r)for(var d=document.getElementsByTagName("script"),c=0;c<d.length;c++){var f=d[c];if(f.getAttribute("src")==e||f.getAttribute("data-webpack")==n+r){i=f;break}}i||(l=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,u.nc&&i.setAttribute("nonce",u.nc),i.setAttribute("data-webpack",n+r),i.src=u.tu(e)),o[e]=[t];var s=(t,r)=>{i.onerror=i.onload=null,clearTimeout(p);var n=o[e];if(delete o[e],i.parentNode&&i.parentNode.removeChild(i),n&&n.forEach((e=>e(r))),t)return t(r)},p=setTimeout(s.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=s.bind(null,i.onerror),i.onload=s.bind(null,i.onload),l&&document.head.appendChild(i)}},u.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},u.tt=()=>(void 0===a&&(a={createScriptURL:e=>e},"undefined"!=typeof trustedTypes&&trustedTypes.createPolicy&&(a=trustedTypes.createPolicy("wallet-hub",a))),a),u.tu=e=>u.tt().createScriptURL(e),u.p="/",(()=>{var e={121:0};u.f.j=(t,r)=>{var o=u.o(e,t)?e[t]:void 0;if(0!==o)if(o)r.push(o[2]);else if(121!=t){var n=new Promise(((r,n)=>o=e[t]=[r,n]));r.push(o[2]=n);var a=u.p+u.u(t),i=new Error;u.l(a,(r=>{if(u.o(e,t)&&(0!==(o=e[t])&&(e[t]=void 0),o)){var n=r&&("load"===r.type?"missing":r.type),a=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+n+": "+a+")",i.name="ChunkLoadError",i.type=n,i.request=a,o[1](i)}}),"chunk-"+t,t)}else e[t]=0},u.O.j=t=>0===e[t];var t=(t,r)=>{var o,n,[a,i,l]=r,d=0;if(a.some((t=>0!==e[t]))){for(o in i)u.o(i,o)&&(u.m[o]=i[o]);if(l)var c=l(u)}for(t&&t(r);d<a.length;d++)n=a[d],u.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return u.O(c)},r=self.webpackChunk_xpay_wallet_hub=self.webpackChunk_xpay_wallet_hub||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})()})();