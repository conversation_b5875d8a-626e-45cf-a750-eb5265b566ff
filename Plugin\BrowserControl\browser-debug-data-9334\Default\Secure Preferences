{"edge": {"services": {"account_id": "0003BFFD5BEAE44A", "last_username": "<EMAIL>"}}, "edge_fundamentals_appdefaults": {"ess_lightweight_version": 101}, "ess_kv_states": {"restore_on_startup": {"closed_notification": false, "decrypt_success": true, "key": "restore_on_startup", "notification_popup_count": 0}, "startup_urls": {"closed_notification": false, "decrypt_success": true, "key": "startup_urls", "notification_popup_count": 0}, "template_url_data": {"closed_notification": false, "decrypt_success": true, "key": "template_url_data", "notification_popup_count": 0}}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "发现 Microsoft Edge 扩展。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Chrome 网上应用店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "bpelnogcookhocnaokfpoeinibimbeff": {"account_extension_type": 2, "active_permissions": {"api": ["activeTab", "contextMenus", "cookies", "storage", "unlimitedStorage", "newTabPageOverride", "scripting", "sidePanel"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["activeTab", "contextMenus", "cookies", "storage", "unlimitedStorage", "newTabPageOverride", "scripting", "sidePanel"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"48": "icons/icon_zh_action_48.png"}, "default_popup": "popup/index.html", "default_title": "WeTab 标签页"}, "background": {"service_worker": "serviceworker.js"}, "chrome_url_overrides": {"newtab": "index.html"}, "current_locale": "zh_CN", "default_locale": "zh_CN", "description": "用小组件自定义你的新标签页,支持暗黑模式", "host_permissions": ["<all_urls>"], "icons": {"128": "icons/icon_zh_128.png", "16": "icons/icon_zh_16.png", "32": "icons/icon_zh_32.png", "48": "icons/icon_zh_48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsVPNxUUTrxRO+deq8I90l0ZqTWCDmHHHUNbke1cySGUdZ+nz3rwDhtme5Tnwjyxz1CipahKfaD2hF++aRViXHRN5Yazm7bcw6jNP2dR02UKrx0WxbE5+FezXnR1oGX2hit5EUrfrsdsuE3uhGrbd09V45qU7uHHuHA2klCa5P/xGjaNS4m+sFiWd8yuXAmNlthhupfgzCcuBwwPnL2Dcyzdq/b3KThmqSo73yAMaZnQxUyW12UxK0SraDeq/2bixIMvYwmxA4fQi9S1KfTi7h+X2fR8yVVlNURjgNg2xlcDVBzeubxiNc3PV21x5T4sDp3pBHk8PKMBjN4T/MipaIwIDAQAB", "manifest_version": 3, "name": "WeTab 新标签页", "optional_permissions": ["system.cpu", "system.memory", "bookmarks", "history", "favicon"], "permissions": ["cookies", "activeTab", "storage", "unlimitedStorage", "sidePanel", "contextMenus", "scripting"], "side_panel": {"default_path": "sidepanel/index.html"}, "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "2.2.4", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["*"]}]}, "path": "bpelnogcookhocnaokfpoeinibimbeff\\2.2.4_0", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "2.2.4"}, "serviceworkerevents": ["contextMenus.onClicked", "runtime.onInstalled"], "uninstall_url": "https://uninstall.wetab.link/", "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "cjneempfhkonkkbcmnfdibgobmhbagaj": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}}, "cnjkedgepfdpdbnepgmajmmjdjkjnifa": {"account_extension_type": 2, "active_permissions": {"api": ["contextMenus", "storage", "scripting", "declarativeNetRequestWithHostAccess"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["contextMenus", "storage", "scripting", "declarativeNetRequestWithHostAccess"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"16": "data/icons/ignored/16.png", "32": "data/icons/ignored/32.png", "48": "data/icons/ignored/48.png"}, "default_popup": "data/popup/index.html"}, "background": {"service_worker": "worker.js"}, "commands": {"_execute_action": {"description": "Execute Action"}}, "current_locale": "zh_CN", "default_locale": "en", "description": "欺骗性网站试图收集有关你的网页导航的信息，以提供你可能不想要的独特内容", "homepage_url": "https://webextension.org/listing/useragent-switcher.html", "host_permissions": ["<all_urls>"], "icons": {"128": "data/icons/active/128.png", "16": "data/icons/active/16.png", "256": "data/icons/active/256.png", "32": "data/icons/active/32.png", "48": "data/icons/active/48.png", "64": "data/icons/active/64.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyysy1IEBNm6uXGKUTr6wgh0EuhTxpYNGt0N03bm4LaBc/lR/EiQV3eZHn9HrL1cUrVnzxDf2xu7cn0GQLeb28TCBXecqZubTnn3a4mszsQYKxSMqMHHruPmGebw2snS8joQACLHoguISd9ysB2+ydIBrAkxCsocOxzTJGOEcRUXMEvOfFCQHWwA2DMuYw1cXkD1FJuhRdCbSEehCph0gFBKtAW/HH33fKXvj7N/vpH1O+HzYq2P1ZcQhN72ciA/58vBl8gar21qKY18oMQYrvznSPJwzOODgs1z74juvljK6h42M38+7Lad7ZgSMWNVSWc64nbl2SwPZ0rt8zM4xcQIDAQAB", "manifest_version": 3, "name": "User-Agent Switcher and Manager", "options_ui": {"open_in_tab": true, "page": "data/options/index.html"}, "permissions": ["storage", "contextMenus", "scripting", "declarativeNetRequestWithHostAccess"], "storage": {"managed_schema": "schema.json"}, "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "0.6.4"}, "path": "cnjkedgepfdpdbnepgmajmmjdjkjnifa\\0.6.4_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "dcaajljecejllikfgbhjdgeognacjkkp": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}}, "dgbhmbogkcdheijkkdmfhodkamcaiheo": {"disable_reasons": [1]}, "dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "dhoenijjpgpeimemopealfcbiecgceod": {"disable_reasons": [1]}, "dimkapigjeiopninbepgaoobcnbjmgik": {"disable_reasons": [1]}, "ehlmnljdoejdahfjdfobmpfancoibmig": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}}, "epdpgaljfdjmcemiaplofbiholoaepem": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": ["https://account.live.com/Consent/Update?*", "https://login.live.com/oauth20_authorize.srf?*"]}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_scripts": [{"js": ["js/check_page_and_click_button.js"], "matches": ["https://account.live.com/Consent/Update?*", "https://login.live.com/oauth20_authorize.srf?*"], "run_at": "document_start"}], "description": "Suppress consent prompt for quick authentication", "key": "AAAAB3NzaC1yc2EAAAADAQABAAAAgQDIUsSOQUI35YGFiytHV/QeV1LMMeiAQY6RMkY2nuE1yZlZn9Q7uKWK6t87FSBhijLpY7MUK9U0TNJY8ugOmR3ysYF+TOUP8AxvY0y/kop/Cg5TKPBcILZaEm+IsM/xYBhawnDFbJWS+k4SY97ERpd64WO4uiUcmjrYL53zl8inTQ==", "manifest_version": 3, "name": "Suppress Consent Prompt", "version": "1.0.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\edge_suppress_consent_prompt", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fhkhedgjlkcgdaldodbhmcldjglpabmf": {"account_extension_type": 2, "active_bit": false, "active_permissions": {"api": ["activeTab", "contextMenus", "storage", "system.display", "tabs", "sidePanel"], "explicit_host": ["*://*/*"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "allowlist": 1, "commands": {"open-bot": {"suggested_key": "Ctrl+Shift+K"}}, "content_settings": [], "creation_flags": 9, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": true, "granted_permissions": {"api": ["activeTab", "contextMenus", "storage", "system.display", "tabs", "sidePanel"], "explicit_host": ["*://*/*"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"128": "icons/icon.png", "16": "icons/icon.png", "48": "icons/icon.png"}, "default_title": "打开侧边栏"}, "background": {"service_worker": "js/background.js"}, "commands": {"open-bot": {"description": "打开侧边栏", "suggested_key": {"default": "Ctrl+Shift+K", "mac": "Command+K"}}}, "content_scripts": [{"js": ["js/content.js"], "matches": ["<all_urls>"]}], "description": "百度AI助手是您在任何页面上的个人AI助手，可以帮助你快速阅读，为你提供创意灵感，在聊天对话中为你答疑解惑。", "host_permissions": ["*://*/*"], "icons": {"128": "icons/icon.png", "16": "icons/icon.png", "48": "icons/icon.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAn/a/0Jp7/CTBGMZlPb7m+nl4QbJMQB2EbDt4RCXli11Q+zJqx6sVT0EUVM5qTPJNIEqVzzd5cbdhSch2JDSaGIo8Qqmv+zSYbN7eyuXuJPfhfgGQnajnJ9Gihm3wlpMJA2c9nCCqC1fB/MCcxWITs7LUiLei5GWUsEwyAcnVgUDkZMMclZUzWPrT7OP8YrEmrS1hsLe11lHHriMO20ioCzZHenca0I0fH9hmA6So8LWCC4YRLGnllIRWbir/gm3Q+mhTYndK9Vxc9Nkrrm1CgO/pJdYX9o+6PuAKxPAthnroy/ThbUmLFly3l+G/uBNV+6e4urPZZUX584/Gs3SJ6wIDAQAB", "manifest_version": 3, "name": "百度AI—浏览器助手", "options_page": "setting.html", "permissions": ["sidePanel", "contextMenus", "tabs", "activeTab", "storage", "windows", "system.display"], "side_panel": {"default_icon": {"128": "icons/icon.png", "16": "icons/icon.png", "48": "icons/icon.png"}, "default_path": "index-extension.html"}, "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.0.25", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["wise-icon/*", "pc-icon/*", "css/*", "js/*"]}]}, "path": "fhkhedgjlkcgdaldodbhmcldjglpabmf\\1.0.25_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "fikbjbembnmfhppjfnmfkahdhfohhjmg": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.microsoftstream.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsAmDrYmQaYQlLxSAn/jTQTGNt1IffJGIJeKucE/B42d8QIyFD2RCarmHP1bmbY1YuTng2dL3J//qyvUNwXPt9cmxH9WKwi512tzOa5r2zYaCuOgP2vAIrah/bKnpO3XmUfFWj+LRcbZahOmMDMQxzPKxFKuIz2eOiakBXDE6Ok7azHJ13LLQTte1JgZIPmyFrAciPABLp/IKLfsfnebVW1YgaOyxBNyp/7bhSmoyZI3kBv8InKOpGE8pttrBg6l5zkvD67a7ViNAYkqZIpJJV5ZTQtVWCWSG0xU2y+3zXZtx8KbGbDiWUAcwNYDVPpsV+IQXVpgAplHvrZme+hAl6QIDAQAB", "manifest_version": 2, "name": "Media Internals Services Extension", "permissions": ["mediaInternalsPrivate"], "version": "2.0.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\media_internals_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fjngpfnaikknjdhkckmncgicobbkcnle": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}}, "flahobhjikkpnpohomeckhdjjkkkkmoc": {"account_extension_type": 2, "active_permissions": {"api": ["storage", "declarativeNetRequestWithHostAccess"], "explicit_host": ["*://*/*", "<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://app.maxai.me/*", "https://bard.google.com/*", "https://chat.openai.com/*", "https://chatgpt.com/*", "https://chatgpt.com/favicon.ico", "https://claude.ai/*", "https://cn.bing.com/*", "https://duckduckgo.com/*", "https://gemini.google.com/*", "https://search.brave.com/*", "https://search.naver.com/*", "https://search.yahoo.com/search*", "https://twitter.com/*", "https://wap.yandex.com/*", "https://www.amazon.com/s/*", "https://www.baidu.com/*", "https://www.bing.com/*", "https://www.google.com/*", "https://www.maxai.co/*", "https://www.perplexity.ai/*", "https://www.reddit.com/*", "https://www.sogou.com/*", "https://www.youtube.com/*", "https://yandex.com/*"]}, "commands": {"toggle-web-access": {"suggested_key": "Alt+W", "was_assigned": true}}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "dnr_static_ruleset": {"1": {"checksum": 1986079478, "ignore_ruleset": false}}, "edge_last_update_check_time": "13398078263284111", "first_install_time": "13398075327988258", "from_webstore": false, "granted_permissions": {"api": ["storage", "declarativeNetRequestWithHostAccess"], "explicit_host": ["*://*/*", "<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://app.maxai.me/*", "https://bard.google.com/*", "https://chat.openai.com/*", "https://chatgpt.com/*", "https://chatgpt.com/favicon.ico", "https://claude.ai/*", "https://cn.bing.com/*", "https://duckduckgo.com/*", "https://gemini.google.com/*", "https://search.brave.com/*", "https://search.naver.com/*", "https://search.yahoo.com/search*", "https://twitter.com/*", "https://wap.yandex.com/*", "https://www.amazon.com/s/*", "https://www.baidu.com/*", "https://www.bing.com/*", "https://www.google.com/*", "https://www.maxai.co/*", "https://www.perplexity.ai/*", "https://www.reddit.com/*", "https://www.sogou.com/*", "https://www.youtube.com/*", "https://yandex.com/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398075327988258", "lastpingday": "*****************", "location": 1, "manifest": {"action": {}, "background": {"service_worker": "background/bg.js", "type": "module"}, "commands": {"toggle-web-access": {"description": "切换网页访问", "suggested_key": {"default": "Alt+W"}}}, "content_scripts": [{"js": ["import_mainUI.js"], "matches": ["https://chat.openai.com/*", "https://chatgpt.com/*", "https://claude.ai/*", "https://bard.google.com/*", "https://gemini.google.com/*"]}, {"all_frames": true, "js": ["import_proxySearchInject.js"], "matches": ["https://chatgpt.com/*", "https://www.google.com/*", "https://www.perplexity.ai/*", "https://www.baidu.com/*", "https://www.bing.com/*", "https://cn.bing.com/*"], "run_at": "document_start"}, {"js": ["import_authClient.js"], "matches": ["https://app.maxai.me/*", "https://www.maxai.co/*"]}, {"js": ["import_theSearchItem.js"], "matches": ["<all_urls>"]}, {"js": ["import_searchWithAI.js"], "matches": ["https://www.google.com/*", "https://www.baidu.com/*", "https://cn.bing.com/*", "https://www.bing.com/*", "https://www.sogou.com/*", "https://duckduckgo.com/*", "https://search.yahoo.com/search*", "https://search.naver.com/*", "https://yandex.com/*", "https://wap.yandex.com/*", "https://search.brave.com/*", "https://www.reddit.com/*", "https://twitter.com/*", "https://www.youtube.com/*", "https://www.amazon.com/s/*"], "run_at": "document_end"}, {"js": ["import_requesterInject.js"], "matches": ["<all_urls>"]}, {"all_frames": true, "js": ["import_contentArkoseTokenIframe.js"], "match_about_blank": true, "matches": ["https://chatgpt.com/favicon.ico"], "run_at": "document_start"}], "current_locale": "zh_CN", "declarative_net_request": {"rule_resources": [{"enabled": true, "id": "ruleset_bing", "path": "rules/bing.json"}]}, "default_locale": "en", "description": "通过网络浏览增强您的ChatGPT提示与相关的网络搜索结果。", "host_permissions": ["*://*/*", "<all_urls>"], "icons": {"128": "assets/icons/icon128.png", "16": "assets/icons/icon16.png", "48": "assets/icons/icon48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA12wxe+ZXDlzpUxkpbsUEn1iWl7eSYNgZKw9xzuQ+thzEPfi17OoZeaXgi1HgOfqUsZFe2+vDb9EW6zuUEaI34D3+Y7lIMI2rvBfi1ptU+fOHXfQiCVQXTTZxRYRE+mxS0KgLw3daiu6t3YDfE/E9N7viyxCys7EE3+BoK3hfC0/BjJEmCPY4WHf8b52wK+1tjrJckgYBFcHYLL+CennmVL6JJn4dB3T4MaNNQupa9pDaAoy7eaaxUDdIJofoHRbK8ez9ST0IPx9QOOTHSMsRRvYWo0vbCoOJNdrkkvivJ/CV4ESCxmORSUCt1FwMdJ9+JlZP1xh2RgxMg59IZxtDMQIDAQAB", "manifest_version": 3, "name": "WebChatGPT: ChatGPT 具备互联网访问功能", "options_ui": {"open_in_tab": true, "page": "pages/options/options.html"}, "permissions": ["storage", "declarativeNetRequestWithHostAccess"], "short_name": "WebChatGPT", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "4.1.61", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["contentScripts/*", "chunks/*.js", "i18n/locales/*", "assets/*", "pages/*"]}]}, "path": "flahobhjikkpnpohomeckhdjjkkkkmoc\\4.1.61_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "gbihlnbpmfkodghomcinpblknjhneknc": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}}, "gbmoeijgfngecijpcnbooedokgafmmji": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}}, "gcinnojdebelpnodghnoicmcdmamjoch": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}}, "gecfnmoodchdkebjjffmdcmeghkflpib": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}}, "ghbmnnjooekpmoecnnnilnnbdlolhkhi": {"account_extension_type": 0, "ack_external": true, "active_permissions": {"api": ["alarms", "storage", "unlimitedStorage", "offscreen"], "explicit_host": ["https://docs.google.com/*", "https://drive.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1048713, "disable_reasons": [*********], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": true, "granted_permissions": {"api": ["alarms", "storage", "unlimitedStorage", "offscreen"], "explicit_host": ["https://docs.google.com/*", "https://drive.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 6, "manifest": {"author": {"email": "<EMAIL>"}, "background": {"service_worker": "service_worker_bin_prod.js"}, "content_capabilities": {"matches": ["https://docs.google.com/*", "https://drive.google.com/*", "https://drive-autopush.corp.google.com/*", "https://drive-daily-0.corp.google.com/*", "https://drive-daily-1.corp.google.com/*", "https://drive-daily-2.corp.google.com/*", "https://drive-daily-3.corp.google.com/*", "https://drive-daily-4.corp.google.com/*", "https://drive-daily-5.corp.google.com/*", "https://drive-daily-6.corp.google.com/*", "https://drive-preprod.corp.google.com/*", "https://drive-staging.corp.google.com/*"], "permissions": ["clipboardRead", "clipboardWrite", "unlimitedStorage"]}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "current_locale": "zh_CN", "default_locale": "en_US", "description": "编辑、创建和查看文档、电子表格和演示文稿，无需连接互联网。", "externally_connectable": {"matches": ["https://docs.google.com/*", "https://drive.google.com/*", "https://drive-autopush.corp.google.com/*", "https://drive-daily-0.corp.google.com/*", "https://drive-daily-1.corp.google.com/*", "https://drive-daily-2.corp.google.com/*", "https://drive-daily-3.corp.google.com/*", "https://drive-daily-4.corp.google.com/*", "https://drive-daily-5.corp.google.com/*", "https://drive-daily-6.corp.google.com/*", "https://drive-preprod.corp.google.com/*", "https://drive-staging.corp.google.com/*"]}, "host_permissions": ["https://docs.google.com/*", "https://drive.google.com/*"], "icons": {"128": "128.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnF7RGLAxIon0/XeNZ4MLdP3DMkoORzEAKVg0sb89JpA/W2osTHr91Wqwdc9lW0mFcSpCYS9Y3e7cUMFo/M2ETASIuZncMiUzX2/0rrWtGQ3UuEj3KSe5PdaVZfisyJw/FebvHwirEWrhqcgzVUj9fL9YjE0G45d1zMKcc1umKvLqPyTznNuKBZ9GJREdGLRJCBmUgCkI8iwtwC+QZTUppmaD50/ksnEUXv+QkgGN07/KoNA5oAgo49Jf1XBoMv4QXtVZQlBYZl84zAsI82hb63a6Gu29U/4qMWDdI7+3Ne5TRvo6Zi3EI4M2NQNplJhik105qrz+eTLJJxvf4slrWwIDAQAB", "manifest_version": 3, "minimum_chrome_version": "88", "name": "Google 文档的离线功能", "permissions": ["alarms", "storage", "unlimitedStorage", "offscreen"], "storage": {"managed_schema": "dasherSettingSchema.json"}, "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.94.1", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["page_embed_script.js"]}]}, "path": "ghbmnnjooekpmoecnnnilnnbdlolhkhi\\1.94.1_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": true, "was_installed_by_oem": false, "withholding_permissions": false}, "hfmgbegjielnmfghmoohgmplnpeehike": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}}, "iglcjdemknebjbklcgkfaebgojjphkec": {"account_extension_type": 0, "active_permissions": {"api": ["identity", "management", "metricsPrivate", "webstorePrivate", "hubPrivate"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "w", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://microsoftedge.microsoft.com"}, "urls": ["https://microsoftedge.microsoft.com"]}, "description": "发现 Microsoft Edge 扩展。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtMvN4+y6cd3el/A/NT5eUnrz1WiD1WJRaJfMBvaMtJHIuFGEmYdYL/YuE74J19+pwhjOHeFZ3XUSMTdOa5moOaXXvdMr5wWaaN2frHewtAnNDO64NGbbZvdsfGm/kRkHKVGNV6dacZsAkylcz5CkwTmq97wOZ7ETaShHvhZEGwRQIt4K1poxurOkDYQw9ERZNf3fgYJ9ZTrLZMAFDLJY+uSF03pClWrr8VGc8LUQ4Naktb8QSgVUlrS14AdF/ESdbhnTvvdB0e7peNWRyoNtCqLJsbtTtBL6sOnqfusnwPowuueOFI+XskOT9TvLo6PcgxhLX5+d0mM+Jtn6PFTU8QIDAQAB", "name": "Microsoft Store", "permissions": ["webstorePrivate", "management", "metricsPrivate", "identity", "hubPrivate"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\microsoft_web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ihmafllikibpmigkcoadcmckbfhibefp": {"account_extension_type": 0, "active_permissions": {"api": ["debugger", "feedbackPrivate", "fileSystem", "fileSystem.write", "app.window.fullscreen", "metricsPrivate", "storage", "tabs", "fileSystem.readFullPath", "edgeInternetConnectivityPrivate"], "explicit_host": ["edge://resources/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["edgeFeedbackPrivate.onFeedbackRequested"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"background": {"scripts": ["js/event_handler.js"]}, "content_security_policy": "default-src 'none'; script-src 'self' blob: filesystem: chrome://resources; style-src 'unsafe-inline' blob: chrome: file: filesystem: data: *; img-src * blob: chrome: file: filesystem: data:; media-src 'self' blob: filesystem:; connect-src data:"}, "description": "User feedback extension", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon128.png", "16": "images/icon16.png", "192": "images/icon192.png", "32": "images/icon32.png", "48": "images/icon48.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAl3vxWwvLjcMIFK4OfG6C8PmJkMhFYDKRnx+SqG23YlMG1A+bOkiNmAN1TWpFPPp1f2PpbiZGNq1y29u/QfkD+PC4bnO7GbNw/2X5tGoP0n2K+KGGAxhnr0ki/oyo2eiFGSTOXlQvTRo5q1vB+Lbg+9TbFsWKlHZyAkeZ/YGz/iijHTqw8Q4RWdl5Tp8SlUhS/92EsWhveNJLW22veaT/Up2iSeSSwfyoHVYy8LUPaD4fbyLvPQacVLJq1dac2bNDqjaNvSPgPWCnkZtDmawZrgxT53otLCES/e96xfAf8I24VHIc1pVP8LqdqKr1AV1Yxn93h3VJ2QejtEhIAWHU6QIDAQAB", "manifest_version": 2, "name": "<PERSON>", "permissions": ["chrome://resources/", "debugger", "edgeInternetConnectivityPrivate", "feedbackPrivate", {"fileSystem": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "write"]}, "fullscreen", "metricsPrivate", "storage", "windows"], "version": "*******"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\edge_feedback", "preferences": {}, "regular_only_preferences": {}, "running": false, "was_installed_by_default": false, "was_installed_by_oem": false}, "iikmkjmpaadaobahmlepeloendndfphd": {"account_extension_type": 2, "active_permissions": {"api": ["alarms", "clipboardWrite", "contextMenus", "cookies", "idle", "notifications", "storage", "tabs", "unlimitedStorage", "webNavigation", "webRequest", "scripting", "declarativeNetRequestWithHostAccess", "offscreen", "userScripts"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "commands": {"open-dashboard": {"suggested_key": ""}, "open-dashboard-with-running-scripts": {"suggested_key": ""}, "open-new-script": {"suggested_key": ""}, "toggle-enable": {"suggested_key": ""}}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["alarms", "clipboardWrite", "contextMenus", "cookies", "idle", "notifications", "storage", "tabs", "unlimitedStorage", "webNavigation", "webRequest", "scripting", "declarativeNetRequestWithHostAccess", "offscreen", "userScripts"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"16": "images/icon_grey16.png", "19": "images/icon_grey19.png", "24": "images/icon_grey24.png", "32": "images/icon_grey32.png", "38": "images/icon_grey38.png"}, "default_popup": "action.html", "default_title": "Tam<PERSON>mon<PERSON>"}, "background": {"service_worker": "background.js"}, "commands": {"open-dashboard": {"description": "Open dashboard"}, "open-dashboard-with-running-scripts": {"description": "Open dashboard with the current tab's URL used as filter"}, "open-new-script": {"description": "Open new script tab"}, "toggle-enable": {"description": "Toggle enable state"}}, "current_locale": "zh_CN", "default_locale": "en", "description": "使用用户脚本自由地改变网络", "host_permissions": ["<all_urls>"], "icons": {"128": "images/icon128.png", "32": "images/icon.png", "48": "images/icon48.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0P8sw/BsnXXss5WQ3BbkcHZ5br0AEJ8Ex267fYl5FuComXndOeqdPJFJvMEE6CDICfC1S6toTAEg+PDVIce68zfQarVpPb5hCVwbAbQCHwWK3pzLY/3CZZnhfIcL3Mh6FHPUxz4MyUEgN69wd/CBKb7LTv+mZVjjvPNgRafpnYc/0Dkplr0CxwUjZSNT+qO1h91Im5m9rLmVkCDUUG/P/EEYT6jf4PB17n29gTvpM80xbaj/dU+a0fTpDOKfw/ARXCYRBi67YnQJa9FZlIrtjPwSeTRR/C8xpXA+w03trmk8AnJbSYLWDGarMmZCUGKPT4fPPxtxxDtNK51tgCnZOwIDAQAB", "manifest_version": 3, "minimum_chrome_version": "120", "name": "篡改猴", "offline_enabled": true, "optional_permissions": ["downloads"], "options_page": "options.html", "options_ui": {"open_in_tab": true, "page": "options.html"}, "permissions": ["notifications", "unlimitedStorage", "tabs", "idle", "webNavigation", "webRequest", "webRequestBlocking", "storage", "contextMenus", "chrome://favicon/", "clipboardWrite", "cookies", "alarms", "declarativeNetRequestWithHostAccess", "scripting", "userScripts", "offscreen"], "short_name": "篡改猴", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "5.3.3"}, "path": "iikmkjmpaadaobahmlepeloendndfphd\\5.3.3_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "jbleckejnaboogigodiafflhkajdmpcl": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}}, "jdiccldimpdaibmpdkjnbmckianbfold": {"account_extension_type": 0, "active_permissions": {"api": ["activeTab", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "errorReporting"], "explicit_host": ["https://*.bing.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["lifetimeHelper.js", "telemetryHelper.js", "errorHelper.js", "voiceList/voiceListRequester.js", "voiceList/voiceListSingleton.js", "voiceList/voiceModel.js", "manifestHelper.js", "config.js", "ssml.js", "uuid.js", "wordBoundary.js", "audioStreamer.js", "wordBoundaryEventManager.js", "audioViewModel.js", "background.js"]}, "description": "Provides access to Microsoft's online text-to-speech voices", "key": "AAAAB3NzaC1yc2EAAAADAQABAAAAgQDjGOAV6/3fmEtQmFqlmqm5cZ+jlNhd6XikwMDp0I7BKh+AjG3aBIG/qqwlsF/7LAGatnSxBwUwZC0qMnGXtcOPVl26Q8OvMx0gt5Va5gxca+ae0Skluj9WN9TNxPFVhw21WbCt4D9q3kb+XXDlx/7v1ktYus4Fwr/skkjADG9cIQ==", "manifest_version": 2, "name": "Microsoft Voices", "permissions": ["activeTab", "errorReporting", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "https://*.bing.com/"], "tts_engine": {"voices": [{"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)", "voice_name": "Microsoft Aria Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, GuyNeural)", "voice_name": "Microsoft Guy Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, XiaoxiaoNeural)", "voice_name": "Microsoft Xiaoxiao Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunyangNeural)", "voice_name": "Microsoft Yunyang Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-TW", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-TW, HanHanRUS)", "voice_name": "Microsoft HanHan Online - Chinese (Taiwan)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-HK", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-HK, TracyRUS)", "voice_name": "Microsoft Tracy Online - Chinese (Hong Kong)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ja-<PERSON>", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, NanamiNeural)", "voice_name": "Microsoft Nanami Online (Natural) - Japanese (Japan)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-GB", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-GB, LibbyNeural)", "voice_name": "Microsoft Libby Online (Natural) - English (United Kingdom)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pt-BR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pt-BR, FranciscaNeural)", "voice_name": "Microsoft Francisca Online (Natural) - Portuguese (Brazil)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-MX", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-MX, DaliaNeural)", "voice_name": "Microsoft Dalia Online (Natural) - Spanish (Mexico)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-IN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-IN, PriyaRUS)", "voice_name": "Microsoft Priya Online - English (India)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-CA, HeatherRUS)", "voice_name": "Microsoft Heather Online - English (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-CA, SylvieNeural)", "voice_name": "Microsoft Sylvie Online (Natural) - French (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-FR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, <PERSON>)", "voice_name": "Microsoft Denise Online (Natural) - French (France)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "de-DE", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (de-DE, KatjaNeural)", "voice_name": "Microsoft Katja Online (Natural) - German (Germany)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ru-RU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ru-RU, EkaterinaRUS)", "voice_name": "Microsoft Ekaterina Online - Russian (Russia)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-AU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-AU, HayleyRUS)", "voice_name": "Microsoft Hayley Online - English (Australia)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "it-IT", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (it-IT, ElsaNeural)", "voice_name": "Microsoft Elsa Online (Natural) - Italian (Italy)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ko-KR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ko-KR, SunHiNeural)", "voice_name": "Microsoft SunHi Online (Natural) - Korean (Korea)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "nl-NL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (nl-NL, HannaRUS)", "voice_name": "Microsoft Hanna Online - Dutch (Netherlands)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-ES", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-ES, ElviraNeural)", "voice_name": "Microsoft Elvira Online (Natural) - Spanish (Spain)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "tr-TR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (tr-TR, EmelNeural)", "voice_name": "Microsoft Emel Online (Natural) - Turkish (Turkey)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pl-PL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pl-PL, PaulinaRUS)", "voice_name": "Microsoft Paulina Online - Polish (Poland)"}]}, "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\microsoft_voices", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "jmjflgjpcpepeafmmgdpfkogkghcpiha": {"account_extension_type": 0, "ack_external": true, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"]}, "commands": {}, "content_settings": [], "creation_flags": 8321, "disable_reasons": [], "edge_last_update_check_time": "*****************", "events": [], "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 10, "manifest": {"content_scripts": [{"js": ["content.js"], "matches": ["https://chrome.google.com/webstore/*"]}, {"js": ["content_new.js"], "matches": ["https://chromewebstore.google.com/*"]}], "description": "Edge relevant text changes on select websites to improve user experience and precisely surfaces the action they want to take.", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu06p2Mjoy6yJDUUjCe8Hnqvtmjll73XqcbylxFZZWe+MCEAEK+1D0Nxrp0+IuWJL02CU3jbuR5KrJYoezA36M1oSGY5lIF/9NhXWEx5GrosxcBjxqEsdWv/eDoOOEbIvIO0ziMv7T1SUnmAA07wwq8DXWYuwlkZU/PA0Mxx0aNZ5+QyMfYqRmMpwxkwPG8gyU7kmacxgCY1v7PmmZo1vSIEOBYrxl064w5Q6s/dpalSJM9qeRnvRMLsszGY/J2bjQ1F0O2JfIlBjCOUg/89+U8ZJ1mObOFrKO4um8QnenXtH0WGmsvb5qBNrvbWNPuFgr2+w5JYlpSQ+O8zUCb8QZwIDAQAB", "manifest_version": 3, "name": "Edge relevant text changes", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "1.2.1"}, "path": "jmjflgjpcpepeafmmgdpfkogkghcpiha\\1.2.1_0", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": true, "was_installed_by_oem": false}, "kdlmggoacmfoombiokflpeompajfljga": {"account_extension_type": 2, "active_permissions": {"api": ["storage", "unlimitedStorage", "declarativeNetRequestWithHostAccess", "sidePanel"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "commands": {"open-app": {"suggested_key": "Alt+J"}}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1, 2], "dnr_static_ruleset": {"1": {"checksum": *********, "ignore_ruleset": false}, "2": {"checksum": *********, "ignore_ruleset": false}, "3": {"checksum": *********, "ignore_ruleset": false}, "4": {"checksum": **********, "ignore_ruleset": false}, "5": {"checksum": *********, "ignore_ruleset": false}, "6": {"checksum": **********, "ignore_ruleset": false}, "7": {"checksum": *********, "ignore_ruleset": false}, "8": {"checksum": **********, "ignore_ruleset": false}}, "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {}, "background": {"service_worker": "service-worker-loader.js", "type": "module"}, "commands": {"open-app": {"description": "Open ChatHub app", "suggested_key": {"default": "Alt+J", "linux": "Alt+J", "mac": "Command+J", "windows": "Alt+J"}}}, "current_locale": "zh_CN", "declarative_net_request": {"rule_resources": [{"enabled": true, "id": "ruleset_x_frame_options", "path": "src/rules/x-frame-options.json"}, {"enabled": true, "id": "ruleset_chatgpt", "path": "src/rules/chatgpt.json"}, {"enabled": true, "id": "ruleset_bing", "path": "src/rules/bing.json"}, {"enabled": true, "id": "ruleset_ddg", "path": "src/rules/ddg.json"}, {"enabled": true, "id": "ruleset_qianwen", "path": "src/rules/qianwen.json"}, {"enabled": true, "id": "ruleset_ollama", "path": "src/rules/ollama.json"}, {"enabled": true, "id": "ruleset_pplx", "path": "src/rules/pplx.json"}, {"enabled": true, "id": "ruleset_anthropic", "path": "src/rules/anthropic.json"}]}, "default_locale": "en", "description": "同时使用<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>和更多机器人", "host_permissions": ["<all_urls>"], "icons": {"128": "src/assets/icon.png", "16": "src/assets/icon.png", "32": "src/assets/icon.png", "48": "src/assets/icon.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAos6UBxsKjXHiMNnRNz7xV12cCJWbFq5qA5r1GBHSmyOECbb2/cNqCOAvsin/KLqW6A6jiFcckJR4lqM97Z5pXIYy/Bh9/cEpF4+g1lHK0dzW717B6ba2cS3gIq4NDNg1wGPXI/9SqrmHGFYPBuEH/m384fqrtcFE888v1pY9zHb7mMGL3O/axQPHda/ntGcHHBVSkwKk0FDlmTRYWtTp8zTO2mvMV0EXCnqwLZVHzdlutjMTjG1AZYXfSUwxTWQs4ljpSpJCbV5PgFvSczKynUgsyt5br+2qSdxg6XM5h1ypL6i6Yr/zttuL1SKhfy7Yw2qwgkevrOs0ApNoBbTAOQIDAQAB", "manifest_version": 3, "name": "ChatHub - GPT-4, <PERSON>, <PERSON>同时用", "permissions": ["storage", "unlimitedStorage", "sidePanel", "declarativeNetRequestWithHostAccess"], "side_panel": {"default_path": "sidepanel.html"}, "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "3.90.0"}, "path": "kdlmggoacmfoombiokflpeompajfljga\\3.90.0_1", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "kfihiegbjaloebkmglnjnljoljgkkchm": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}}, "kmhegedbanhfblnoboomoeafpmojfdlp": {"account_extension_type": 2, "active_permissions": {"api": ["clipboardWrite", "contextMenus", "storage", "tabs", "scripting"], "explicit_host": ["*://*/*"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://*.getliner.com/pdf/checksum/*", "https://getliner.com/pdf/checksum/*", "https://www.youtube-nocookie.com/embed/*", "https://www.youtube.com/embed/*", "https://www.youtube.com/watch*"]}, "commands": {"toggle-side-panel": {"suggested_key": "Ctrl+I", "was_assigned": true}}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["clipboardWrite", "contextMenus", "storage", "tabs", "scripting"], "explicit_host": ["*://*/*"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://*.getliner.com/pdf/checksum/*", "https://getliner.com/pdf/checksum/*", "https://www.youtube-nocookie.com/embed/*", "https://www.youtube.com/embed/*", "https://www.youtube.com/watch*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"128": "/images/icon/icon-saved-128.png", "16": "/images/icon/icon-saved-16.png", "32": "/images/icon/icon-saved-32.png", "48": "/images/icon/icon-saved-48.png"}, "default_title": "Save to Liner"}, "background": {"service_worker": "backgrounds.js"}, "commands": {"toggle-side-panel": {"description": "Open/Close Copilot side panel", "suggested_key": "Ctrl+I"}}, "content_scripts": [{"exclude_matches": ["https://www.youtube.com/watch*"], "js": ["/liner-core.be.js"], "matches": ["<all_urls>"], "run_at": "document_start"}, {"all_frames": true, "exclude_matches": ["https://www.youtube.com/embed/?*", "https://www.youtube.com/embed?*"], "js": ["/liner-core.be.js"], "matches": ["https://www.youtube.com/watch*", "https://www.youtube-nocookie.com/embed/*", "https://www.youtube.com/embed/*"], "run_at": "document_start"}, {"css": ["/pdfCSS.css"], "matches": ["https://getliner.com/pdf/checksum/*", "https://*.getliner.com/pdf/checksum/*"]}], "current_locale": "zh_CN", "default_locale": "en", "description": "直接在页面上使用 ChatGPT，甚至在 YouTube 上！添加 AI Copilot，与您的个人 AI 助手/AI 伴侣一起完成更多工作。", "homepage_url": "https://getliner.com", "host_permissions": ["*://*/*"], "icons": {"128": "/images/icon/icon-saved-128.png", "16": "/images/icon/icon-saved-16.png", "32": "/images/icon/icon-saved-32.png", "48": "/images/icon/icon-saved-48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz1nnDPRjJksERHG40ojyWnMsgQ4XLU+0w8DxxArsYgVKGni9ZPZbpa1QnxAbAuGGxWLmR+mhnbFjvhrCo3nwL8eVbIfMpFJIR3GiPG+fIORby0i3KA7gC9dboRkJy4hZJ9sxcbhmFLIMiOG/1fU/Hq8Y+LPNXDg9FXVkpQLm5yujUFC/Jmv5jPGBtfpdkhI/300GU14HZ6ldNG5+YBgxq32wD7nFGWFujzTpb91Ep4hoKdpuso00oMSpphxpgQrswlezri1htyPjxVnGEzq8TuR+3HL9UXL1YDqRLUY8W49dEeeMjQuswpUUA6IgFADbbtwTa7SlYXNkJOhs5TOLKwIDAQAB", "manifest_version": 3, "name": "Liner ChatGPT：Web&YouTube的AI副驾驶", "options_page": "/options/options.html", "permissions": ["tabs", "clipboardWrite", "storage", "scripting", "contextMenus"], "short_name": "Liner", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "7.17.1", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["/fonts/ABCArizonaFlare-Regular.woff2"]}]}, "path": "kmhegedbanhfblnoboomoeafpmojfdlp\\7.17.1_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "liilgpjgabokdklappibcjfablkpcekh": {"account_extension_type": 2, "active_permissions": {"api": ["activeTab", "alarms", "clipboardWrite", "contextMenus", "cookies", "downloads", "notifications", "storage", "tabs", "unlimitedStorage", "webRequest", "declarativeNetRequest", "scripting", "offscreen", "userScripts"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["activeTab", "alarms", "clipboardWrite", "contextMenus", "cookies", "downloads", "notifications", "storage", "tabs", "unlimitedStorage", "webRequest", "declarativeNetRequest", "scripting", "offscreen", "userScripts"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"128": "assets/logo.png"}, "default_popup": "src/popup.html"}, "author": "CodFrm", "background": {"service_worker": "src/service_worker.js"}, "current_locale": "zh_CN", "default_locale": "en", "description": "万物皆可脚本化，让你的浏览器可以做更多的事情！", "host_permissions": ["<all_urls>"], "icons": {"128": "assets/logo.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtsWIPxfRpoykop0oqVrpcqPyV/6j0GwCQ6v6LJxgSd3z8wsix9PgpvkRc4aiU0Bjp2gR/epYCcd+HZ0BzCSmr03B4XgO0QW4Mt505VY0ZuhqIjnXWGFFkgLRij0Z/kb3l42a9eW8agDJ8AOxz4AI0w6vJ5qyEaf+2XLc0vgghCB6rFxU/uR1PrDNXyO05WltumuP2Xh0Mmp/BlFDr3kUqVU7+u+yxT4zErcFrYkCVuSqlXh2XzEB0QpBqk/RFRS5tENJv2drJcsBySv4QENeGP0lyYG1kYmiOzuNiFkkNfdiQBaJk3r+1i5MJM8cSE+N3ufTJabXAq6D8xp6EnDetQIDAQAB", "manifest_version": 3, "name": "脚本猫", "options_ui": {"open_in_tab": true, "page": "src/options.html"}, "permissions": ["tabs", "alarms", "storage", "cookies", "offscreen", "scripting", "downloads", "activeTab", "webRequest", "userScripts", "contextMenus", "notifications", "clipboardWrite", "unlimitedStorage", "declarativeNetRequest"], "sandbox": {"pages": ["src/sandbox.html"]}, "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "0.18.2"}, "path": "liilgpjgabokdklappibcjfablkpcekh\\0.18.2_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "ljlelhlcghfgkhckkmplmbgfaajlmemo": {"account_extension_type": 2, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": ["*://chaoshi.detail.tmall.com/*", "*://detail.tmall.com/*", "*://detail.tmall.hk/*", "*://e.jd.com/*", "*://i-item.jd.com/*", "*://item.jd.com/*", "*://item.taobao.com/*", "*://npcitem.jd.hk/*"]}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": ["*://chaoshi.detail.tmall.com/*", "*://detail.tmall.com/*", "*://detail.tmall.hk/*", "*://e.jd.com/*", "*://i-item.jd.com/*", "*://item.jd.com/*", "*://item.taobao.com/*", "*://npcitem.jd.hk/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"background": {"page": "background.html", "persistent": true}, "browser_action": {"default_icon": "128.png", "default_title": "券多多"}, "content_scripts": [{"css": ["main.css"], "js": ["jquery.bundle.js", "script.bundle.js"], "matches": ["*://item.jd.com/*", "*://i-item.jd.com/*", "*://e.jd.com/*", "*://npcitem.jd.hk/*", "*://item.taobao.com/*", "*://chaoshi.detail.tmall.com/*", "*://detail.tmall.com/*", "*://detail.tmall.hk/*"]}], "content_security_policy": "script-src 'self' 'unsafe-eval'; object-src 'self'", "description": "当您在京东或者淘宝浏览商品详情页时，为您获取该商品的优惠券，领取优惠券后下单立减。", "icons": {"128": "128.png", "16": "16.png", "48": "48.png", "96": "96.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyJoVNnrsOI5rxOj1zhB2RZI5KWiIHQ7fjIRI27RRAL+7dB6uEQypmYnPuw2Rf6EcSaM9CGi6D63rDYbZdXsjt4ZwKBlehHHjzjtqvPhDuf1wZ0p7QdNtQ/37lsai2EnhrmD+1RyNRq4ntzHt53jsvm0Tj27542eXcc89CTFEmdtwzlEvPMvDKaYaKFNHqxHs1UXUY/BgRC8x0Li3Ak3PuCxlw727xzPuQllMOKjoxhJ9WkWg/oOymrAXquihy7U+RwkMRVI01VxpWOU4VrkTIDY4EOiAaoNDFDD3PKWnPs7Bs5IOuv2BZ14vtuXOyCTfz4NZe8fccY2VbLC8FeDFhwIDAQAB", "manifest_version": 2, "name": "券多多", "options_page": "options.html", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "0.0.3", "web_accessible_resources": ["img/*"]}, "path": "ljlelhlcghfgkhckkmplmbgfaajlmemo\\0.0.3_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ncbjelpjchkpbikbpkcchkhkblodoama": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.teams.microsoft.com/*", "https://*.skype.com/*", "https://*.teams.live.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtAdFAR3ckd5c7G8VSzUj4Ltt/QRInUOD00StG95LweksGcLBlFlYL46cHFVgHHj1gmzcpBtgsURdcrAC3V8yiE7GY4wtpOP+9l+adUGR+cyOG0mw9fLjyH+2Il0QqktsNXzkNiE1ogW4l0h4+PJc262j0vtm4hBzMvR0QScFWcAIcAErlUiWTt4jefXCAYqubV99ed5MvVMWBxe97wOa9hYwAhbCminOepA4RRTg9eyi0TiuHpq/bNI8C5qZgKIQNBAjgiFBaIx9hiMBFlK4NHUbFdgY6Qp/hSCMNurctwz1jpsXEnT4eHg1YWXfquoH8s4swIjkFCMBF6Ejc3cUkQIDAQAB", "manifest_version": 2, "name": "WebRTC Internals Extension", "permissions": ["webrtcInternalsPrivate"], "version": "2.0.2"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\webrtc_internals", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ngphehpfehdmjellohmlojkplilekadg": {"account_extension_type": 2, "active_permissions": {"api": ["clipboardRead", "clipboardWrite", "contextMenus", "management", "storage", "tabs", "scripting", "declarativeNetRequestWithHostAccess"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://docs.google.com/*", "https://www.maxai.co/*"]}, "commands": {"_execute_action": {"was_assigned": true}, "open-immersive-chat": {"suggested_key": "Alt+I", "was_assigned": true}, "toggle-page-translator": {"suggested_key": "Alt+A", "was_assigned": true}}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["clipboardRead", "clipboardWrite", "contextMenus", "management", "storage", "tabs", "scripting", "declarativeNetRequestWithHostAccess"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://docs.google.com/*", "https://www.maxai.co/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"16": "assets/USE_CHAT_GPT_AI/icons/maxai_16_normal.png", "24": "assets/USE_CHAT_GPT_AI/icons/maxai_32_normal.png", "32": "assets/USE_CHAT_GPT_AI/icons/maxai_32_normal.png"}, "default_popup": "pages/popup/index.html", "default_title": "MaxAI"}, "background": {"service_worker": "background.js", "type": "module"}, "commands": {"_execute_action": {"description": "Active MaxAI", "suggested_key": {"default": "Alt+J", "linux": "Alt+J", "mac": "Command+J", "windows": "Alt+J"}}, "open-immersive-chat": {"description": "Open immersive chat", "suggested_key": {"default": "Alt+I", "linux": "Alt+I", "mac": "Command+I", "windows": "Alt+I"}}, "toggle-page-translator": {"description": "Translate webpage", "suggested_key": {"default": "Alt+A"}}}, "content_scripts": [{"js": ["import_content.js"], "matches": ["<all_urls>"], "run_at": "document_end"}, {"js": ["import_apps_content-scripts_checkMaxAIStatus.js"], "matches": ["https://www.maxai.co/*"], "run_at": "document_end"}, {"all_frames": false, "js": ["import_apps_content-scripts_injectDocumentStart.js"], "matches": ["<all_urls>"], "run_at": "document_start"}, {"all_frames": false, "js": ["apps/content-scripts/website/googleDoc.js"], "matches": ["https://docs.google.com/*"], "run_at": "document_start", "world": "MAIN"}, {"all_frames": true, "js": ["import_apps_content-scripts_iframeDocumentEnd.js"], "match_about_blank": true, "matches": ["<all_urls>"], "run_at": "document_end"}], "current_locale": "zh_CN", "default_locale": "en", "description": "使用您的AI助手节省时间，帮助您在任何在线工作中更快地阅读、写作和搜索。", "host_permissions": ["<all_urls>"], "icons": {"128": "assets/USE_CHAT_GPT_AI/icons/maxai_128_normal.png", "16": "assets/USE_CHAT_GPT_AI/icons/maxai_16_normal.png", "32": "assets/USE_CHAT_GPT_AI/icons/maxai_32_normal.png", "48": "assets/USE_CHAT_GPT_AI/icons/maxai_48_normal.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA+LyrkUJYDX2WMxmrHSc7nZCpl2jxZ3br6+WAIwOawHZrvRyab7H4hwcC9frM1ZKC/C8fK+kJd7J1NO5/PfB/9d6E55YCXb7jbTAYYA/K3r62koT7eFONbsyIs7QwnOv5lkXX4vwBd8CJH0dCPCntH9XENoZlzpq7j8uvZfq7vx+iboHZBodaHU5FJTwb88PmC3PJrEGAuy5NOWVGI2kylH4gIq1BEUlCE/ZfsYfqRNDhnl57oIuAlakwtwZcGjZt7PZ/Bl99Smk4sr9YGH4w+xVWtkG0OmepeYE4EVeKGpOVYcDfqWpVxuMAgGq6cQ4h28i2aWFKQ7/TOmVzqO0JAwIDAQAB", "manifest_version": 3, "name": "MaxAI：在浏览时询问AI任何问题", "options_ui": {"open_in_tab": true, "page": "pages/settings/index.html"}, "permissions": ["tabs", "scripting", "storage", "management", "contextMenus", "clipboardRead", "clipboardWrite", "declarativeNetRequestWithHostAccess"], "short_name": "MaxAI", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "8.26.2", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["worker.js", "i18n/locales/*", "pages/pdf/*", "content.css", "content_style.css", "chunks/*.js", "assets/*", "pages/*", "apps/*", "content.js", "apps/content-scripts/checkMaxAIStatus.js", "apps/content-scripts/injectDocumentStart.js", "apps/content-scripts/iframeDocumentEnd.js"]}]}, "path": "ngphehpfehdmjellohmlojkplilekadg\\8.26.2_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "nkbndigcebkoaejohleckhekfmcecfja": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.teams.microsoft.com/*", "https://*.meet.teams.live.com/*", "https://*.meet.skype.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "WebRTC Extension", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ofefcgjbeghpigppfmkologfjadafddi": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}}, "opgbiafapkbbnbnjcdomjaghbckfkglc": {"account_extension_type": 2, "active_permissions": {"api": ["alarms", "contextMenus", "storage", "webRequest", "declarativeNetRequest", "scripting"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["alarms", "contextMenus", "storage", "webRequest", "declarativeNetRequest", "scripting"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"128": "images/icon_128.png", "16": "images/icon_16.png", "48": "images/icon_48.png"}, "default_popup": "src/popup_v1.html", "default_title": "<PERSON><PERSON><PERSON><PERSON>"}, "author": "modhader@", "background": {"service_worker": "serviceWorker.js", "type": "module"}, "content_scripts": [{"js": ["src/js/service/content_script_vite.js"], "matches": ["<all_urls>"]}], "current_locale": "zh_CN", "default_locale": "en", "description": "Modify HTTP request headers, response headers, and redirect URLs", "host_permissions": ["<all_urls>"], "icons": {"128": "images/icon_128.png", "16": "images/icon_16.png", "48": "images/icon_48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoapYsLRJ84zvyAhU0h638d97Ng0Ygy2r/KQStSVJ/ny0pevQDT57gWNMIBIebQQmIE/oScZUwUo/9TXhW9buaQwTqZwM0zKi6GFipGftRyVc/08DwZyxoTuNuhhU3a/4hBeHNIH87BSBQuBGVEHqVMwZTKay7gZonVodOfHHcaXwrmQ0+IDXL+l85xea6qO98KJCTKTFO31bsXejj14M2kj0x+fspnSeunpFznYE+Wv1aICMahXjjjWchQXt8ES9Ju87OesuwAh/QDSzmwu7jJm9gzx+Z6uBDkR/LsCVpiWKOJvs0Ms7qqQ+TpI8m7REa0Cn4tun4uXoqUZQdptd7wIDAQAB", "manifest_version": 3, "name": "ModHeader - Modify HTTP headers", "optional_permissions": ["contentSettings", "browsingData"], "options_ui": {"open_in_tab": true, "page": "src/app_v1.html"}, "permissions": ["alarms", "contextMenus", "storage", "webRequest", "declarativeNetRequest", "scripting"], "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "7.0.8", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["assets/AppVite-041b9295.css", "assets/AppVite-5ee08b2b.js", "assets/IconBtn-94c9cb85.js", "assets/_commonjsHelpers-187a63f9.js", "assets/browser-polyfill-c2a30efe.js", "assets/dayjs.min-db7fc211.js", "assets/isObjectLike-7962ce13.js", "assets/renderContent-9d534bdd.js", "assets/src/js/service/content_script_vite-4ac27ed4.js", "images/*"]}]}, "path": "opgbiafapkbbnbnjcdomjaghbckfkglc\\7.0.8_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "pcgaihjmjiakbnobkkbjdhfcchfnbgbj": {"disable_reasons": [1]}}}, "google": {"services": {"last_signed_in_username": "<EMAIL>"}}, "protection": {"macs": {"browser": {"show_home_button": "86A5E492FB843938B996426DA1BC6E716F9EEF2CC1FD77A5AA5B15F5F8F8A706"}, "default_search_provider_data": {"template_url_data": "DB27AFF8EFA17199FDB79097529F893C4CC6CE48DEDFE29EF1F4F8D9636EDB7D"}, "edge": {"services": {"account_id": "CA9EA4DA4F37708B820CEE215A90D29A9AE1CDECB242C856A43BDBA24A7F7D09", "last_username": "5DA5A85C95F3026A74277C223C00263DE0242AD12D9A140A648D31F0CD3F1278"}}, "enterprise_signin": {"policy_recovery_token": "14161CABF9FDD6DB5563D05D468B46F66E7E395114D67320B43EAF72CA2C6F39"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "D56A9C2D55E6D987838D8550EABEC5897F0C4596869E9AF00ABA5FB988804DAD", "bpelnogcookhocnaokfpoeinibimbeff": "10894F1C21AE31F7297A3DB0E63CE65CA72703018AB358D37E4B11522137DBCD", "cjneempfhkonkkbcmnfdibgobmhbagaj": "F054DB1EB4CCA9CFC84F1331AE3B239E9CCB72A64EEEC0D0BEE7D3E7899AACB6", "cnjkedgepfdpdbnepgmajmmjdjkjnifa": "DE4918D140F1728E580D9A73D008854614B9581488664D9F4ABB7AC42F1B6829", "dcaajljecejllikfgbhjdgeognacjkkp": "64FD3E3EC85C3B62F0AE85F39053F480B8C22C84A7590A24CBFB3435E9B69218", "dgbhmbogkcdheijkkdmfhodkamcaiheo": "94BB68B9ABD0E1D0DA985C0151BD745C10729298208EFE48244C2A7C80FCA12D", "dgiklkfkllikcanfonkcabmbdfmgleag": "***************************917BC94783357ACEE914C52BB0468B1AE274C", "dhoenijjpgpeimemopealfcbiecgceod": "FCDF487A7AE81CF03F04B0E981185121EF0201FBBACA76BB9A0B776C8F8EFDF7", "dimkapigjeiopninbepgaoobcnbjmgik": "DD842E30B0E03F7024801F6F21D2E62E513DC9305C1BA4DB6FD4F48294A6351C", "ehlmnljdoejdahfjdfobmpfancoibmig": "768ED433ECFAAF0A02920012536FD855B225BB5B131C30F4CAA8B8F7C6DFBAC3", "epdpgaljfdjmcemiaplofbiholoaepem": "FED54DF19FE4F51749255FDC7C3813CDAA46C449A9450BE2C9AB4AE46A6EFD4D", "fhkhedgjlkcgdaldodbhmcldjglpabmf": "3C840564F0174CDE7A0018DDD339AE75E375D705119A27A87B54F10D9A43549B", "fikbjbembnmfhppjfnmfkahdhfohhjmg": "A87B1E093E18C42188121727E0D588DE3FD217BFB5D2548139A3B102B6251AE4", "fjngpfnaikknjdhkckmncgicobbkcnle": "B00462280CBEB255ED353B7DF73EB6A7624659B390F53639E5EEC1160D728CFA", "flahobhjikkpnpohomeckhdjjkkkkmoc": "4A15C2D387C6E3DB6DDF04EA91369D7E79B644330B2EDA5E345B8A5576F06D34", "gbihlnbpmfkodghomcinpblknjhneknc": "ACD56D4ED70531D6CA1AE4CE8508D9F8AE8D34B89C2D9DA2F492BC2CC640BC0B", "gbmoeijgfngecijpcnbooedokgafmmji": "1C5E7D3D87E2F77BFBFA9FD910BF57F2D6F1AE2CCAABD429BB3D9CC9B311E038", "gcinnojdebelpnodghnoicmcdmamjoch": "1A0F536A77AF89217CC533B12607D6F012CE3FD2677553AE1D97D170368CF1E7", "gecfnmoodchdkebjjffmdcmeghkflpib": "533BF45E1FC2578E44B52E6817CE7D2E0722C0F9B2B504EB4E2B9EA6F41B6BE7", "ghbmnnjooekpmoecnnnilnnbdlolhkhi": "2AE81F0685FD6CD33877717940B76627626095BA0A407648F32115EFE94756D9", "hfmgbegjielnmfghmoohgmplnpeehike": "35A6B039B3F46478079F8B6540587A843078E0DE931E3C421F0D0704F7366EF6", "iglcjdemknebjbklcgkfaebgojjphkec": "EBEE64F423A7029D10AEC9900C12BA00AEB6DD82AF671C4AB7C5E3EDF2A9C30D", "ihmafllikibpmigkcoadcmckbfhibefp": "A9D0FC1DCABBEE805218E4041F3664D6648F4FBFB3974384AE8B15D235401514", "iikmkjmpaadaobahmlepeloendndfphd": "FC0DD2BFF1FBD9E788BD3B27B4E44E3169D751253690A55EE86E774A020ABD13", "jbleckejnaboogigodiafflhkajdmpcl": "CB171F7CA685CC561896B8AC2A9574209F0E49E8D269FAFE12D3934063B94E8C", "jdiccldimpdaibmpdkjnbmckianbfold": "66A9DC411875D570777A3E8A9834DBE4D769A453A97B1B0AC76C0AD33CE106D3", "jmjflgjpcpepeafmmgdpfkogkghcpiha": "5AEAF8658ADC4E5D9B37BAFFC7A5942E2A7E18A4544CD07067F17852130FD923", "kdlmggoacmfoombiokflpeompajfljga": "2DAE48DD48F83CFAD17C8FD586E8E70147CF887F6F4C79194643375F02BF9CDF", "kfihiegbjaloebkmglnjnljoljgkkchm": "AF3ACA3353CCA9348B5E3ED0EFA6764BFDD7617243EFD0811FC7A737E180E070", "kmhegedbanhfblnoboomoeafpmojfdlp": "3E58231038122AD3129DD50C1B77D2C42E3F4F068802BE7B4F2F5A6F8218479F", "liilgpjgabokdklappibcjfablkpcekh": "6DEB5EE5AB582BEF1A9B6570F5DFE03FAE81DF04CC54FCB9366FD592A81AB7E2", "ljlelhlcghfgkhckkmplmbgfaajlmemo": "BB4E68CA8F096AF81F7B7CEA82B076A53C44430E44B2C74D64DA942B8FFC369E", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "53CA5BD34DAEA677DED9A43475036B9920DF3A7B731F476B0A426B8726D6BDDE", "ncbjelpjchkpbikbpkcchkhkblodoama": "484F1130098A41B4889367B1D2F137AAA694850833EC0C2BB0FF8D7FB090846B", "ngphehpfehdmjellohmlojkplilekadg": "27CD995F6B4541F71CFCDD8B3CD7592DDBA7CC4EDF3E6648842388D0194C51EE", "nkbndigcebkoaejohleckhekfmcecfja": "197287616B30A11048D6E71ED7EF089DA219EBBF8A1BCDB553943F06074BE1CB", "nkeimhogjdpnpccoofpliimaahmaaome": "ED538E6EBA993BF0C71319E931ED40E1255EDA4E15F2A9F0676C9ECD2004231E", "ofefcgjbeghpigppfmkologfjadafddi": "90F9708A1A80E5AF49C294FBE9934EAD56751228A5DE71791C075B6860AF05C3", "opgbiafapkbbnbnjcdomjaghbckfkglc": "F6580D892F7B3DF7B622F236B280AB32D79060E73753FC35A6F3D776C09451C1", "pcgaihjmjiakbnobkkbjdhfcchfnbgbj": "B201E92563FF33D4EC8089FE785367FAD3376DE416BDA2F47F3A296F5F409ED9"}, "ui": {"developer_mode": "1F89DB1DF63F2C52B95CDF57DCE98C109D1A79A05E4C51B43ACBEE67BBC7AD37"}}, "google": {"services": {"last_signed_in_username": "1476DBCCD710759A3A00DDA4D8906228F8F48E186EEC60FA57B7FAF2364328B1"}}, "homepage": "3D708379264349802A6089C6BA66055D615F1A9B9D0861D0413C8E9E3B68B444", "homepage_is_newtabpage": "0BD0A34C349ADA85649ABD5C8D3CC240D69D048F2A77F38BF427B301A1CC1872", "media": {"cdm": {"origin_data": "8273AFBD1161CC75F5B08BBD4408DAE5095E8A71ABA3C24613E92C5A85901390"}, "storage_id_salt": "83C5AD99603406C0D3683C0A4EB29B988A7B2602D5425197327DD025ED40F77F"}, "pinned_tabs": "B3C35D11039A08566B009DA6BB99440201DD192C9BC6654BD69D3F67106FD9D6", "prefs": {"preference_reset_time": "D54DE5F20656109D5063FC69A938C09E6638DE98B1E21D996085D8AFCE733A4F"}, "safebrowsing": {"incidents_sent": "B5088CC32D5653E49C0187D3CFFD69FDFA8C0C80CBE3320776624497C886CC68"}, "search_provider_overrides": "9C3020E3464E557BB1ACB59BA112C7B9CB8FE6C6A1663406615F8020851F5568", "session": {"restore_on_startup": "44311EF09ECF703B4D8F2966F21E42D8F80FEDCC5D27872DACF3FC00DB4FE7A6", "startup_urls": "5754CA4C0757E92688CE6DE9036A31E25C89EB2CDF4EE223EEE1E94001B6957B"}}, "super_mac": "CDB498EA6F680BEB5BA0459120366590E4D46DD9995A9272CAAB0DDEEC8BE013"}, "session": {"startup_urls": ["http://egdh.baidu68861.com"], "startup_urls_edge_lightweight": "65AA54FA54CD0FB61DC9DC36872E034725101E0E2F4806865F7DAFC62036CF6DDF5BCE0E2E1325A1F37312CCCE11447B9098B7B336080BD3C417", "startup_urls_edge_lightweight_verify": "b11cb7c0a3534650de1ae4cbdbb7bd9a"}}