:root {
  --color: #444;
  --bg-color: #fff;
  --color-admin: #444;
  --color-desc: #2f2f2f;
  --bg-desc: #e8e8e8;
  --color-note: #2f2f2f;
  --bg-note: #e8e8e8;
  --bg-admin: #ffffed;
  --border-admin: #e8ec3a;
  --color-light: #636363;
  --bg-color-light: #f5f5f5;
  --bg-focus: #eff6f9;
  --color-a: #0057d8;
}
@media (prefers-color-scheme: dark) {
  :root {
    --color: #d1d2cc;
    --bg-color: #272d37;
    --color-light: #f5f5f5;
    --bg-color-light: #343946;
    --bg-focus: #454b5d;
    --color-a: #9ec9ff;
  }
}

body {
  color: var(--color);
  background-color: var(--bg-color);
  font-size: 14px;
  font-family: system-ui, -apple-system, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
  width: min(100% - 2rem, 70rem);
  margin-inline: auto;
  color-scheme: light dark;
}

textarea {
  padding: 5px;
}
button,
input[type=submit],
input[type=button] {
  height: 28px;
  color: #444;
  background-image: linear-gradient(rgb(237, 237, 237), rgb(237, 237, 237) 38%, rgb(222, 222, 222));
  box-shadow: rgba(0, 0, 0, 0.08) 0 1px 0, rgba(255, 255, 255, 0.75) 0 1px 2px inset;
  text-shadow: rgb(240, 240, 240) 0 1px 0;
  border: solid 1px rgba(0, 0, 0, 0.25);
}
button:disabled,
input[type=button]:disabled {
  opacity: 0.5;
}

button,
input[type=submit],
input[type=button] {
  cursor: pointer;
}
input[type=text],
textarea {
  color: var(--color-light);
  background-color: var(--bg-color-light);
  border: none;
  width: 100%;
  box-sizing: border-box;
  outline: none;
}
input[type=text] {
  height: 28px;
  text-indent: 5px;
}
textarea:focus {
  background-color: var(--bg-focus);
}
h1 {
  font-size: 15px;
  font-weight: normal;
}
a,
a:visited {
  color: var(--color-a);
  text-decoration: none;
}
.mode-3,
.mode-2,
.mode {
  display: grid;
  white-space: nowrap;
  align-items: center;
  grid-gap: 5px;
}
.mode {
  grid-template-columns: min-content min-content min-content;
}
.mode-2 {
  grid-template-columns: min-content min-content;
}
.mode-3 {
  grid-template-columns: 1fr min-content;
}
.mode-3 h1 {
  grid-column: 1/3;
}
.mode input[type=radio] {
  margin: 0;
}
#toggle-sibling-desc,
#toggle-parser-desc,
#toggle-protected-desc,
#toggle-custom-desc,
#toggle-whitelist-desc,
#toggle-blacklist-desc {
  cursor: pointer;
  color: var(--color-desc);
  background-color: var(--bg-desc);
  padding: 1px 4px;
}
.note {
  background-color: var(--bg-note);
  color: var(--color-note);
  padding: 10px;
  margin: 0 0 10px 0;
}
.hidden {
  display: none;
}
.checked {
  display: grid;
  grid-template-columns: min-content 1fr;
  grid-gap: 5px;
  margin: 10px 0;
}

#tools,
#backup {
  display: grid;
  white-space: nowrap;
  grid-gap: 5px;
}
#backup {
  margin: 10px 0;
  grid-template-columns: min-content min-content;
}
#tools {
  grid-template-columns: repeat(4, min-content) 1fr;
  align-items: center;
}
@media screen and (max-width: 600px) {
  #backup {
    grid-template-columns: 1fr 1fr;
  }
  #tools {
    grid-template-columns: 1fr 1fr;
  }
}
.admin {
  color: var(--color-admin);
  background-color: var(--bg-admin);
  border: solid 1px var(--border-admin);
  padding: 10px;
  margin: 15px 0;
}
#protected {
  white-space: normal;
}
