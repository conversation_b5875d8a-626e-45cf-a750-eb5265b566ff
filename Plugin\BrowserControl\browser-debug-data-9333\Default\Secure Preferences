{"edge": {"services": {"account_id": "0003BFFD5BEAE44A", "last_username": "<EMAIL>"}}, "edge_fundamentals_appdefaults": {"ess_lightweight_version": 101}, "ess_kv_states": {"restore_on_startup": {"closed_notification": false, "decrypt_success": true, "key": "restore_on_startup", "notification_popup_count": 0}, "startup_urls": {"closed_notification": false, "decrypt_success": true, "key": "startup_urls", "notification_popup_count": 0}, "template_url_data": {"closed_notification": false, "decrypt_success": true, "key": "template_url_data", "notification_popup_count": 0}}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "发现 Microsoft Edge 扩展。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Chrome 网上应用店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "bpelnogcookhocnaokfpoeinibimbeff": {"account_extension_type": 2, "active_permissions": {"api": ["activeTab", "contextMenus", "cookies", "storage", "unlimitedStorage", "newTabPageOverride", "scripting", "sidePanel"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["activeTab", "contextMenus", "cookies", "storage", "unlimitedStorage", "newTabPageOverride", "scripting", "sidePanel"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"48": "icons/icon_zh_action_48.png"}, "default_popup": "popup/index.html", "default_title": "WeTab 标签页"}, "background": {"service_worker": "serviceworker.js"}, "chrome_url_overrides": {"newtab": "index.html"}, "current_locale": "zh_CN", "default_locale": "zh_CN", "description": "用小组件自定义你的新标签页,支持暗黑模式", "host_permissions": ["<all_urls>"], "icons": {"128": "icons/icon_zh_128.png", "16": "icons/icon_zh_16.png", "32": "icons/icon_zh_32.png", "48": "icons/icon_zh_48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsVPNxUUTrxRO+deq8I90l0ZqTWCDmHHHUNbke1cySGUdZ+nz3rwDhtme5Tnwjyxz1CipahKfaD2hF++aRViXHRN5Yazm7bcw6jNP2dR02UKrx0WxbE5+FezXnR1oGX2hit5EUrfrsdsuE3uhGrbd09V45qU7uHHuHA2klCa5P/xGjaNS4m+sFiWd8yuXAmNlthhupfgzCcuBwwPnL2Dcyzdq/b3KThmqSo73yAMaZnQxUyW12UxK0SraDeq/2bixIMvYwmxA4fQi9S1KfTi7h+X2fR8yVVlNURjgNg2xlcDVBzeubxiNc3PV21x5T4sDp3pBHk8PKMBjN4T/MipaIwIDAQAB", "manifest_version": 3, "name": "WeTab 新标签页", "optional_permissions": ["system.cpu", "system.memory", "bookmarks", "history", "favicon"], "permissions": ["cookies", "activeTab", "storage", "unlimitedStorage", "sidePanel", "contextMenus", "scripting"], "side_panel": {"default_path": "sidepanel/index.html"}, "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "2.2.4", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["*"]}]}, "path": "bpelnogcookhocnaokfpoeinibimbeff\\2.2.4_0", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "2.2.4"}, "serviceworkerevents": ["contextMenus.onClicked", "runtime.onInstalled"], "uninstall_url": "https://uninstall.wetab.link/", "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "cjneempfhkonkkbcmnfdibgobmhbagaj": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "cnjkedgepfdpdbnepgmajmmjdjkjnifa": {"account_extension_type": 2, "active_permissions": {"api": ["contextMenus", "storage", "scripting", "declarativeNetRequestWithHostAccess"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["contextMenus", "storage", "scripting", "declarativeNetRequestWithHostAccess"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"16": "data/icons/ignored/16.png", "32": "data/icons/ignored/32.png", "48": "data/icons/ignored/48.png"}, "default_popup": "data/popup/index.html"}, "background": {"service_worker": "worker.js"}, "commands": {"_execute_action": {"description": "Execute Action"}}, "current_locale": "zh_CN", "default_locale": "en", "description": "欺骗性网站试图收集有关你的网页导航的信息，以提供你可能不想要的独特内容", "homepage_url": "https://webextension.org/listing/useragent-switcher.html", "host_permissions": ["<all_urls>"], "icons": {"128": "data/icons/active/128.png", "16": "data/icons/active/16.png", "256": "data/icons/active/256.png", "32": "data/icons/active/32.png", "48": "data/icons/active/48.png", "64": "data/icons/active/64.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyysy1IEBNm6uXGKUTr6wgh0EuhTxpYNGt0N03bm4LaBc/lR/EiQV3eZHn9HrL1cUrVnzxDf2xu7cn0GQLeb28TCBXecqZubTnn3a4mszsQYKxSMqMHHruPmGebw2snS8joQACLHoguISd9ysB2+ydIBrAkxCsocOxzTJGOEcRUXMEvOfFCQHWwA2DMuYw1cXkD1FJuhRdCbSEehCph0gFBKtAW/HH33fKXvj7N/vpH1O+HzYq2P1ZcQhN72ciA/58vBl8gar21qKY18oMQYrvznSPJwzOODgs1z74juvljK6h42M38+7Lad7ZgSMWNVSWc64nbl2SwPZ0rt8zM4xcQIDAQAB", "manifest_version": 3, "name": "User-Agent Switcher and Manager", "options_ui": {"open_in_tab": true, "page": "data/options/index.html"}, "permissions": ["storage", "contextMenus", "scripting", "declarativeNetRequestWithHostAccess"], "storage": {"managed_schema": "schema.json"}, "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "0.6.4"}, "path": "cnjkedgepfdpdbnepgmajmmjdjkjnifa\\0.6.4_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "dcaajljecejllikfgbhjdgeognacjkkp": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "dgbhmbogkcdheijkkdmfhodkamcaiheo": {"disable_reasons": [1], "lastpingday": "*****************"}, "dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "dhoenijjpgpeimemopealfcbiecgceod": {"account_extension_type": 2, "active_permissions": {"api": ["activeTab", "alarms", "contextMenus", "cookies", "storage", "tabs", "unlimitedStorage", "declarativeNetRequest", "scripting", "offscreen", "sidePanel"], "explicit_host": ["<all_urls>", "https://*.openai.com/*"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://*.yandex.by/search*", "https://*.yandex.com.tr/search*", "https://*.yandex.com/search*", "https://*.yandex.kz/search*", "https://*.yandex.ru/search*", "https://*.yandex.uz/search*", "https://chat.openai.com/*", "https://cn.bing.com/search*", "https://duckduckgo.com/*", "https://kagi.com/search*", "https://search.brave.com/search*", "https://search.naver.com/search*", "https://search.yahoo.co.jp/search*", "https://search.yahoo.com/search*", "https://searx.be/search*", "https://www.baidu.com/*", "https://www.bing.com/search*", "https://www.google.ad/search*", "https://www.google.ae/search*", "https://www.google.al/search*", "https://www.google.am/search*", "https://www.google.as/search*", "https://www.google.at/search*", "https://www.google.az/search*", "https://www.google.ba/search*", "https://www.google.be/search*", "https://www.google.bf/search*", "https://www.google.bg/search*", "https://www.google.bi/search*", "https://www.google.bj/search*", "https://www.google.bs/search*", "https://www.google.bt/search*", "https://www.google.by/search*", "https://www.google.ca/search*", "https://www.google.cat/search*", "https://www.google.cd/search*", "https://www.google.cf/search*", "https://www.google.cg/search*", "https://www.google.ch/search*", "https://www.google.ci/search*", "https://www.google.cl/search*", "https://www.google.cm/search*", "https://www.google.cn/search*", "https://www.google.co.ao/search*", "https://www.google.co.bw/search*", "https://www.google.co.ck/search*", "https://www.google.co.cr/search*", "https://www.google.co.id/search*", "https://www.google.co.il/search*", "https://www.google.co.in/search*", "https://www.google.co.jp/search*", "https://www.google.co.ke/search*", "https://www.google.co.kr/search*", "https://www.google.co.ls/search*", "https://www.google.co.ma/search*", "https://www.google.co.mz/search*", "https://www.google.co.nz/search*", "https://www.google.co.th/search*", "https://www.google.co.tz/search*", "https://www.google.co.ug/search*", "https://www.google.co.uk/search*", "https://www.google.co.uz/search*", "https://www.google.co.ve/search*", "https://www.google.co.vi/search*", "https://www.google.co.za/search*", "https://www.google.co.zm/search*", "https://www.google.co.zw/search*", "https://www.google.com.af/search*", "https://www.google.com.ag/search*", "https://www.google.com.ai/search*", "https://www.google.com.ar/search*", "https://www.google.com.au/search*", "https://www.google.com.bd/search*", "https://www.google.com.bh/search*", "https://www.google.com.bn/search*", "https://www.google.com.bo/search*", "https://www.google.com.br/search*", "https://www.google.com.bz/search*", "https://www.google.com.co/search*", "https://www.google.com.cu/search*", "https://www.google.com.cy/search*", "https://www.google.com.do/search*", "https://www.google.com.ec/search*", "https://www.google.com.eg/search*", "https://www.google.com.et/search*", "https://www.google.com.fj/search*", "https://www.google.com.gh/search*", "https://www.google.com.gi/search*", "https://www.google.com.gt/search*", "https://www.google.com.hk/search*", "https://www.google.com.jm/search*", "https://www.google.com.kh/search*", "https://www.google.com.kw/search*", "https://www.google.com.lb/search*", "https://www.google.com.ly/search*", "https://www.google.com.mm/search*", "https://www.google.com.mt/search*", "https://www.google.com.mx/search*", "https://www.google.com.my/search*", "https://www.google.com.na/search*", "https://www.google.com.ng/search*", "https://www.google.com.ni/search*", "https://www.google.com.np/search*", "https://www.google.com.om/search*", "https://www.google.com.pa/search*", "https://www.google.com.pe/search*", "https://www.google.com.pg/search*", "https://www.google.com.ph/search*", "https://www.google.com.pk/search*", "https://www.google.com.pr/search*", "https://www.google.com.py/search*", "https://www.google.com.qa/search*", "https://www.google.com.sa/search*", "https://www.google.com.sb/search*", "https://www.google.com.sg/search*", "https://www.google.com.sl/search*", "https://www.google.com.sv/search*", "https://www.google.com.tj/search*", "https://www.google.com.tr/search*", "https://www.google.com.tw/search*", "https://www.google.com.ua/search*", "https://www.google.com.uy/search*", "https://www.google.com.vc/search*", "https://www.google.com.vn/search*", "https://www.google.com/search*", "https://www.google.cv/search*", "https://www.google.cz/search*", "https://www.google.de/search*", "https://www.google.dj/search*", "https://www.google.dk/search*", "https://www.google.dm/search*", "https://www.google.dz/search*", "https://www.google.ee/search*", "https://www.google.es/search*", "https://www.google.fi/search*", "https://www.google.fm/search*", "https://www.google.fr/search*", "https://www.google.ga/search*", "https://www.google.ge/search*", "https://www.google.gg/search*", "https://www.google.gl/search*", "https://www.google.gm/search*", "https://www.google.gr/search*", "https://www.google.gy/search*", "https://www.google.hn/search*", "https://www.google.hr/search*", "https://www.google.ht/search*", "https://www.google.hu/search*", "https://www.google.ie/search*", "https://www.google.im/search*", "https://www.google.iq/search*", "https://www.google.is/search*", "https://www.google.it/search*", "https://www.google.je/search*", "https://www.google.jo/search*", "https://www.google.kg/search*", "https://www.google.ki/search*", "https://www.google.kz/search*", "https://www.google.la/search*", "https://www.google.li/search*", "https://www.google.lk/search*", "https://www.google.lt/search*", "https://www.google.lu/search*", "https://www.google.lv/search*", "https://www.google.md/search*", "https://www.google.me/search*", "https://www.google.mg/search*", "https://www.google.mk/search*", "https://www.google.ml/search*", "https://www.google.mn/search*", "https://www.google.ms/search*", "https://www.google.mu/search*", "https://www.google.mv/search*", "https://www.google.mw/search*", "https://www.google.ne/search*", "https://www.google.nl/search*", "https://www.google.no/search*", "https://www.google.nr/search*", "https://www.google.nu/search*", "https://www.google.pl/search*", "https://www.google.pn/search*", "https://www.google.ps/search*", "https://www.google.pt/search*", "https://www.google.ro/search*", "https://www.google.rs/search*", "https://www.google.ru/search*", "https://www.google.rw/search*", "https://www.google.sc/search*", "https://www.google.se/search*", "https://www.google.sh/search*", "https://www.google.si/search*", "https://www.google.sk/search*", "https://www.google.sm/search*", "https://www.google.sn/search*", "https://www.google.so/search*", "https://www.google.sr/search*", "https://www.google.st/search*", "https://www.google.td/search*", "https://www.google.tg/search*", "https://www.google.tl/search*", "https://www.google.tm/search*", "https://www.google.tn/search*", "https://www.google.to/search*", "https://www.google.tt/search*", "https://www.google.vg/search*", "https://www.google.vu/search*", "https://www.google.ws/search*", "https://www.netflix.com/*", "https://www.youtube-nocookie.com/embed/*", "https://www.youtube.com/embed/*", "https://www.zaobao.com.sg/*", "https://www.zaobao.com/*"]}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "13398082015773677", "first_install_time": "13398073238618386", "from_webstore": false, "granted_permissions": {"api": ["activeTab", "alarms", "contextMenus", "cookies", "storage", "tabs", "unlimitedStorage", "declarativeNetRequest", "scripting", "offscreen", "sidePanel"], "explicit_host": ["<all_urls>", "https://*.openai.com/*"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://*.yandex.by/search*", "https://*.yandex.com.tr/search*", "https://*.yandex.com/search*", "https://*.yandex.kz/search*", "https://*.yandex.ru/search*", "https://*.yandex.uz/search*", "https://chat.openai.com/*", "https://cn.bing.com/search*", "https://duckduckgo.com/*", "https://kagi.com/search*", "https://search.brave.com/search*", "https://search.naver.com/search*", "https://search.yahoo.co.jp/search*", "https://search.yahoo.com/search*", "https://searx.be/search*", "https://www.baidu.com/*", "https://www.bing.com/search*", "https://www.google.ad/search*", "https://www.google.ae/search*", "https://www.google.al/search*", "https://www.google.am/search*", "https://www.google.as/search*", "https://www.google.at/search*", "https://www.google.az/search*", "https://www.google.ba/search*", "https://www.google.be/search*", "https://www.google.bf/search*", "https://www.google.bg/search*", "https://www.google.bi/search*", "https://www.google.bj/search*", "https://www.google.bs/search*", "https://www.google.bt/search*", "https://www.google.by/search*", "https://www.google.ca/search*", "https://www.google.cat/search*", "https://www.google.cd/search*", "https://www.google.cf/search*", "https://www.google.cg/search*", "https://www.google.ch/search*", "https://www.google.ci/search*", "https://www.google.cl/search*", "https://www.google.cm/search*", "https://www.google.cn/search*", "https://www.google.co.ao/search*", "https://www.google.co.bw/search*", "https://www.google.co.ck/search*", "https://www.google.co.cr/search*", "https://www.google.co.id/search*", "https://www.google.co.il/search*", "https://www.google.co.in/search*", "https://www.google.co.jp/search*", "https://www.google.co.ke/search*", "https://www.google.co.kr/search*", "https://www.google.co.ls/search*", "https://www.google.co.ma/search*", "https://www.google.co.mz/search*", "https://www.google.co.nz/search*", "https://www.google.co.th/search*", "https://www.google.co.tz/search*", "https://www.google.co.ug/search*", "https://www.google.co.uk/search*", "https://www.google.co.uz/search*", "https://www.google.co.ve/search*", "https://www.google.co.vi/search*", "https://www.google.co.za/search*", "https://www.google.co.zm/search*", "https://www.google.co.zw/search*", "https://www.google.com.af/search*", "https://www.google.com.ag/search*", "https://www.google.com.ai/search*", "https://www.google.com.ar/search*", "https://www.google.com.au/search*", "https://www.google.com.bd/search*", "https://www.google.com.bh/search*", "https://www.google.com.bn/search*", "https://www.google.com.bo/search*", "https://www.google.com.br/search*", "https://www.google.com.bz/search*", "https://www.google.com.co/search*", "https://www.google.com.cu/search*", "https://www.google.com.cy/search*", "https://www.google.com.do/search*", "https://www.google.com.ec/search*", "https://www.google.com.eg/search*", "https://www.google.com.et/search*", "https://www.google.com.fj/search*", "https://www.google.com.gh/search*", "https://www.google.com.gi/search*", "https://www.google.com.gt/search*", "https://www.google.com.hk/search*", "https://www.google.com.jm/search*", "https://www.google.com.kh/search*", "https://www.google.com.kw/search*", "https://www.google.com.lb/search*", "https://www.google.com.ly/search*", "https://www.google.com.mm/search*", "https://www.google.com.mt/search*", "https://www.google.com.mx/search*", "https://www.google.com.my/search*", "https://www.google.com.na/search*", "https://www.google.com.ng/search*", "https://www.google.com.ni/search*", "https://www.google.com.np/search*", "https://www.google.com.om/search*", "https://www.google.com.pa/search*", "https://www.google.com.pe/search*", "https://www.google.com.pg/search*", "https://www.google.com.ph/search*", "https://www.google.com.pk/search*", "https://www.google.com.pr/search*", "https://www.google.com.py/search*", "https://www.google.com.qa/search*", "https://www.google.com.sa/search*", "https://www.google.com.sb/search*", "https://www.google.com.sg/search*", "https://www.google.com.sl/search*", "https://www.google.com.sv/search*", "https://www.google.com.tj/search*", "https://www.google.com.tr/search*", "https://www.google.com.tw/search*", "https://www.google.com.ua/search*", "https://www.google.com.uy/search*", "https://www.google.com.vc/search*", "https://www.google.com.vn/search*", "https://www.google.com/search*", "https://www.google.cv/search*", "https://www.google.cz/search*", "https://www.google.de/search*", "https://www.google.dj/search*", "https://www.google.dk/search*", "https://www.google.dm/search*", "https://www.google.dz/search*", "https://www.google.ee/search*", "https://www.google.es/search*", "https://www.google.fi/search*", "https://www.google.fm/search*", "https://www.google.fr/search*", "https://www.google.ga/search*", "https://www.google.ge/search*", "https://www.google.gg/search*", "https://www.google.gl/search*", "https://www.google.gm/search*", "https://www.google.gr/search*", "https://www.google.gy/search*", "https://www.google.hn/search*", "https://www.google.hr/search*", "https://www.google.ht/search*", "https://www.google.hu/search*", "https://www.google.ie/search*", "https://www.google.im/search*", "https://www.google.iq/search*", "https://www.google.is/search*", "https://www.google.it/search*", "https://www.google.je/search*", "https://www.google.jo/search*", "https://www.google.kg/search*", "https://www.google.ki/search*", "https://www.google.kz/search*", "https://www.google.la/search*", "https://www.google.li/search*", "https://www.google.lk/search*", "https://www.google.lt/search*", "https://www.google.lu/search*", "https://www.google.lv/search*", "https://www.google.md/search*", "https://www.google.me/search*", "https://www.google.mg/search*", "https://www.google.mk/search*", "https://www.google.ml/search*", "https://www.google.mn/search*", "https://www.google.ms/search*", "https://www.google.mu/search*", "https://www.google.mv/search*", "https://www.google.mw/search*", "https://www.google.ne/search*", "https://www.google.nl/search*", "https://www.google.no/search*", "https://www.google.nr/search*", "https://www.google.nu/search*", "https://www.google.pl/search*", "https://www.google.pn/search*", "https://www.google.ps/search*", "https://www.google.pt/search*", "https://www.google.ro/search*", "https://www.google.rs/search*", "https://www.google.ru/search*", "https://www.google.rw/search*", "https://www.google.sc/search*", "https://www.google.se/search*", "https://www.google.sh/search*", "https://www.google.si/search*", "https://www.google.sk/search*", "https://www.google.sm/search*", "https://www.google.sn/search*", "https://www.google.so/search*", "https://www.google.sr/search*", "https://www.google.st/search*", "https://www.google.td/search*", "https://www.google.tg/search*", "https://www.google.tl/search*", "https://www.google.tm/search*", "https://www.google.tn/search*", "https://www.google.to/search*", "https://www.google.tt/search*", "https://www.google.vg/search*", "https://www.google.vu/search*", "https://www.google.ws/search*", "https://www.netflix.com/*", "https://www.youtube-nocookie.com/embed/*", "https://www.youtube.com/embed/*", "https://www.zaobao.com.sg/*", "https://www.zaobao.com/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398073238618386", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_title": "Toggle ChatGPT Sidebar"}, "background": {"service_worker": "background.js"}, "content_scripts": [{"js": ["content-script.js"], "matches": ["https://www.google.com/search*", "https://www.google.ad/search*", "https://www.google.ae/search*", "https://www.google.com.af/search*", "https://www.google.com.ag/search*", "https://www.google.com.ai/search*", "https://www.google.al/search*", "https://www.google.am/search*", "https://www.google.co.ao/search*", "https://www.google.com.ar/search*", "https://www.google.as/search*", "https://www.google.at/search*", "https://www.google.com.au/search*", "https://www.google.az/search*", "https://www.google.ba/search*", "https://www.google.com.bd/search*", "https://www.google.be/search*", "https://www.google.bf/search*", "https://www.google.bg/search*", "https://www.google.com.bh/search*", "https://www.google.bi/search*", "https://www.google.bj/search*", "https://www.google.com.bn/search*", "https://www.google.com.bo/search*", "https://www.google.com.br/search*", "https://www.google.bs/search*", "https://www.google.bt/search*", "https://www.google.co.bw/search*", "https://www.google.by/search*", "https://www.google.com.bz/search*", "https://www.google.ca/search*", "https://www.google.cd/search*", "https://www.google.cf/search*", "https://www.google.cg/search*", "https://www.google.ch/search*", "https://www.google.ci/search*", "https://www.google.co.ck/search*", "https://www.google.cl/search*", "https://www.google.cm/search*", "https://www.google.cn/search*", "https://www.google.com.co/search*", "https://www.google.co.cr/search*", "https://www.google.com.cu/search*", "https://www.google.cv/search*", "https://www.google.com.cy/search*", "https://www.google.cz/search*", "https://www.google.de/search*", "https://www.google.dj/search*", "https://www.google.dk/search*", "https://www.google.dm/search*", "https://www.google.com.do/search*", "https://www.google.dz/search*", "https://www.google.com.ec/search*", "https://www.google.ee/search*", "https://www.google.com.eg/search*", "https://www.google.es/search*", "https://www.google.com.et/search*", "https://www.google.fi/search*", "https://www.google.com.fj/search*", "https://www.google.fm/search*", "https://www.google.fr/search*", "https://www.google.ga/search*", "https://www.google.ge/search*", "https://www.google.gg/search*", "https://www.google.com.gh/search*", "https://www.google.com.gi/search*", "https://www.google.gl/search*", "https://www.google.gm/search*", "https://www.google.gr/search*", "https://www.google.com.gt/search*", "https://www.google.gy/search*", "https://www.google.com.hk/search*", "https://www.google.hn/search*", "https://www.google.hr/search*", "https://www.google.ht/search*", "https://www.google.hu/search*", "https://www.google.co.id/search*", "https://www.google.ie/search*", "https://www.google.co.il/search*", "https://www.google.im/search*", "https://www.google.co.in/search*", "https://www.google.iq/search*", "https://www.google.is/search*", "https://www.google.it/search*", "https://www.google.je/search*", "https://www.google.com.jm/search*", "https://www.google.jo/search*", "https://www.google.co.jp/search*", "https://www.google.co.ke/search*", "https://www.google.com.kh/search*", "https://www.google.ki/search*", "https://www.google.kg/search*", "https://www.google.co.kr/search*", "https://www.google.com.kw/search*", "https://www.google.kz/search*", "https://www.google.la/search*", "https://www.google.com.lb/search*", "https://www.google.li/search*", "https://www.google.lk/search*", "https://www.google.co.ls/search*", "https://www.google.lt/search*", "https://www.google.lu/search*", "https://www.google.lv/search*", "https://www.google.com.ly/search*", "https://www.google.co.ma/search*", "https://www.google.md/search*", "https://www.google.me/search*", "https://www.google.mg/search*", "https://www.google.mk/search*", "https://www.google.ml/search*", "https://www.google.com.mm/search*", "https://www.google.mn/search*", "https://www.google.ms/search*", "https://www.google.com.mt/search*", "https://www.google.mu/search*", "https://www.google.mv/search*", "https://www.google.mw/search*", "https://www.google.com.mx/search*", "https://www.google.com.my/search*", "https://www.google.co.mz/search*", "https://www.google.com.na/search*", "https://www.google.com.ng/search*", "https://www.google.com.ni/search*", "https://www.google.ne/search*", "https://www.google.nl/search*", "https://www.google.no/search*", "https://www.google.com.np/search*", "https://www.google.nr/search*", "https://www.google.nu/search*", "https://www.google.co.nz/search*", "https://www.google.com.om/search*", "https://www.google.com.pa/search*", "https://www.google.com.pe/search*", "https://www.google.com.pg/search*", "https://www.google.com.ph/search*", "https://www.google.com.pk/search*", "https://www.google.pl/search*", "https://www.google.pn/search*", "https://www.google.com.pr/search*", "https://www.google.ps/search*", "https://www.google.pt/search*", "https://www.google.com.py/search*", "https://www.google.com.qa/search*", "https://www.google.ro/search*", "https://www.google.ru/search*", "https://www.google.rw/search*", "https://www.google.com.sa/search*", "https://www.google.com.sb/search*", "https://www.google.sc/search*", "https://www.google.se/search*", "https://www.google.com.sg/search*", "https://www.google.sh/search*", "https://www.google.si/search*", "https://www.google.sk/search*", "https://www.google.com.sl/search*", "https://www.google.sn/search*", "https://www.google.so/search*", "https://www.google.sm/search*", "https://www.google.sr/search*", "https://www.google.st/search*", "https://www.google.com.sv/search*", "https://www.google.td/search*", "https://www.google.tg/search*", "https://www.google.co.th/search*", "https://www.google.com.tj/search*", "https://www.google.tl/search*", "https://www.google.tm/search*", "https://www.google.tn/search*", "https://www.google.to/search*", "https://www.google.com.tr/search*", "https://www.google.tt/search*", "https://www.google.com.tw/search*", "https://www.google.co.tz/search*", "https://www.google.com.ua/search*", "https://www.google.co.ug/search*", "https://www.google.co.uk/search*", "https://www.google.com.uy/search*", "https://www.google.co.uz/search*", "https://www.google.com.vc/search*", "https://www.google.co.ve/search*", "https://www.google.vg/search*", "https://www.google.co.vi/search*", "https://www.google.com.vn/search*", "https://www.google.vu/search*", "https://www.google.ws/search*", "https://www.google.rs/search*", "https://www.google.co.za/search*", "https://www.google.co.zm/search*", "https://www.google.co.zw/search*", "https://www.google.cat/search*", "https://kagi.com/search*", "https://www.bing.com/search*", "https://cn.bing.com/search*", "https://search.yahoo.com/search*", "https://search.yahoo.co.jp/search*", "https://search.naver.com/search*", "https://search.brave.com/search*", "https://duckduckgo.com/*", "https://www.baidu.com/*", "https://*.yandex.com/search*", "https://*.yandex.ru/search*", "https://*.yandex.com.tr/search*", "https://*.yandex.uz/search*", "https://*.yandex.kz/search*", "https://*.yandex.by/search*", "https://searx.be/search*"]}, {"js": ["content-chat.openai.com.js"], "matches": ["https://chat.openai.com/*"]}, {"all_frames": true, "css": ["fonts/fonts.css"], "js": ["content-all.js"], "match_about_blank": true, "matches": ["<all_urls>"], "run_at": "document_end"}, {"all_frames": true, "css": ["fonts/fonts.css"], "js": ["content-youtube-embed.js"], "matches": ["https://www.youtube.com/embed/*", "https://www.youtube-nocookie.com/embed/*"], "run_at": "document_end"}, {"js": ["inject-json-hack.js"], "matches": ["https://www.netflix.com/*"], "run_at": "document_end", "world": "MAIN"}, {"all_frames": true, "js": ["all-frames.js"], "match_about_blank": true, "matches": ["<all_urls>"], "run_at": "document_start"}, {"all_frames": true, "js": ["all-frames-main-start.js"], "matches": ["https://www.zaobao.com/*", "https://www.zaobao.com.sg/*"], "run_at": "document_start", "world": "MAIN"}], "content_security_policy": {"extension_pages": "script-src 'self' http://localhost:8097/ 'wasm-unsafe-eval'; object-src 'self'", "sandbox": "sandbox allow-scripts; script-src 'self' https://ssl.google-analytics.com https://www.google-analytics.com https://connect.facebook.net https://platform.twitter.com http://localhost:8097/; object-src 'self';  connect-src ws://localhost:8097"}, "current_locale": "zh_CN", "default_locale": "en", "description": "ChatGPT 侧边栏：使用 ChatGPT、GPT-4o、Claude3 和 Gemini 1.5 Pro 进行高级 AI 搜索、阅读和写作，提升工作效率。", "host_permissions": ["https://*.openai.com/", "<all_urls>"], "icons": {"128": "logo.png", "16": "logo.png", "32": "logo.png", "48": "logo.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1HThuo/7TwnEDQaGx+WeitecmGAACvRzCVeUl5dl8FFwK+wE+xJ1VhGSYlggQ0wgvq6ZAgvIo6/DjsJVaK93MLqhs4DxchinHlRnPp6k+bjD<PERSON>h65456peIPa6ygSKbYdHDpZyQAYlNLIBS4MPUQEsuoy/SI4W8PloyYlDJsS9p/5jD7tQsqXmMRnlZPLEqphTbZctQ6frZnTAg/KwnMvqdyhQkmWNiYl+1VfENViI/174Gil2+4Mqr7btZALu3ZV3+NOTYL2wrJGx9x7QyXwusNrtYA1n9LO52dVWE69BWCCozBkAUtIXhDH2CPva2a7zzYjv68C/NA3NQmwCz8QIDAQAB", "manifest_version": 3, "name": "Sider: ChatGPT 侧边栏", "optional_permissions": ["clipboardRead"], "options_ui": {"open_in_tab": true, "page": "options.html"}, "permissions": ["storage", "cookies", "contextMenus", "scripting", "activeTab", "unlimitedStorage", "tabs", "sidePanel", "offscreen", "alarms", "declarativeNetRequest"], "side_panel": {"default_path": "sidepanel.html"}, "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "5.13.0", "version_name": "5.13.0-e", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["content-all.css", "content-script.css", "i18n/*", "assets/*", "fonts/*"]}]}, "path": "dhoenijjpgpeimemopealfcbiecgceod\\5.13.0_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "dimkapigjeiopninbepgaoobcnbjmgik": {"account_extension_type": 2, "active_permissions": {"api": ["storage"], "explicit_host": ["*://*/*"], "manifest_permissions": [], "scriptable_host": ["*://*/*", "*://duckduckgo.com/?*", "*://search.brave.com/search?*", "*://www.bing.com/search?*", "*://www.ecosia.org/search?*", "*://www.google.ac/search?*", "*://www.google.ad/search?*", "*://www.google.ae/search?*", "*://www.google.al/search?*", "*://www.google.am/search?*", "*://www.google.as/search?*", "*://www.google.at/search?*", "*://www.google.az/search?*", "*://www.google.ba/search?*", "*://www.google.be/search?*", "*://www.google.bf/search?*", "*://www.google.bg/search?*", "*://www.google.bi/search?*", "*://www.google.bj/search?*", "*://www.google.bs/search?*", "*://www.google.bt/search?*", "*://www.google.by/search?*", "*://www.google.ca/search?*", "*://www.google.cat/search?*", "*://www.google.cc/search?*", "*://www.google.cd/search?*", "*://www.google.cf/search?*", "*://www.google.cg/search?*", "*://www.google.ch/search?*", "*://www.google.ci/search?*", "*://www.google.cl/search?*", "*://www.google.cm/search?*", "*://www.google.cn/search?*", "*://www.google.co.ao/search?*", "*://www.google.co.bw/search?*", "*://www.google.co.ck/search?*", "*://www.google.co.cr/search?*", "*://www.google.co.id/search?*", "*://www.google.co.il/search?*", "*://www.google.co.in/search?*", "*://www.google.co.jp/search?*", "*://www.google.co.ke/search?*", "*://www.google.co.kr/search?*", "*://www.google.co.ls/search?*", "*://www.google.co.ma/search?*", "*://www.google.co.mz/search?*", "*://www.google.co.nz/search?*", "*://www.google.co.th/search?*", "*://www.google.co.tz/search?*", "*://www.google.co.ug/search?*", "*://www.google.co.uk/search?*", "*://www.google.co.uz/search?*", "*://www.google.co.ve/search?*", "*://www.google.co.vi/search?*", "*://www.google.co.za/search?*", "*://www.google.co.zm/search?*", "*://www.google.co.zw/search?*", "*://www.google.com.af/search?*", "*://www.google.com.ag/search?*", "*://www.google.com.ai/search?*", "*://www.google.com.ar/search?*", "*://www.google.com.au/search?*", "*://www.google.com.bd/search?*", "*://www.google.com.bh/search?*", "*://www.google.com.bn/search?*", "*://www.google.com.bo/search?*", "*://www.google.com.br/search?*", "*://www.google.com.bz/search?*", "*://www.google.com.co/search?*", "*://www.google.com.cu/search?*", "*://www.google.com.cy/search?*", "*://www.google.com.do/search?*", "*://www.google.com.ec/search?*", "*://www.google.com.eg/search?*", "*://www.google.com.et/search?*", "*://www.google.com.fj/search?*", "*://www.google.com.gh/search?*", "*://www.google.com.gi/search?*", "*://www.google.com.gt/search?*", "*://www.google.com.hk/search?*", "*://www.google.com.jm/search?*", "*://www.google.com.kh/search?*", "*://www.google.com.kw/search?*", "*://www.google.com.lb/search?*", "*://www.google.com.lc/search?*", "*://www.google.com.ly/search?*", "*://www.google.com.mm/search?*", "*://www.google.com.mt/search?*", "*://www.google.com.mx/search?*", "*://www.google.com.my/search?*", "*://www.google.com.na/search?*", "*://www.google.com.nf/search?*", "*://www.google.com.ng/search?*", "*://www.google.com.ni/search?*", "*://www.google.com.np/search?*", "*://www.google.com.om/search?*", "*://www.google.com.pa/search?*", "*://www.google.com.pe/search?*", "*://www.google.com.pg/search?*", "*://www.google.com.ph/search?*", "*://www.google.com.pk/search?*", "*://www.google.com.pr/search?*", "*://www.google.com.py/search?*", "*://www.google.com.qa/search?*", "*://www.google.com.sa/search?*", "*://www.google.com.sb/search?*", "*://www.google.com.sg/search?*", "*://www.google.com.sl/search?*", "*://www.google.com.sv/search?*", "*://www.google.com.tj/search?*", "*://www.google.com.tr/search?*", "*://www.google.com.tw/search?*", "*://www.google.com.ua/search?*", "*://www.google.com.uy/search?*", "*://www.google.com.vc/search?*", "*://www.google.com.vn/search?*", "*://www.google.com/search?*", "*://www.google.cv/search?*", "*://www.google.cz/search?*", "*://www.google.de/search?*", "*://www.google.dj/search?*", "*://www.google.dk/search?*", "*://www.google.dm/search?*", "*://www.google.dz/search?*", "*://www.google.ee/search?*", "*://www.google.es/search?*", "*://www.google.fi/search?*", "*://www.google.fm/search?*", "*://www.google.fr/search?*", "*://www.google.ga/search?*", "*://www.google.ge/search?*", "*://www.google.gf/search?*", "*://www.google.gg/search?*", "*://www.google.gl/search?*", "*://www.google.gm/search?*", "*://www.google.gp/search?*", "*://www.google.gr/search?*", "*://www.google.gy/search?*", "*://www.google.hn/search?*", "*://www.google.hr/search?*", "*://www.google.ht/search?*", "*://www.google.hu/search?*", "*://www.google.ie/search?*", "*://www.google.im/search?*", "*://www.google.io/search?*", "*://www.google.iq/search?*", "*://www.google.is/search?*", "*://www.google.it/search?*", "*://www.google.je/search?*", "*://www.google.jo/search?*", "*://www.google.kg/search?*", "*://www.google.ki/search?*", "*://www.google.kz/search?*", "*://www.google.la/search?*", "*://www.google.li/search?*", "*://www.google.lk/search?*", "*://www.google.lt/search?*", "*://www.google.lu/search?*", "*://www.google.lv/search?*", "*://www.google.md/search?*", "*://www.google.me/search?*", "*://www.google.mg/search?*", "*://www.google.mk/search?*", "*://www.google.ml/search?*", "*://www.google.mn/search?*", "*://www.google.ms/search?*", "*://www.google.mu/search?*", "*://www.google.mv/search?*", "*://www.google.mw/search?*", "*://www.google.ne/search?*", "*://www.google.nl/search?*", "*://www.google.no/search?*", "*://www.google.nr/search?*", "*://www.google.nu/search?*", "*://www.google.pl/search?*", "*://www.google.pn/search?*", "*://www.google.ps/search?*", "*://www.google.pt/search?*", "*://www.google.ro/search?*", "*://www.google.rs/search?*", "*://www.google.ru/search?*", "*://www.google.rw/search?*", "*://www.google.sc/search?*", "*://www.google.se/search?*", "*://www.google.sh/search?*", "*://www.google.si/search?*", "*://www.google.sk/search?*", "*://www.google.sm/search?*", "*://www.google.sn/search?*", "*://www.google.so/search?*", "*://www.google.sr/search?*", "*://www.google.st/search?*", "*://www.google.td/search?*", "*://www.google.tg/search?*", "*://www.google.tk/search?*", "*://www.google.tl/search?*", "*://www.google.tm/search?*", "*://www.google.tn/search?*", "*://www.google.to/search?*", "*://www.google.tt/search?*", "*://www.google.vg/search?*", "*://www.google.vu/search?*", "*://www.google.ws/search?*"]}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "13398082015773686", "first_install_time": "13398073299381736", "from_webstore": false, "granted_permissions": {"api": ["storage"], "explicit_host": ["*://*/*"], "manifest_permissions": [], "scriptable_host": ["*://*/*", "*://duckduckgo.com/?*", "*://search.brave.com/search?*", "*://www.bing.com/search?*", "*://www.ecosia.org/search?*", "*://www.google.ac/search?*", "*://www.google.ad/search?*", "*://www.google.ae/search?*", "*://www.google.al/search?*", "*://www.google.am/search?*", "*://www.google.as/search?*", "*://www.google.at/search?*", "*://www.google.az/search?*", "*://www.google.ba/search?*", "*://www.google.be/search?*", "*://www.google.bf/search?*", "*://www.google.bg/search?*", "*://www.google.bi/search?*", "*://www.google.bj/search?*", "*://www.google.bs/search?*", "*://www.google.bt/search?*", "*://www.google.by/search?*", "*://www.google.ca/search?*", "*://www.google.cat/search?*", "*://www.google.cc/search?*", "*://www.google.cd/search?*", "*://www.google.cf/search?*", "*://www.google.cg/search?*", "*://www.google.ch/search?*", "*://www.google.ci/search?*", "*://www.google.cl/search?*", "*://www.google.cm/search?*", "*://www.google.cn/search?*", "*://www.google.co.ao/search?*", "*://www.google.co.bw/search?*", "*://www.google.co.ck/search?*", "*://www.google.co.cr/search?*", "*://www.google.co.id/search?*", "*://www.google.co.il/search?*", "*://www.google.co.in/search?*", "*://www.google.co.jp/search?*", "*://www.google.co.ke/search?*", "*://www.google.co.kr/search?*", "*://www.google.co.ls/search?*", "*://www.google.co.ma/search?*", "*://www.google.co.mz/search?*", "*://www.google.co.nz/search?*", "*://www.google.co.th/search?*", "*://www.google.co.tz/search?*", "*://www.google.co.ug/search?*", "*://www.google.co.uk/search?*", "*://www.google.co.uz/search?*", "*://www.google.co.ve/search?*", "*://www.google.co.vi/search?*", "*://www.google.co.za/search?*", "*://www.google.co.zm/search?*", "*://www.google.co.zw/search?*", "*://www.google.com.af/search?*", "*://www.google.com.ag/search?*", "*://www.google.com.ai/search?*", "*://www.google.com.ar/search?*", "*://www.google.com.au/search?*", "*://www.google.com.bd/search?*", "*://www.google.com.bh/search?*", "*://www.google.com.bn/search?*", "*://www.google.com.bo/search?*", "*://www.google.com.br/search?*", "*://www.google.com.bz/search?*", "*://www.google.com.co/search?*", "*://www.google.com.cu/search?*", "*://www.google.com.cy/search?*", "*://www.google.com.do/search?*", "*://www.google.com.ec/search?*", "*://www.google.com.eg/search?*", "*://www.google.com.et/search?*", "*://www.google.com.fj/search?*", "*://www.google.com.gh/search?*", "*://www.google.com.gi/search?*", "*://www.google.com.gt/search?*", "*://www.google.com.hk/search?*", "*://www.google.com.jm/search?*", "*://www.google.com.kh/search?*", "*://www.google.com.kw/search?*", "*://www.google.com.lb/search?*", "*://www.google.com.lc/search?*", "*://www.google.com.ly/search?*", "*://www.google.com.mm/search?*", "*://www.google.com.mt/search?*", "*://www.google.com.mx/search?*", "*://www.google.com.my/search?*", "*://www.google.com.na/search?*", "*://www.google.com.nf/search?*", "*://www.google.com.ng/search?*", "*://www.google.com.ni/search?*", "*://www.google.com.np/search?*", "*://www.google.com.om/search?*", "*://www.google.com.pa/search?*", "*://www.google.com.pe/search?*", "*://www.google.com.pg/search?*", "*://www.google.com.ph/search?*", "*://www.google.com.pk/search?*", "*://www.google.com.pr/search?*", "*://www.google.com.py/search?*", "*://www.google.com.qa/search?*", "*://www.google.com.sa/search?*", "*://www.google.com.sb/search?*", "*://www.google.com.sg/search?*", "*://www.google.com.sl/search?*", "*://www.google.com.sv/search?*", "*://www.google.com.tj/search?*", "*://www.google.com.tr/search?*", "*://www.google.com.tw/search?*", "*://www.google.com.ua/search?*", "*://www.google.com.uy/search?*", "*://www.google.com.vc/search?*", "*://www.google.com.vn/search?*", "*://www.google.com/search?*", "*://www.google.cv/search?*", "*://www.google.cz/search?*", "*://www.google.de/search?*", "*://www.google.dj/search?*", "*://www.google.dk/search?*", "*://www.google.dm/search?*", "*://www.google.dz/search?*", "*://www.google.ee/search?*", "*://www.google.es/search?*", "*://www.google.fi/search?*", "*://www.google.fm/search?*", "*://www.google.fr/search?*", "*://www.google.ga/search?*", "*://www.google.ge/search?*", "*://www.google.gf/search?*", "*://www.google.gg/search?*", "*://www.google.gl/search?*", "*://www.google.gm/search?*", "*://www.google.gp/search?*", "*://www.google.gr/search?*", "*://www.google.gy/search?*", "*://www.google.hn/search?*", "*://www.google.hr/search?*", "*://www.google.ht/search?*", "*://www.google.hu/search?*", "*://www.google.ie/search?*", "*://www.google.im/search?*", "*://www.google.io/search?*", "*://www.google.iq/search?*", "*://www.google.is/search?*", "*://www.google.it/search?*", "*://www.google.je/search?*", "*://www.google.jo/search?*", "*://www.google.kg/search?*", "*://www.google.ki/search?*", "*://www.google.kz/search?*", "*://www.google.la/search?*", "*://www.google.li/search?*", "*://www.google.lk/search?*", "*://www.google.lt/search?*", "*://www.google.lu/search?*", "*://www.google.lv/search?*", "*://www.google.md/search?*", "*://www.google.me/search?*", "*://www.google.mg/search?*", "*://www.google.mk/search?*", "*://www.google.ml/search?*", "*://www.google.mn/search?*", "*://www.google.ms/search?*", "*://www.google.mu/search?*", "*://www.google.mv/search?*", "*://www.google.mw/search?*", "*://www.google.ne/search?*", "*://www.google.nl/search?*", "*://www.google.no/search?*", "*://www.google.nr/search?*", "*://www.google.nu/search?*", "*://www.google.pl/search?*", "*://www.google.pn/search?*", "*://www.google.ps/search?*", "*://www.google.pt/search?*", "*://www.google.ro/search?*", "*://www.google.rs/search?*", "*://www.google.ru/search?*", "*://www.google.rw/search?*", "*://www.google.sc/search?*", "*://www.google.se/search?*", "*://www.google.sh/search?*", "*://www.google.si/search?*", "*://www.google.sk/search?*", "*://www.google.sm/search?*", "*://www.google.sn/search?*", "*://www.google.so/search?*", "*://www.google.sr/search?*", "*://www.google.st/search?*", "*://www.google.td/search?*", "*://www.google.tg/search?*", "*://www.google.tk/search?*", "*://www.google.tl/search?*", "*://www.google.tm/search?*", "*://www.google.tn/search?*", "*://www.google.to/search?*", "*://www.google.tt/search?*", "*://www.google.vg/search?*", "*://www.google.vu/search?*", "*://www.google.ws/search?*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398073299381736", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_popup": "pop-up/index.html"}, "background": {"service_worker": "background.js"}, "content_scripts": [{"js": ["libs/plotly-2.8.3.min.js", "libs/math.min.js", "libs/run_prettify.js", "libs/tex-svg.js", "libs/drawdown.js", "settings.js", "plot.js", "utils.js", "sites/chatgpt.js", "panel.js", "tools.js", "init.js", "sites/stackoverflow.js", "sites/mdn.js", "sites/wikipedia.js", "sites/w3schools.js", "sites/genius.js", "sites/unity.js", "sites/mathworks.js"], "matches": ["*://*/*", "*://duckduckgo.com/?*", "*://www.bing.com/search?*", "*://www.ecosia.org/search?*", "*://search.brave.com/search?*", "*://www.google.com/search?*", "*://www.google.ac/search?*", "*://www.google.ad/search?*", "*://www.google.ae/search?*", "*://www.google.com.af/search?*", "*://www.google.com.ag/search?*", "*://www.google.com.ai/search?*", "*://www.google.al/search?*", "*://www.google.am/search?*", "*://www.google.co.ao/search?*", "*://www.google.com.ar/search?*", "*://www.google.as/search?*", "*://www.google.at/search?*", "*://www.google.com.au/search?*", "*://www.google.az/search?*", "*://www.google.ba/search?*", "*://www.google.com.bd/search?*", "*://www.google.be/search?*", "*://www.google.bf/search?*", "*://www.google.bg/search?*", "*://www.google.com.bh/search?*", "*://www.google.bi/search?*", "*://www.google.bj/search?*", "*://www.google.com.bn/search?*", "*://www.google.com.bo/search?*", "*://www.google.com.br/search?*", "*://www.google.bs/search?*", "*://www.google.bt/search?*", "*://www.google.co.bw/search?*", "*://www.google.by/search?*", "*://www.google.com.bz/search?*", "*://www.google.ca/search?*", "*://www.google.com.kh/search?*", "*://www.google.cc/search?*", "*://www.google.cd/search?*", "*://www.google.cf/search?*", "*://www.google.cat/search?*", "*://www.google.cg/search?*", "*://www.google.ch/search?*", "*://www.google.ci/search?*", "*://www.google.co.ck/search?*", "*://www.google.cl/search?*", "*://www.google.cm/search?*", "*://www.google.cn/search?*", "*://www.google.com.co/search?*", "*://www.google.co.cr/search?*", "*://www.google.com.cu/search?*", "*://www.google.cv/search?*", "*://www.google.com.cy/search?*", "*://www.google.cz/search?*", "*://www.google.de/search?*", "*://www.google.dj/search?*", "*://www.google.dk/search?*", "*://www.google.dm/search?*", "*://www.google.com.do/search?*", "*://www.google.dz/search?*", "*://www.google.com.ec/search?*", "*://www.google.ee/search?*", "*://www.google.com.eg/search?*", "*://www.google.es/search?*", "*://www.google.com.et/search?*", "*://www.google.fi/search?*", "*://www.google.com.fj/search?*", "*://www.google.fm/search?*", "*://www.google.fr/search?*", "*://www.google.ga/search?*", "*://www.google.ge/search?*", "*://www.google.gf/search?*", "*://www.google.gg/search?*", "*://www.google.com.gh/search?*", "*://www.google.com.gi/search?*", "*://www.google.gl/search?*", "*://www.google.gm/search?*", "*://www.google.gp/search?*", "*://www.google.gr/search?*", "*://www.google.com.gt/search?*", "*://www.google.gy/search?*", "*://www.google.com.hk/search?*", "*://www.google.hn/search?*", "*://www.google.hr/search?*", "*://www.google.ht/search?*", "*://www.google.hu/search?*", "*://www.google.co.id/search?*", "*://www.google.iq/search?*", "*://www.google.ie/search?*", "*://www.google.co.il/search?*", "*://www.google.im/search?*", "*://www.google.co.in/search?*", "*://www.google.io/search?*", "*://www.google.is/search?*", "*://www.google.it/search?*", "*://www.google.je/search?*", "*://www.google.com.jm/search?*", "*://www.google.jo/search?*", "*://www.google.co.jp/search?*", "*://www.google.co.ke/search?*", "*://www.google.ki/search?*", "*://www.google.kg/search?*", "*://www.google.co.kr/search?*", "*://www.google.com.kw/search?*", "*://www.google.kz/search?*", "*://www.google.la/search?*", "*://www.google.com.lb/search?*", "*://www.google.com.lc/search?*", "*://www.google.li/search?*", "*://www.google.lk/search?*", "*://www.google.co.ls/search?*", "*://www.google.lt/search?*", "*://www.google.lu/search?*", "*://www.google.lv/search?*", "*://www.google.com.ly/search?*", "*://www.google.co.ma/search?*", "*://www.google.md/search?*", "*://www.google.me/search?*", "*://www.google.mg/search?*", "*://www.google.mk/search?*", "*://www.google.ml/search?*", "*://www.google.com.mm/search?*", "*://www.google.mn/search?*", "*://www.google.ms/search?*", "*://www.google.com.mt/search?*", "*://www.google.mu/search?*", "*://www.google.mv/search?*", "*://www.google.mw/search?*", "*://www.google.com.mx/search?*", "*://www.google.com.my/search?*", "*://www.google.co.mz/search?*", "*://www.google.com.na/search?*", "*://www.google.ne/search?*", "*://www.google.com.nf/search?*", "*://www.google.com.ng/search?*", "*://www.google.com.ni/search?*", "*://www.google.nl/search?*", "*://www.google.no/search?*", "*://www.google.com.np/search?*", "*://www.google.nr/search?*", "*://www.google.nu/search?*", "*://www.google.co.nz/search?*", "*://www.google.com.om/search?*", "*://www.google.com.pk/search?*", "*://www.google.com.pa/search?*", "*://www.google.com.pe/search?*", "*://www.google.com.ph/search?*", "*://www.google.pl/search?*", "*://www.google.com.pg/search?*", "*://www.google.pn/search?*", "*://www.google.com.pr/search?*", "*://www.google.ps/search?*", "*://www.google.pt/search?*", "*://www.google.com.py/search?*", "*://www.google.com.qa/search?*", "*://www.google.ro/search?*", "*://www.google.rs/search?*", "*://www.google.ru/search?*", "*://www.google.rw/search?*", "*://www.google.com.sa/search?*", "*://www.google.com.sb/search?*", "*://www.google.sc/search?*", "*://www.google.se/search?*", "*://www.google.com.sg/search?*", "*://www.google.sh/search?*", "*://www.google.si/search?*", "*://www.google.sk/search?*", "*://www.google.com.sl/search?*", "*://www.google.sn/search?*", "*://www.google.sm/search?*", "*://www.google.so/search?*", "*://www.google.st/search?*", "*://www.google.sr/search?*", "*://www.google.com.sv/search?*", "*://www.google.td/search?*", "*://www.google.tg/search?*", "*://www.google.co.th/search?*", "*://www.google.com.tj/search?*", "*://www.google.tk/search?*", "*://www.google.tl/search?*", "*://www.google.tm/search?*", "*://www.google.to/search?*", "*://www.google.tn/search?*", "*://www.google.com.tr/search?*", "*://www.google.tt/search?*", "*://www.google.com.tw/search?*", "*://www.google.co.tz/search?*", "*://www.google.com.ua/search?*", "*://www.google.co.ug/search?*", "*://www.google.co.uk/search?*", "*://www.google.com/search?*", "*://www.google.com.uy/search?*", "*://www.google.co.uz/search?*", "*://www.google.com.vc/search?*", "*://www.google.co.ve/search?*", "*://www.google.vg/search?*", "*://www.google.co.vi/search?*", "*://www.google.com.vn/search?*", "*://www.google.vu/search?*", "*://www.google.ws/search?*", "*://www.google.co.za/search?*", "*://www.google.co.zm/search?*", "*://www.google.co.zw/search?*"], "run_at": "document_end"}], "current_locale": "zh_CN", "default_locale": "en", "description": "Get ChatGPT response on any website. AI replies works on Google, Gmail, & 10M+ websites. It's free & privacy first. GPT everywhere", "host_permissions": ["*://*/*"], "icons": {"128": "icons/icon_128.png", "16": "icons/icon_16.png", "48": "icons/icon_48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxGms7ce/gAcbsibjH7j15qPrU/bom/2WqMmDY2i/aNQzecOj/d1d1PQXvrSI2Bea8HgltE7oRuNdlEQeMmGekallJNPiMMa6RuD3IKXXN8xOVz7OO+4K5DtHl7FfxQPPKigucjOAJjJY/FHnT6xxIE8zSZ4zBQY8zYLPemASDewydv8f4rATpdWmA++AOMX9WXWspo4BHH9nSyz6Vun8sDPTWHzW26+5aL+2ggU3cpypygc3mde4Y/Z+09un8xgBbbIWT8hRaTEG+Lo+vYhzv3DM1KI/o0VVzGXwz06BfWJSV2vMSOyzK/FZmYUaHDqG//2PJXQIw2C5+PEKyxBlKQIDAQAB", "manifest_version": 3, "name": "AI Search GPT for Edge", "permissions": ["storage"], "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "1.2", "web_accessible_resources": [{"matches": ["*://*/*", "*://duckduckgo.com/*", "*://www.bing.com/*", "*://www.ecosia.org/*", "*://search.brave.com/*", "*://www.google.com/*", "*://www.google.ac/*", "*://www.google.ad/*", "*://www.google.ae/*", "*://www.google.com.af/*", "*://www.google.com.ag/*", "*://www.google.com.ai/*", "*://www.google.al/*", "*://www.google.am/*", "*://www.google.co.ao/*", "*://www.google.com.ar/*", "*://www.google.as/*", "*://www.google.at/*", "*://www.google.com.au/*", "*://www.google.az/*", "*://www.google.ba/*", "*://www.google.com.bd/*", "*://www.google.be/*", "*://www.google.bf/*", "*://www.google.bg/*", "*://www.google.com.bh/*", "*://www.google.bi/*", "*://www.google.bj/*", "*://www.google.com.bn/*", "*://www.google.com.bo/*", "*://www.google.com.br/*", "*://www.google.bs/*", "*://www.google.bt/*", "*://www.google.co.bw/*", "*://www.google.by/*", "*://www.google.com.bz/*", "*://www.google.ca/*", "*://www.google.com.kh/*", "*://www.google.cc/*", "*://www.google.cd/*", "*://www.google.cf/*", "*://www.google.cat/*", "*://www.google.cg/*", "*://www.google.ch/*", "*://www.google.ci/*", "*://www.google.co.ck/*", "*://www.google.cl/*", "*://www.google.cm/*", "*://www.google.cn/*", "*://www.google.com.co/*", "*://www.google.co.cr/*", "*://www.google.com.cu/*", "*://www.google.cv/*", "*://www.google.com.cy/*", "*://www.google.cz/*", "*://www.google.de/*", "*://www.google.dj/*", "*://www.google.dk/*", "*://www.google.dm/*", "*://www.google.com.do/*", "*://www.google.dz/*", "*://www.google.com.ec/*", "*://www.google.ee/*", "*://www.google.com.eg/*", "*://www.google.es/*", "*://www.google.com.et/*", "*://www.google.fi/*", "*://www.google.com.fj/*", "*://www.google.fm/*", "*://www.google.fr/*", "*://www.google.ga/*", "*://www.google.ge/*", "*://www.google.gf/*", "*://www.google.gg/*", "*://www.google.com.gh/*", "*://www.google.com.gi/*", "*://www.google.gl/*", "*://www.google.gm/*", "*://www.google.gp/*", "*://www.google.gr/*", "*://www.google.com.gt/*", "*://www.google.gy/*", "*://www.google.com.hk/*", "*://www.google.hn/*", "*://www.google.hr/*", "*://www.google.ht/*", "*://www.google.hu/*", "*://www.google.co.id/*", "*://www.google.iq/*", "*://www.google.ie/*", "*://www.google.co.il/*", "*://www.google.im/*", "*://www.google.co.in/*", "*://www.google.io/*", "*://www.google.is/*", "*://www.google.it/*", "*://www.google.je/*", "*://www.google.com.jm/*", "*://www.google.jo/*", "*://www.google.co.jp/*", "*://www.google.co.ke/*", "*://www.google.ki/*", "*://www.google.kg/*", "*://www.google.co.kr/*", "*://www.google.com.kw/*", "*://www.google.kz/*", "*://www.google.la/*", "*://www.google.com.lb/*", "*://www.google.com.lc/*", "*://www.google.li/*", "*://www.google.lk/*", "*://www.google.co.ls/*", "*://www.google.lt/*", "*://www.google.lu/*", "*://www.google.lv/*", "*://www.google.com.ly/*", "*://www.google.co.ma/*", "*://www.google.md/*", "*://www.google.me/*", "*://www.google.mg/*", "*://www.google.mk/*", "*://www.google.ml/*", "*://www.google.com.mm/*", "*://www.google.mn/*", "*://www.google.ms/*", "*://www.google.com.mt/*", "*://www.google.mu/*", "*://www.google.mv/*", "*://www.google.mw/*", "*://www.google.com.mx/*", "*://www.google.com.my/*", "*://www.google.co.mz/*", "*://www.google.com.na/*", "*://www.google.ne/*", "*://www.google.com.nf/*", "*://www.google.com.ng/*", "*://www.google.com.ni/*", "*://www.google.nl/*", "*://www.google.no/*", "*://www.google.com.np/*", "*://www.google.nr/*", "*://www.google.nu/*", "*://www.google.co.nz/*", "*://www.google.com.om/*", "*://www.google.com.pk/*", "*://www.google.com.pa/*", "*://www.google.com.pe/*", "*://www.google.com.ph/*", "*://www.google.pl/*", "*://www.google.com.pg/*", "*://www.google.pn/*", "*://www.google.com.pr/*", "*://www.google.ps/*", "*://www.google.pt/*", "*://www.google.com.py/*", "*://www.google.com.qa/*", "*://www.google.ro/*", "*://www.google.rs/*", "*://www.google.ru/*", "*://www.google.rw/*", "*://www.google.com.sa/*", "*://www.google.com.sb/*", "*://www.google.sc/*", "*://www.google.se/*", "*://www.google.com.sg/*", "*://www.google.sh/*", "*://www.google.si/*", "*://www.google.sk/*", "*://www.google.com.sl/*", "*://www.google.sn/*", "*://www.google.sm/*", "*://www.google.so/*", "*://www.google.st/*", "*://www.google.sr/*", "*://www.google.com.sv/*", "*://www.google.td/*", "*://www.google.tg/*", "*://www.google.co.th/*", "*://www.google.com.tj/*", "*://www.google.tk/*", "*://www.google.tl/*", "*://www.google.tm/*", "*://www.google.to/*", "*://www.google.tn/*", "*://www.google.com.tr/*", "*://www.google.tt/*", "*://www.google.com.tw/*", "*://www.google.co.tz/*", "*://www.google.com.ua/*", "*://www.google.co.ug/*", "*://www.google.co.uk/*", "*://www.google.com/*", "*://www.google.com.uy/*", "*://www.google.co.uz/*", "*://www.google.com.vc/*", "*://www.google.co.ve/*", "*://www.google.vg/*", "*://www.google.co.vi/*", "*://www.google.com.vn/*", "*://www.google.vu/*", "*://www.google.ws/*", "*://www.google.co.za/*", "*://www.google.co.zm/*", "*://www.google.co.zw/*"], "resources": ["engines.json", "styles/*", "sites/icons/*"]}]}, "path": "dimkapigjeiopninbepgaoobcnbjmgik\\1.2_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "ehlmnljdoejdahfjdfobmpfancoibmig": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "epdpgaljfdjmcemiaplofbiholoaepem": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": ["https://account.live.com/Consent/Update?*", "https://login.live.com/oauth20_authorize.srf?*"]}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_scripts": [{"js": ["js/check_page_and_click_button.js"], "matches": ["https://account.live.com/Consent/Update?*", "https://login.live.com/oauth20_authorize.srf?*"], "run_at": "document_start"}], "description": "Suppress consent prompt for quick authentication", "key": "AAAAB3NzaC1yc2EAAAADAQABAAAAgQDIUsSOQUI35YGFiytHV/QeV1LMMeiAQY6RMkY2nuE1yZlZn9Q7uKWK6t87FSBhijLpY7MUK9U0TNJY8ugOmR3ysYF+TOUP8AxvY0y/kop/Cg5TKPBcILZaEm+IsM/xYBhawnDFbJWS+k4SY97ERpd64WO4uiUcmjrYL53zl8inTQ==", "manifest_version": 3, "name": "Suppress Consent Prompt", "version": "1.0.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\edge_suppress_consent_prompt", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fhkhedgjlkcgdaldodbhmcldjglpabmf": {"account_extension_type": 2, "active_bit": false, "active_permissions": {"api": ["activeTab", "contextMenus", "storage", "system.display", "tabs", "sidePanel"], "explicit_host": ["*://*/*"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "allowlist": 1, "commands": {"open-bot": {"suggested_key": "Ctrl+Shift+K"}}, "content_settings": [], "creation_flags": 9, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": true, "granted_permissions": {"api": ["activeTab", "contextMenus", "storage", "system.display", "tabs", "sidePanel"], "explicit_host": ["*://*/*"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"128": "icons/icon.png", "16": "icons/icon.png", "48": "icons/icon.png"}, "default_title": "打开侧边栏"}, "background": {"service_worker": "js/background.js"}, "commands": {"open-bot": {"description": "打开侧边栏", "suggested_key": {"default": "Ctrl+Shift+K", "mac": "Command+K"}}}, "content_scripts": [{"js": ["js/content.js"], "matches": ["<all_urls>"]}], "description": "百度AI助手是您在任何页面上的个人AI助手，可以帮助你快速阅读，为你提供创意灵感，在聊天对话中为你答疑解惑。", "host_permissions": ["*://*/*"], "icons": {"128": "icons/icon.png", "16": "icons/icon.png", "48": "icons/icon.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAn/a/0Jp7/CTBGMZlPb7m+nl4QbJMQB2EbDt4RCXli11Q+zJqx6sVT0EUVM5qTPJNIEqVzzd5cbdhSch2JDSaGIo8Qqmv+zSYbN7eyuXuJPfhfgGQnajnJ9Gihm3wlpMJA2c9nCCqC1fB/MCcxWITs7LUiLei5GWUsEwyAcnVgUDkZMMclZUzWPrT7OP8YrEmrS1hsLe11lHHriMO20ioCzZHenca0I0fH9hmA6So8LWCC4YRLGnllIRWbir/gm3Q+mhTYndK9Vxc9Nkrrm1CgO/pJdYX9o+6PuAKxPAthnroy/ThbUmLFly3l+G/uBNV+6e4urPZZUX584/Gs3SJ6wIDAQAB", "manifest_version": 3, "name": "百度AI—浏览器助手", "options_page": "setting.html", "permissions": ["sidePanel", "contextMenus", "tabs", "activeTab", "storage", "windows", "system.display"], "side_panel": {"default_icon": {"128": "icons/icon.png", "16": "icons/icon.png", "48": "icons/icon.png"}, "default_path": "index-extension.html"}, "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.0.25", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["wise-icon/*", "pc-icon/*", "css/*", "js/*"]}]}, "path": "fhkhedgjlkcgdaldodbhmcldjglpabmf\\1.0.25_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "fikbjbembnmfhppjfnmfkahdhfohhjmg": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.microsoftstream.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsAmDrYmQaYQlLxSAn/jTQTGNt1IffJGIJeKucE/B42d8QIyFD2RCarmHP1bmbY1YuTng2dL3J//qyvUNwXPt9cmxH9WKwi512tzOa5r2zYaCuOgP2vAIrah/bKnpO3XmUfFWj+LRcbZahOmMDMQxzPKxFKuIz2eOiakBXDE6Ok7azHJ13LLQTte1JgZIPmyFrAciPABLp/IKLfsfnebVW1YgaOyxBNyp/7bhSmoyZI3kBv8InKOpGE8pttrBg6l5zkvD67a7ViNAYkqZIpJJV5ZTQtVWCWSG0xU2y+3zXZtx8KbGbDiWUAcwNYDVPpsV+IQXVpgAplHvrZme+hAl6QIDAQAB", "manifest_version": 2, "name": "Media Internals Services Extension", "permissions": ["mediaInternalsPrivate"], "version": "2.0.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\media_internals_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fjngpfnaikknjdhkckmncgicobbkcnle": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "flahobhjikkpnpohomeckhdjjkkkkmoc": {"account_extension_type": 2, "active_permissions": {"api": ["storage", "declarativeNetRequestWithHostAccess"], "explicit_host": ["*://*/*", "<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://app.maxai.me/*", "https://bard.google.com/*", "https://chat.openai.com/*", "https://chatgpt.com/*", "https://chatgpt.com/favicon.ico", "https://claude.ai/*", "https://cn.bing.com/*", "https://duckduckgo.com/*", "https://gemini.google.com/*", "https://search.brave.com/*", "https://search.naver.com/*", "https://search.yahoo.com/search*", "https://twitter.com/*", "https://wap.yandex.com/*", "https://www.amazon.com/s/*", "https://www.baidu.com/*", "https://www.bing.com/*", "https://www.google.com/*", "https://www.maxai.co/*", "https://www.perplexity.ai/*", "https://www.reddit.com/*", "https://www.sogou.com/*", "https://www.youtube.com/*", "https://yandex.com/*"]}, "commands": {"toggle-web-access": {"suggested_key": "Alt+W", "was_assigned": true}}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "dnr_static_ruleset": {"1": {"checksum": 1986079478, "ignore_ruleset": false}}, "edge_last_update_check_time": "13398082015773693", "first_install_time": "13398073247350273", "from_webstore": false, "granted_permissions": {"api": ["storage", "declarativeNetRequestWithHostAccess"], "explicit_host": ["*://*/*", "<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://app.maxai.me/*", "https://bard.google.com/*", "https://chat.openai.com/*", "https://chatgpt.com/*", "https://chatgpt.com/favicon.ico", "https://claude.ai/*", "https://cn.bing.com/*", "https://duckduckgo.com/*", "https://gemini.google.com/*", "https://search.brave.com/*", "https://search.naver.com/*", "https://search.yahoo.com/search*", "https://twitter.com/*", "https://wap.yandex.com/*", "https://www.amazon.com/s/*", "https://www.baidu.com/*", "https://www.bing.com/*", "https://www.google.com/*", "https://www.maxai.co/*", "https://www.perplexity.ai/*", "https://www.reddit.com/*", "https://www.sogou.com/*", "https://www.youtube.com/*", "https://yandex.com/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398073247350273", "lastpingday": "*****************", "location": 1, "manifest": {"action": {}, "background": {"service_worker": "background/bg.js", "type": "module"}, "commands": {"toggle-web-access": {"description": "切换网页访问", "suggested_key": {"default": "Alt+W"}}}, "content_scripts": [{"js": ["import_mainUI.js"], "matches": ["https://chat.openai.com/*", "https://chatgpt.com/*", "https://claude.ai/*", "https://bard.google.com/*", "https://gemini.google.com/*"]}, {"all_frames": true, "js": ["import_proxySearchInject.js"], "matches": ["https://chatgpt.com/*", "https://www.google.com/*", "https://www.perplexity.ai/*", "https://www.baidu.com/*", "https://www.bing.com/*", "https://cn.bing.com/*"], "run_at": "document_start"}, {"js": ["import_authClient.js"], "matches": ["https://app.maxai.me/*", "https://www.maxai.co/*"]}, {"js": ["import_theSearchItem.js"], "matches": ["<all_urls>"]}, {"js": ["import_searchWithAI.js"], "matches": ["https://www.google.com/*", "https://www.baidu.com/*", "https://cn.bing.com/*", "https://www.bing.com/*", "https://www.sogou.com/*", "https://duckduckgo.com/*", "https://search.yahoo.com/search*", "https://search.naver.com/*", "https://yandex.com/*", "https://wap.yandex.com/*", "https://search.brave.com/*", "https://www.reddit.com/*", "https://twitter.com/*", "https://www.youtube.com/*", "https://www.amazon.com/s/*"], "run_at": "document_end"}, {"js": ["import_requesterInject.js"], "matches": ["<all_urls>"]}, {"all_frames": true, "js": ["import_contentArkoseTokenIframe.js"], "match_about_blank": true, "matches": ["https://chatgpt.com/favicon.ico"], "run_at": "document_start"}], "current_locale": "zh_CN", "declarative_net_request": {"rule_resources": [{"enabled": true, "id": "ruleset_bing", "path": "rules/bing.json"}]}, "default_locale": "en", "description": "通过网络浏览增强您的ChatGPT提示与相关的网络搜索结果。", "host_permissions": ["*://*/*", "<all_urls>"], "icons": {"128": "assets/icons/icon128.png", "16": "assets/icons/icon16.png", "48": "assets/icons/icon48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA12wxe+ZXDlzpUxkpbsUEn1iWl7eSYNgZKw9xzuQ+thzEPfi17OoZeaXgi1HgOfqUsZFe2+vDb9EW6zuUEaI34D3+Y7lIMI2rvBfi1ptU+fOHXfQiCVQXTTZxRYRE+mxS0KgLw3daiu6t3YDfE/E9N7viyxCys7EE3+BoK3hfC0/BjJEmCPY4WHf8b52wK+1tjrJckgYBFcHYLL+CennmVL6JJn4dB3T4MaNNQupa9pDaAoy7eaaxUDdIJofoHRbK8ez9ST0IPx9QOOTHSMsRRvYWo0vbCoOJNdrkkvivJ/CV4ESCxmORSUCt1FwMdJ9+JlZP1xh2RgxMg59IZxtDMQIDAQAB", "manifest_version": 3, "name": "WebChatGPT: ChatGPT 具备互联网访问功能", "options_ui": {"open_in_tab": true, "page": "pages/options/options.html"}, "permissions": ["storage", "declarativeNetRequestWithHostAccess"], "short_name": "WebChatGPT", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "4.1.61", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["contentScripts/*", "chunks/*.js", "i18n/locales/*", "assets/*", "pages/*"]}]}, "path": "flahobhjikkpnpohomeckhdjjkkkkmoc\\4.1.61_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "gbihlnbpmfkodghomcinpblknjhneknc": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbmoeijgfngecijpcnbooedokgafmmji": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gcinnojdebelpnodghnoicmcdmamjoch": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gdmpgjhgjlgmnjjcebofmbkimgkinjbo": {"account_extension_type": 2, "active_permissions": {"api": ["tabs", "webRequest", "webRequestBlocking"], "explicit_host": ["http://*/*", "https://*/*"], "manifest_permissions": [], "scriptable_host": ["http://*/*", "https://*/*"]}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [], "edge_last_update_check_time": "*****************", "events": [], "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["tabs", "webRequest", "webRequestBlocking"], "explicit_host": ["http://*/*", "https://*/*"], "manifest_permissions": [], "scriptable_host": ["http://*/*", "https://*/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"background": {"persistent": true, "scripts": ["js/background.js"]}, "browser_action": {"default_icon": {"128": "icons/extension-icon-x128.png", "16": "icons/extension-icon-x16.png", "32": "icons/extension-icon-x32.png", "48": "icons/extension-icon-x48.png"}}, "content_scripts": [{"all_frames": false, "css": ["css/all.css"], "js": ["js/all.js"], "matches": ["http://*/*", "https://*/*"]}], "description": "速翻译是一款优秀高效的网页翻译工具，支持自动识别翻译，中文会翻译成英文，英文会翻译成中文，支持划词搜索。", "icons": {"128": "icons/extension-icon-x128.png", "16": "icons/extension-icon-x16.png", "32": "icons/extension-icon-x32.png", "48": "icons/extension-icon-x48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0lwenyN39F3oy+oI95w0x0vBZHRfqWVmZhXFx2LaCx/z2XWKYUF/AzqoiqJzt5WugXO6YVl5gZIwGxYsnikZR+5+hLb+A21wTOMVIXswd8pJrsLIJrbIPuHtDi514Gqgt+nLSpf1F3BrxTXhG7zdY6d4rv43fFgdowFfFy5v8OM6isPSdUs0o5ma6UuxM7wCkfK1/Nelw0KvBWSNXNgKZ3pN6q4xwbJ4ICLrPXxNQfTyARMftkeUp/2hwgaSc1EIxLD9im2Qj5ZNQoilBv9p+E4FyHdwIbSvxGasz32/cj12URnx3z5//6XrdgzUUF26UQU1Nazfe7p9PQAUJA+pgwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "60", "name": "速翻译", "options_ui": {"open_in_tab": true, "page": "options.html"}, "permissions": ["tabs", "http://*/*", "https://*/*", "webRequest", "webRequestBlocking"], "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "1.0.0", "web_accessible_resources": ["icons/*", "images/*", "fonts/*"]}, "path": "gdmpgjhgjlgmnjjcebofmbkimgkinjbo\\1.0.0_0", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "gecfnmoodchdkebjjffmdcmeghkflpib": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "geieijlhpiifjlffnldmnnjbebmpjgmk": {"lastpingday": "*****************"}, "ghbmnnjooekpmoecnnnilnnbdlolhkhi": {"account_extension_type": 0, "ack_external": true, "active_permissions": {"api": ["alarms", "storage", "unlimitedStorage", "offscreen"], "explicit_host": ["https://docs.google.com/*", "https://drive.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1048713, "disable_reasons": [*********], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": true, "granted_permissions": {"api": ["alarms", "storage", "unlimitedStorage", "offscreen"], "explicit_host": ["https://docs.google.com/*", "https://drive.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 6, "manifest": {"author": {"email": "<EMAIL>"}, "background": {"service_worker": "service_worker_bin_prod.js"}, "content_capabilities": {"matches": ["https://docs.google.com/*", "https://drive.google.com/*", "https://drive-autopush.corp.google.com/*", "https://drive-daily-0.corp.google.com/*", "https://drive-daily-1.corp.google.com/*", "https://drive-daily-2.corp.google.com/*", "https://drive-daily-3.corp.google.com/*", "https://drive-daily-4.corp.google.com/*", "https://drive-daily-5.corp.google.com/*", "https://drive-daily-6.corp.google.com/*", "https://drive-preprod.corp.google.com/*", "https://drive-staging.corp.google.com/*"], "permissions": ["clipboardRead", "clipboardWrite", "unlimitedStorage"]}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "current_locale": "zh_CN", "default_locale": "en_US", "description": "编辑、创建和查看文档、电子表格和演示文稿，无需连接互联网。", "externally_connectable": {"matches": ["https://docs.google.com/*", "https://drive.google.com/*", "https://drive-autopush.corp.google.com/*", "https://drive-daily-0.corp.google.com/*", "https://drive-daily-1.corp.google.com/*", "https://drive-daily-2.corp.google.com/*", "https://drive-daily-3.corp.google.com/*", "https://drive-daily-4.corp.google.com/*", "https://drive-daily-5.corp.google.com/*", "https://drive-daily-6.corp.google.com/*", "https://drive-preprod.corp.google.com/*", "https://drive-staging.corp.google.com/*"]}, "host_permissions": ["https://docs.google.com/*", "https://drive.google.com/*"], "icons": {"128": "128.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnF7RGLAxIon0/XeNZ4MLdP3DMkoORzEAKVg0sb89JpA/W2osTHr91Wqwdc9lW0mFcSpCYS9Y3e7cUMFo/M2ETASIuZncMiUzX2/0rrWtGQ3UuEj3KSe5PdaVZfisyJw/FebvHwirEWrhqcgzVUj9fL9YjE0G45d1zMKcc1umKvLqPyTznNuKBZ9GJREdGLRJCBmUgCkI8iwtwC+QZTUppmaD50/ksnEUXv+QkgGN07/KoNA5oAgo49Jf1XBoMv4QXtVZQlBYZl84zAsI82hb63a6Gu29U/4qMWDdI7+3Ne5TRvo6Zi3EI4M2NQNplJhik105qrz+eTLJJxvf4slrWwIDAQAB", "manifest_version": 3, "minimum_chrome_version": "88", "name": "Google 文档的离线功能", "permissions": ["alarms", "storage", "unlimitedStorage", "offscreen"], "storage": {"managed_schema": "dasherSettingSchema.json"}, "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.94.1", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["page_embed_script.js"]}]}, "path": "ghbmnnjooekpmoecnnnilnnbdlolhkhi\\1.94.1_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": true, "was_installed_by_oem": false, "withholding_permissions": false}, "hfmgbegjielnmfghmoohgmplnpeehike": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "iglcjdemknebjbklcgkfaebgojjphkec": {"account_extension_type": 0, "active_permissions": {"api": ["identity", "management", "metricsPrivate", "webstorePrivate", "hubPrivate"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "w", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://microsoftedge.microsoft.com"}, "urls": ["https://microsoftedge.microsoft.com"]}, "description": "发现 Microsoft Edge 扩展。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtMvN4+y6cd3el/A/NT5eUnrz1WiD1WJRaJfMBvaMtJHIuFGEmYdYL/YuE74J19+pwhjOHeFZ3XUSMTdOa5moOaXXvdMr5wWaaN2frHewtAnNDO64NGbbZvdsfGm/kRkHKVGNV6dacZsAkylcz5CkwTmq97wOZ7ETaShHvhZEGwRQIt4K1poxurOkDYQw9ERZNf3fgYJ9ZTrLZMAFDLJY+uSF03pClWrr8VGc8LUQ4Naktb8QSgVUlrS14AdF/ESdbhnTvvdB0e7peNWRyoNtCqLJsbtTtBL6sOnqfusnwPowuueOFI+XskOT9TvLo6PcgxhLX5+d0mM+Jtn6PFTU8QIDAQAB", "name": "Microsoft Store", "permissions": ["webstorePrivate", "management", "metricsPrivate", "identity", "hubPrivate"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\microsoft_web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ihmafllikibpmigkcoadcmckbfhibefp": {"account_extension_type": 0, "active_permissions": {"api": ["debugger", "feedbackPrivate", "fileSystem", "fileSystem.write", "app.window.fullscreen", "metricsPrivate", "storage", "tabs", "fileSystem.readFullPath", "edgeInternetConnectivityPrivate"], "explicit_host": ["edge://resources/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["edgeFeedbackPrivate.onFeedbackRequested"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"background": {"scripts": ["js/event_handler.js"]}, "content_security_policy": "default-src 'none'; script-src 'self' blob: filesystem: chrome://resources; style-src 'unsafe-inline' blob: chrome: file: filesystem: data: *; img-src * blob: chrome: file: filesystem: data:; media-src 'self' blob: filesystem:; connect-src data:"}, "description": "User feedback extension", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon128.png", "16": "images/icon16.png", "192": "images/icon192.png", "32": "images/icon32.png", "48": "images/icon48.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAl3vxWwvLjcMIFK4OfG6C8PmJkMhFYDKRnx+SqG23YlMG1A+bOkiNmAN1TWpFPPp1f2PpbiZGNq1y29u/QfkD+PC4bnO7GbNw/2X5tGoP0n2K+KGGAxhnr0ki/oyo2eiFGSTOXlQvTRo5q1vB+Lbg+9TbFsWKlHZyAkeZ/YGz/iijHTqw8Q4RWdl5Tp8SlUhS/92EsWhveNJLW22veaT/Up2iSeSSwfyoHVYy8LUPaD4fbyLvPQacVLJq1dac2bNDqjaNvSPgPWCnkZtDmawZrgxT53otLCES/e96xfAf8I24VHIc1pVP8LqdqKr1AV1Yxn93h3VJ2QejtEhIAWHU6QIDAQAB", "manifest_version": 2, "name": "<PERSON>", "permissions": ["chrome://resources/", "debugger", "edgeInternetConnectivityPrivate", "feedbackPrivate", {"fileSystem": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "write"]}, "fullscreen", "metricsPrivate", "storage", "windows"], "version": "*******"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\edge_feedback", "preferences": {}, "regular_only_preferences": {}, "running": false, "was_installed_by_default": false, "was_installed_by_oem": false}, "iikmkjmpaadaobahmlepeloendndfphd": {"account_extension_type": 2, "active_permissions": {"api": ["alarms", "clipboardWrite", "contextMenus", "cookies", "idle", "notifications", "storage", "tabs", "unlimitedStorage", "webNavigation", "webRequest", "scripting", "declarativeNetRequestWithHostAccess", "offscreen", "userScripts"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "commands": {"open-dashboard": {"suggested_key": ""}, "open-dashboard-with-running-scripts": {"suggested_key": ""}, "open-new-script": {"suggested_key": ""}, "toggle-enable": {"suggested_key": ""}}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["alarms", "clipboardWrite", "contextMenus", "cookies", "idle", "notifications", "storage", "tabs", "unlimitedStorage", "webNavigation", "webRequest", "scripting", "declarativeNetRequestWithHostAccess", "offscreen", "userScripts"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"16": "images/icon_grey16.png", "19": "images/icon_grey19.png", "24": "images/icon_grey24.png", "32": "images/icon_grey32.png", "38": "images/icon_grey38.png"}, "default_popup": "action.html", "default_title": "Tam<PERSON>mon<PERSON>"}, "background": {"service_worker": "background.js"}, "commands": {"open-dashboard": {"description": "Open dashboard"}, "open-dashboard-with-running-scripts": {"description": "Open dashboard with the current tab's URL used as filter"}, "open-new-script": {"description": "Open new script tab"}, "toggle-enable": {"description": "Toggle enable state"}}, "current_locale": "zh_CN", "default_locale": "en", "description": "使用用户脚本自由地改变网络", "host_permissions": ["<all_urls>"], "icons": {"128": "images/icon128.png", "32": "images/icon.png", "48": "images/icon48.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0P8sw/BsnXXss5WQ3BbkcHZ5br0AEJ8Ex267fYl5FuComXndOeqdPJFJvMEE6CDICfC1S6toTAEg+PDVIce68zfQarVpPb5hCVwbAbQCHwWK3pzLY/3CZZnhfIcL3Mh6FHPUxz4MyUEgN69wd/CBKb7LTv+mZVjjvPNgRafpnYc/0Dkplr0CxwUjZSNT+qO1h91Im5m9rLmVkCDUUG/P/EEYT6jf4PB17n29gTvpM80xbaj/dU+a0fTpDOKfw/ARXCYRBi67YnQJa9FZlIrtjPwSeTRR/C8xpXA+w03trmk8AnJbSYLWDGarMmZCUGKPT4fPPxtxxDtNK51tgCnZOwIDAQAB", "manifest_version": 3, "minimum_chrome_version": "120", "name": "篡改猴", "offline_enabled": true, "optional_permissions": ["downloads"], "options_page": "options.html", "options_ui": {"open_in_tab": true, "page": "options.html"}, "permissions": ["notifications", "unlimitedStorage", "tabs", "idle", "webNavigation", "webRequest", "webRequestBlocking", "storage", "contextMenus", "chrome://favicon/", "clipboardWrite", "cookies", "alarms", "declarativeNetRequestWithHostAccess", "scripting", "userScripts", "offscreen"], "short_name": "篡改猴", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "5.3.3"}, "path": "iikmkjmpaadaobahmlepeloendndfphd\\5.3.3_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "jbleckejnaboogigodiafflhkajdmpcl": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "jdiccldimpdaibmpdkjnbmckianbfold": {"account_extension_type": 0, "active_permissions": {"api": ["activeTab", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "errorReporting"], "explicit_host": ["https://*.bing.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["lifetimeHelper.js", "telemetryHelper.js", "errorHelper.js", "voiceList/voiceListRequester.js", "voiceList/voiceListSingleton.js", "voiceList/voiceModel.js", "manifestHelper.js", "config.js", "ssml.js", "uuid.js", "wordBoundary.js", "audioStreamer.js", "wordBoundaryEventManager.js", "audioViewModel.js", "background.js"]}, "description": "Provides access to Microsoft's online text-to-speech voices", "key": "AAAAB3NzaC1yc2EAAAADAQABAAAAgQDjGOAV6/3fmEtQmFqlmqm5cZ+jlNhd6XikwMDp0I7BKh+AjG3aBIG/qqwlsF/7LAGatnSxBwUwZC0qMnGXtcOPVl26Q8OvMx0gt5Va5gxca+ae0Skluj9WN9TNxPFVhw21WbCt4D9q3kb+XXDlx/7v1ktYus4Fwr/skkjADG9cIQ==", "manifest_version": 2, "name": "Microsoft Voices", "permissions": ["activeTab", "errorReporting", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "https://*.bing.com/"], "tts_engine": {"voices": [{"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)", "voice_name": "Microsoft Aria Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, GuyNeural)", "voice_name": "Microsoft Guy Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, XiaoxiaoNeural)", "voice_name": "Microsoft Xiaoxiao Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunyangNeural)", "voice_name": "Microsoft Yunyang Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-TW", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-TW, HanHanRUS)", "voice_name": "Microsoft HanHan Online - Chinese (Taiwan)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-HK", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-HK, TracyRUS)", "voice_name": "Microsoft Tracy Online - Chinese (Hong Kong)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ja-<PERSON>", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, NanamiNeural)", "voice_name": "Microsoft Nanami Online (Natural) - Japanese (Japan)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-GB", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-GB, LibbyNeural)", "voice_name": "Microsoft Libby Online (Natural) - English (United Kingdom)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pt-BR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pt-BR, FranciscaNeural)", "voice_name": "Microsoft Francisca Online (Natural) - Portuguese (Brazil)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-MX", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-MX, DaliaNeural)", "voice_name": "Microsoft Dalia Online (Natural) - Spanish (Mexico)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-IN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-IN, PriyaRUS)", "voice_name": "Microsoft Priya Online - English (India)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-CA, HeatherRUS)", "voice_name": "Microsoft Heather Online - English (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-CA, SylvieNeural)", "voice_name": "Microsoft Sylvie Online (Natural) - French (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-FR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, <PERSON>)", "voice_name": "Microsoft Denise Online (Natural) - French (France)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "de-DE", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (de-DE, KatjaNeural)", "voice_name": "Microsoft Katja Online (Natural) - German (Germany)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ru-RU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ru-RU, EkaterinaRUS)", "voice_name": "Microsoft Ekaterina Online - Russian (Russia)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-AU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-AU, HayleyRUS)", "voice_name": "Microsoft Hayley Online - English (Australia)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "it-IT", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (it-IT, ElsaNeural)", "voice_name": "Microsoft Elsa Online (Natural) - Italian (Italy)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ko-KR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ko-KR, SunHiNeural)", "voice_name": "Microsoft SunHi Online (Natural) - Korean (Korea)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "nl-NL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (nl-NL, HannaRUS)", "voice_name": "Microsoft Hanna Online - Dutch (Netherlands)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-ES", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-ES, ElviraNeural)", "voice_name": "Microsoft Elvira Online (Natural) - Spanish (Spain)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "tr-TR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (tr-TR, EmelNeural)", "voice_name": "Microsoft Emel Online (Natural) - Turkish (Turkey)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pl-PL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pl-PL, PaulinaRUS)", "voice_name": "Microsoft Paulina Online - Polish (Poland)"}]}, "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\microsoft_voices", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "jmjflgjpcpepeafmmgdpfkogkghcpiha": {"account_extension_type": 0, "ack_external": true, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"]}, "commands": {}, "content_settings": [], "creation_flags": 8321, "disable_reasons": [], "edge_last_update_check_time": "*****************", "events": [], "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 10, "manifest": {"content_scripts": [{"js": ["content.js"], "matches": ["https://chrome.google.com/webstore/*"]}, {"js": ["content_new.js"], "matches": ["https://chromewebstore.google.com/*"]}], "description": "Edge relevant text changes on select websites to improve user experience and precisely surfaces the action they want to take.", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu06p2Mjoy6yJDUUjCe8Hnqvtmjll73XqcbylxFZZWe+MCEAEK+1D0Nxrp0+IuWJL02CU3jbuR5KrJYoezA36M1oSGY5lIF/9NhXWEx5GrosxcBjxqEsdWv/eDoOOEbIvIO0ziMv7T1SUnmAA07wwq8DXWYuwlkZU/PA0Mxx0aNZ5+QyMfYqRmMpwxkwPG8gyU7kmacxgCY1v7PmmZo1vSIEOBYrxl064w5Q6s/dpalSJM9qeRnvRMLsszGY/J2bjQ1F0O2JfIlBjCOUg/89+U8ZJ1mObOFrKO4um8QnenXtH0WGmsvb5qBNrvbWNPuFgr2+w5JYlpSQ+O8zUCb8QZwIDAQAB", "manifest_version": 3, "name": "Edge relevant text changes", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "1.2.1"}, "path": "jmjflgjpcpepeafmmgdpfkogkghcpiha\\1.2.1_0", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": true, "was_installed_by_oem": false}, "kdlmggoacmfoombiokflpeompajfljga": {"account_extension_type": 2, "active_permissions": {"api": ["storage", "unlimitedStorage", "declarativeNetRequestWithHostAccess", "sidePanel"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "commands": {"open-app": {"suggested_key": "Alt+J"}}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1, 2], "dnr_static_ruleset": {"1": {"checksum": *********, "ignore_ruleset": false}, "2": {"checksum": *********, "ignore_ruleset": false}, "3": {"checksum": *********, "ignore_ruleset": false}, "4": {"checksum": **********, "ignore_ruleset": false}, "5": {"checksum": *********, "ignore_ruleset": false}, "6": {"checksum": **********, "ignore_ruleset": false}, "7": {"checksum": *********, "ignore_ruleset": false}, "8": {"checksum": **********, "ignore_ruleset": false}}, "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {}, "background": {"service_worker": "service-worker-loader.js", "type": "module"}, "commands": {"open-app": {"description": "Open ChatHub app", "suggested_key": {"default": "Alt+J", "linux": "Alt+J", "mac": "Command+J", "windows": "Alt+J"}}}, "current_locale": "zh_CN", "declarative_net_request": {"rule_resources": [{"enabled": true, "id": "ruleset_x_frame_options", "path": "src/rules/x-frame-options.json"}, {"enabled": true, "id": "ruleset_chatgpt", "path": "src/rules/chatgpt.json"}, {"enabled": true, "id": "ruleset_bing", "path": "src/rules/bing.json"}, {"enabled": true, "id": "ruleset_ddg", "path": "src/rules/ddg.json"}, {"enabled": true, "id": "ruleset_qianwen", "path": "src/rules/qianwen.json"}, {"enabled": true, "id": "ruleset_ollama", "path": "src/rules/ollama.json"}, {"enabled": true, "id": "ruleset_pplx", "path": "src/rules/pplx.json"}, {"enabled": true, "id": "ruleset_anthropic", "path": "src/rules/anthropic.json"}]}, "default_locale": "en", "description": "同时使用<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>和更多机器人", "host_permissions": ["<all_urls>"], "icons": {"128": "src/assets/icon.png", "16": "src/assets/icon.png", "32": "src/assets/icon.png", "48": "src/assets/icon.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAos6UBxsKjXHiMNnRNz7xV12cCJWbFq5qA5r1GBHSmyOECbb2/cNqCOAvsin/KLqW6A6jiFcckJR4lqM97Z5pXIYy/Bh9/cEpF4+g1lHK0dzW717B6ba2cS3gIq4NDNg1wGPXI/9SqrmHGFYPBuEH/m384fqrtcFE888v1pY9zHb7mMGL3O/axQPHda/ntGcHHBVSkwKk0FDlmTRYWtTp8zTO2mvMV0EXCnqwLZVHzdlutjMTjG1AZYXfSUwxTWQs4ljpSpJCbV5PgFvSczKynUgsyt5br+2qSdxg6XM5h1ypL6i6Yr/zttuL1SKhfy7Yw2qwgkevrOs0ApNoBbTAOQIDAQAB", "manifest_version": 3, "name": "ChatHub - GPT-4, <PERSON>, <PERSON>同时用", "permissions": ["storage", "unlimitedStorage", "sidePanel", "declarativeNetRequestWithHostAccess"], "side_panel": {"default_path": "sidepanel.html"}, "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "3.90.0"}, "path": "kdlmggoacmfoombiokflpeompajfljga\\3.90.0_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "kfihiegbjaloebkmglnjnljoljgkkchm": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "kmhegedbanhfblnoboomoeafpmojfdlp": {"account_extension_type": 2, "active_permissions": {"api": ["clipboardWrite", "contextMenus", "storage", "tabs", "scripting"], "explicit_host": ["*://*/*"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://*.getliner.com/pdf/checksum/*", "https://getliner.com/pdf/checksum/*", "https://www.youtube-nocookie.com/embed/*", "https://www.youtube.com/embed/*", "https://www.youtube.com/watch*"]}, "commands": {"toggle-side-panel": {"suggested_key": "Ctrl+I", "was_assigned": true}}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["clipboardWrite", "contextMenus", "storage", "tabs", "scripting"], "explicit_host": ["*://*/*"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://*.getliner.com/pdf/checksum/*", "https://getliner.com/pdf/checksum/*", "https://www.youtube-nocookie.com/embed/*", "https://www.youtube.com/embed/*", "https://www.youtube.com/watch*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"128": "/images/icon/icon-saved-128.png", "16": "/images/icon/icon-saved-16.png", "32": "/images/icon/icon-saved-32.png", "48": "/images/icon/icon-saved-48.png"}, "default_title": "Save to Liner"}, "background": {"service_worker": "backgrounds.js"}, "commands": {"toggle-side-panel": {"description": "Open/Close Copilot side panel", "suggested_key": "Ctrl+I"}}, "content_scripts": [{"exclude_matches": ["https://www.youtube.com/watch*"], "js": ["/liner-core.be.js"], "matches": ["<all_urls>"], "run_at": "document_start"}, {"all_frames": true, "exclude_matches": ["https://www.youtube.com/embed/?*", "https://www.youtube.com/embed?*"], "js": ["/liner-core.be.js"], "matches": ["https://www.youtube.com/watch*", "https://www.youtube-nocookie.com/embed/*", "https://www.youtube.com/embed/*"], "run_at": "document_start"}, {"css": ["/pdfCSS.css"], "matches": ["https://getliner.com/pdf/checksum/*", "https://*.getliner.com/pdf/checksum/*"]}], "current_locale": "zh_CN", "default_locale": "en", "description": "直接在页面上使用 ChatGPT，甚至在 YouTube 上！添加 AI Copilot，与您的个人 AI 助手/AI 伴侣一起完成更多工作。", "homepage_url": "https://getliner.com", "host_permissions": ["*://*/*"], "icons": {"128": "/images/icon/icon-saved-128.png", "16": "/images/icon/icon-saved-16.png", "32": "/images/icon/icon-saved-32.png", "48": "/images/icon/icon-saved-48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz1nnDPRjJksERHG40ojyWnMsgQ4XLU+0w8DxxArsYgVKGni9ZPZbpa1QnxAbAuGGxWLmR+mhnbFjvhrCo3nwL8eVbIfMpFJIR3GiPG+fIORby0i3KA7gC9dboRkJy4hZJ9sxcbhmFLIMiOG/1fU/Hq8Y+LPNXDg9FXVkpQLm5yujUFC/Jmv5jPGBtfpdkhI/300GU14HZ6ldNG5+YBgxq32wD7nFGWFujzTpb91Ep4hoKdpuso00oMSpphxpgQrswlezri1htyPjxVnGEzq8TuR+3HL9UXL1YDqRLUY8W49dEeeMjQuswpUUA6IgFADbbtwTa7SlYXNkJOhs5TOLKwIDAQAB", "manifest_version": 3, "name": "Liner ChatGPT：Web&YouTube的AI副驾驶", "options_page": "/options/options.html", "permissions": ["tabs", "clipboardWrite", "storage", "scripting", "contextMenus"], "short_name": "Liner", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "7.17.1", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["/fonts/ABCArizonaFlare-Regular.woff2"]}]}, "path": "kmhegedbanhfblnoboomoeafpmojfdlp\\7.17.1_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "liilgpjgabokdklappibcjfablkpcekh": {"account_extension_type": 2, "active_permissions": {"api": ["activeTab", "alarms", "clipboardWrite", "contextMenus", "cookies", "downloads", "notifications", "storage", "tabs", "unlimitedStorage", "webRequest", "declarativeNetRequest", "scripting", "offscreen", "userScripts"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "************78573", "from_webstore": false, "granted_permissions": {"api": ["activeTab", "alarms", "clipboardWrite", "contextMenus", "cookies", "downloads", "notifications", "storage", "tabs", "unlimitedStorage", "webRequest", "declarativeNetRequest", "scripting", "offscreen", "userScripts"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "************78573", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"128": "assets/logo.png"}, "default_popup": "src/popup.html"}, "author": "CodFrm", "background": {"service_worker": "src/service_worker.js"}, "current_locale": "zh_CN", "default_locale": "en", "description": "万物皆可脚本化，让你的浏览器可以做更多的事情！", "host_permissions": ["<all_urls>"], "icons": {"128": "assets/logo.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtsWIPxfRpoykop0oqVrpcqPyV/6j0GwCQ6v6LJxgSd3z8wsix9PgpvkRc4aiU0Bjp2gR/epYCcd+HZ0BzCSmr03B4XgO0QW4Mt505VY0ZuhqIjnXWGFFkgLRij0Z/kb3l42a9eW8agDJ8AOxz4AI0w6vJ5qyEaf+2XLc0vgghCB6rFxU/uR1PrDNXyO05WltumuP2Xh0Mmp/BlFDr3kUqVU7+u+yxT4zErcFrYkCVuSqlXh2XzEB0QpBqk/RFRS5tENJv2drJcsBySv4QENeGP0lyYG1kYmiOzuNiFkkNfdiQBaJk3r+1i5MJM8cSE+N3ufTJabXAq6D8xp6EnDetQIDAQAB", "manifest_version": 3, "name": "脚本猫", "options_ui": {"open_in_tab": true, "page": "src/options.html"}, "permissions": ["tabs", "alarms", "storage", "cookies", "offscreen", "scripting", "downloads", "activeTab", "webRequest", "userScripts", "contextMenus", "notifications", "clipboardWrite", "unlimitedStorage", "declarativeNetRequest"], "sandbox": {"pages": ["src/sandbox.html"]}, "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "0.18.2"}, "path": "liilgpjgabokdklappibcjfablkpcekh\\0.18.2_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "ljlelhlcghfgkhckkmplmbgfaajlmemo": {"account_extension_type": 2, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": ["*://chaoshi.detail.tmall.com/*", "*://detail.tmall.com/*", "*://detail.tmall.hk/*", "*://e.jd.com/*", "*://i-item.jd.com/*", "*://item.jd.com/*", "*://item.taobao.com/*", "*://npcitem.jd.hk/*"]}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": ["*://chaoshi.detail.tmall.com/*", "*://detail.tmall.com/*", "*://detail.tmall.hk/*", "*://e.jd.com/*", "*://i-item.jd.com/*", "*://item.jd.com/*", "*://item.taobao.com/*", "*://npcitem.jd.hk/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"background": {"page": "background.html", "persistent": true}, "browser_action": {"default_icon": "128.png", "default_title": "券多多"}, "content_scripts": [{"css": ["main.css"], "js": ["jquery.bundle.js", "script.bundle.js"], "matches": ["*://item.jd.com/*", "*://i-item.jd.com/*", "*://e.jd.com/*", "*://npcitem.jd.hk/*", "*://item.taobao.com/*", "*://chaoshi.detail.tmall.com/*", "*://detail.tmall.com/*", "*://detail.tmall.hk/*"]}], "content_security_policy": "script-src 'self' 'unsafe-eval'; object-src 'self'", "description": "当您在京东或者淘宝浏览商品详情页时，为您获取该商品的优惠券，领取优惠券后下单立减。", "icons": {"128": "128.png", "16": "16.png", "48": "48.png", "96": "96.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyJoVNnrsOI5rxOj1zhB2RZI5KWiIHQ7fjIRI27RRAL+7dB6uEQypmYnPuw2Rf6EcSaM9CGi6D63rDYbZdXsjt4ZwKBlehHHjzjtqvPhDuf1wZ0p7QdNtQ/37lsai2EnhrmD+1RyNRq4ntzHt53jsvm0Tj27542eXcc89CTFEmdtwzlEvPMvDKaYaKFNHqxHs1UXUY/BgRC8x0Li3Ak3PuCxlw727xzPuQllMOKjoxhJ9WkWg/oOymrAXquihy7U+RwkMRVI01VxpWOU4VrkTIDY4EOiAaoNDFDD3PKWnPs7Bs5IOuv2BZ14vtuXOyCTfz4NZe8fccY2VbLC8FeDFhwIDAQAB", "manifest_version": 2, "name": "券多多", "options_page": "options.html", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "0.0.3", "web_accessible_resources": ["img/*"]}, "path": "ljlelhlcghfgkhckkmplmbgfaajlmemo\\0.0.3_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ncbjelpjchkpbikbpkcchkhkblodoama": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.teams.microsoft.com/*", "https://*.skype.com/*", "https://*.teams.live.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtAdFAR3ckd5c7G8VSzUj4Ltt/QRInUOD00StG95LweksGcLBlFlYL46cHFVgHHj1gmzcpBtgsURdcrAC3V8yiE7GY4wtpOP+9l+adUGR+cyOG0mw9fLjyH+2Il0QqktsNXzkNiE1ogW4l0h4+PJc262j0vtm4hBzMvR0QScFWcAIcAErlUiWTt4jefXCAYqubV99ed5MvVMWBxe97wOa9hYwAhbCminOepA4RRTg9eyi0TiuHpq/bNI8C5qZgKIQNBAjgiFBaIx9hiMBFlK4NHUbFdgY6Qp/hSCMNurctwz1jpsXEnT4eHg1YWXfquoH8s4swIjkFCMBF6Ejc3cUkQIDAQAB", "manifest_version": 2, "name": "WebRTC Internals Extension", "permissions": ["webrtcInternalsPrivate"], "version": "2.0.2"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\webrtc_internals", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ngphehpfehdmjellohmlojkplilekadg": {"account_extension_type": 2, "active_permissions": {"api": ["clipboardRead", "clipboardWrite", "contextMenus", "management", "storage", "tabs", "scripting", "declarativeNetRequestWithHostAccess"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://docs.google.com/*", "https://www.maxai.co/*"]}, "commands": {"_execute_action": {"was_assigned": true}, "open-immersive-chat": {"suggested_key": "Alt+I", "was_assigned": true}, "toggle-page-translator": {"suggested_key": "Alt+A", "was_assigned": true}}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["clipboardRead", "clipboardWrite", "contextMenus", "management", "storage", "tabs", "scripting", "declarativeNetRequestWithHostAccess"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "https://docs.google.com/*", "https://www.maxai.co/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"16": "assets/USE_CHAT_GPT_AI/icons/maxai_16_normal.png", "24": "assets/USE_CHAT_GPT_AI/icons/maxai_32_normal.png", "32": "assets/USE_CHAT_GPT_AI/icons/maxai_32_normal.png"}, "default_popup": "pages/popup/index.html", "default_title": "MaxAI"}, "background": {"service_worker": "background.js", "type": "module"}, "commands": {"_execute_action": {"description": "Active MaxAI", "suggested_key": {"default": "Alt+J", "linux": "Alt+J", "mac": "Command+J", "windows": "Alt+J"}}, "open-immersive-chat": {"description": "Open immersive chat", "suggested_key": {"default": "Alt+I", "linux": "Alt+I", "mac": "Command+I", "windows": "Alt+I"}}, "toggle-page-translator": {"description": "Translate webpage", "suggested_key": {"default": "Alt+A"}}}, "content_scripts": [{"js": ["import_content.js"], "matches": ["<all_urls>"], "run_at": "document_end"}, {"js": ["import_apps_content-scripts_checkMaxAIStatus.js"], "matches": ["https://www.maxai.co/*"], "run_at": "document_end"}, {"all_frames": false, "js": ["import_apps_content-scripts_injectDocumentStart.js"], "matches": ["<all_urls>"], "run_at": "document_start"}, {"all_frames": false, "js": ["apps/content-scripts/website/googleDoc.js"], "matches": ["https://docs.google.com/*"], "run_at": "document_start", "world": "MAIN"}, {"all_frames": true, "js": ["import_apps_content-scripts_iframeDocumentEnd.js"], "match_about_blank": true, "matches": ["<all_urls>"], "run_at": "document_end"}], "current_locale": "zh_CN", "default_locale": "en", "description": "使用您的AI助手节省时间，帮助您在任何在线工作中更快地阅读、写作和搜索。", "host_permissions": ["<all_urls>"], "icons": {"128": "assets/USE_CHAT_GPT_AI/icons/maxai_128_normal.png", "16": "assets/USE_CHAT_GPT_AI/icons/maxai_16_normal.png", "32": "assets/USE_CHAT_GPT_AI/icons/maxai_32_normal.png", "48": "assets/USE_CHAT_GPT_AI/icons/maxai_48_normal.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA+LyrkUJYDX2WMxmrHSc7nZCpl2jxZ3br6+WAIwOawHZrvRyab7H4hwcC9frM1ZKC/C8fK+kJd7J1NO5/PfB/9d6E55YCXb7jbTAYYA/K3r62koT7eFONbsyIs7QwnOv5lkXX4vwBd8CJH0dCPCntH9XENoZlzpq7j8uvZfq7vx+iboHZBodaHU5FJTwb88PmC3PJrEGAuy5NOWVGI2kylH4gIq1BEUlCE/ZfsYfqRNDhnl57oIuAlakwtwZcGjZt7PZ/Bl99Smk4sr9YGH4w+xVWtkG0OmepeYE4EVeKGpOVYcDfqWpVxuMAgGq6cQ4h28i2aWFKQ7/TOmVzqO0JAwIDAQAB", "manifest_version": 3, "name": "MaxAI：在浏览时询问AI任何问题", "options_ui": {"open_in_tab": true, "page": "pages/settings/index.html"}, "permissions": ["tabs", "scripting", "storage", "management", "contextMenus", "clipboardRead", "clipboardWrite", "declarativeNetRequestWithHostAccess"], "short_name": "MaxAI", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "8.26.2", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["worker.js", "i18n/locales/*", "pages/pdf/*", "content.css", "content_style.css", "chunks/*.js", "assets/*", "pages/*", "apps/*", "content.js", "apps/content-scripts/checkMaxAIStatus.js", "apps/content-scripts/injectDocumentStart.js", "apps/content-scripts/iframeDocumentEnd.js"]}]}, "path": "ngphehpfehdmjellohmlojkplilekadg\\8.26.2_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "nkbndigcebkoaejohleckhekfmcecfja": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.teams.microsoft.com/*", "https://*.meet.teams.live.com/*", "https://*.meet.skype.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "WebRTC Extension", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ofefcgjbeghpigppfmkologfjadafddi": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "opgbiafapkbbnbnjcdomjaghbckfkglc": {"account_extension_type": 2, "active_permissions": {"api": ["alarms", "contextMenus", "storage", "webRequest", "declarativeNetRequest", "scripting"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [1], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["alarms", "contextMenus", "storage", "webRequest", "declarativeNetRequest", "scripting"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 1, "manifest": {"action": {"default_icon": {"128": "images/icon_128.png", "16": "images/icon_16.png", "48": "images/icon_48.png"}, "default_popup": "src/popup_v1.html", "default_title": "<PERSON><PERSON><PERSON><PERSON>"}, "author": "modhader@", "background": {"service_worker": "serviceWorker.js", "type": "module"}, "content_scripts": [{"js": ["src/js/service/content_script_vite.js"], "matches": ["<all_urls>"]}], "current_locale": "zh_CN", "default_locale": "en", "description": "Modify HTTP request headers, response headers, and redirect URLs", "host_permissions": ["<all_urls>"], "icons": {"128": "images/icon_128.png", "16": "images/icon_16.png", "48": "images/icon_48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoapYsLRJ84zvyAhU0h638d97Ng0Ygy2r/KQStSVJ/ny0pevQDT57gWNMIBIebQQmIE/oScZUwUo/9TXhW9buaQwTqZwM0zKi6GFipGftRyVc/08DwZyxoTuNuhhU3a/4hBeHNIH87BSBQuBGVEHqVMwZTKay7gZonVodOfHHcaXwrmQ0+IDXL+l85xea6qO98KJCTKTFO31bsXejj14M2kj0x+fspnSeunpFznYE+Wv1aICMahXjjjWchQXt8ES9Ju87OesuwAh/QDSzmwu7jJm9gzx+Z6uBDkR/LsCVpiWKOJvs0Ms7qqQ+TpI8m7REa0Cn4tun4uXoqUZQdptd7wIDAQAB", "manifest_version": 3, "name": "ModHeader - Modify HTTP headers", "optional_permissions": ["contentSettings", "browsingData"], "options_ui": {"open_in_tab": true, "page": "src/app_v1.html"}, "permissions": ["alarms", "contextMenus", "storage", "webRequest", "declarativeNetRequest", "scripting"], "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "7.0.8", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["assets/AppVite-041b9295.css", "assets/AppVite-5ee08b2b.js", "assets/IconBtn-94c9cb85.js", "assets/_commonjsHelpers-187a63f9.js", "assets/browser-polyfill-c2a30efe.js", "assets/dayjs.min-db7fc211.js", "assets/isObjectLike-7962ce13.js", "assets/renderContent-9d534bdd.js", "assets/src/js/service/content_script_vite-4ac27ed4.js", "images/*"]}]}, "path": "opgbiafapkbbnbnjcdomjaghbckfkglc\\7.0.8_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "pcgaihjmjiakbnobkkbjdhfcchfnbgbj": {"disable_reasons": [1], "lastpingday": "*****************"}}}, "google": {"services": {"last_signed_in_username": "<EMAIL>"}}, "protection": {"macs": {"browser": {"show_home_button": "86A5E492FB843938B996426DA1BC6E716F9EEF2CC1FD77A5AA5B15F5F8F8A706"}, "default_search_provider_data": {"template_url_data": "DB27AFF8EFA17199FDB79097529F893C4CC6CE48DEDFE29EF1F4F8D9636EDB7D"}, "edge": {"services": {"account_id": "CA9EA4DA4F37708B820CEE215A90D29A9AE1CDECB242C856A43BDBA24A7F7D09", "last_username": "5DA5A85C95F3026A74277C223C00263DE0242AD12D9A140A648D31F0CD3F1278"}}, "enterprise_signin": {"policy_recovery_token": "14161CABF9FDD6DB5563D05D468B46F66E7E395114D67320B43EAF72CA2C6F39"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "0CFF8E4BDB2CAF08D1DE0E84396FE8DF22FD3C44DB0368B0BBA942EE25B19B1C", "bpelnogcookhocnaokfpoeinibimbeff": "B46EE77F6901372D33E2C53F887F7710645BC16C8649622512C16014A4148EF0", "cjneempfhkonkkbcmnfdibgobmhbagaj": "B236902EFB6B24441A3B023689CC16B1C055959C2066C46F61EF1DE1C1B75FB8", "cnjkedgepfdpdbnepgmajmmjdjkjnifa": "193585DC09E25E89B636C967AB6ACC9EDDD528BF564B91ECB6823DFB719B1288", "dcaajljecejllikfgbhjdgeognacjkkp": "33957C9FD09BC8D165E71CBF509B34C315B381ADE0235AAD3996B55FAD95BD5F", "dgbhmbogkcdheijkkdmfhodkamcaiheo": "4CFD5A3AE5E6C1A9CF9474D74A6D0343AC0BD5B185E9D7B84032D5BE4E88E5A6", "dgiklkfkllikcanfonkcabmbdfmgleag": "1729CF62A8DEB439EABA29E7BF0E8A1E0FFEAE237772598B17D4F3A59C0A84DB", "dhoenijjpgpeimemopealfcbiecgceod": "EB2689467B87FB3D1D4D454EF16ED6E145E6AB0A7BE1653A00BA0855CF733AFC", "dimkapigjeiopninbepgaoobcnbjmgik": "C23F92AF8CE3DCD992ACAE5784E755673531F35EF0C149DD46783C80833FAA5F", "ehlmnljdoejdahfjdfobmpfancoibmig": "2CB39233DC2295904A0847A373261B0DA51635E23B129F8ED19352837A892D99", "epdpgaljfdjmcemiaplofbiholoaepem": "8F7049ADF8F254BDBDDBB70F9782BCEE486060CB11B480AF9164863FACDF10FC", "fhkhedgjlkcgdaldodbhmcldjglpabmf": "0E4F8A2E490B95074A4AD56A0D95230B906DAF7A34FCCD1D381A77109E23FF36", "fikbjbembnmfhppjfnmfkahdhfohhjmg": "89DAA15863AC811510A6E15D7D77BCAF171F89F8099D50E2C85526D453F43050", "fjngpfnaikknjdhkckmncgicobbkcnle": "CE9DEA9DFE94BA2D2543AC37F253D5B3A4CEE78D452578A83BD49EB9DFDD65D4", "flahobhjikkpnpohomeckhdjjkkkkmoc": "90175D2034D686333F8AE135C704FC200FBE250CC068ECEF9C4C036F0FBA5254", "gbihlnbpmfkodghomcinpblknjhneknc": "5B726BF0BD41E9E58D1F8AB0B9D624C38EB63FDCB34599435EF27F1199121674", "gbmoeijgfngecijpcnbooedokgafmmji": "D06B7A7403DA591F20E97DC614D98B3AD9B8502F6166BE6C9145D2CC69098529", "gcinnojdebelpnodghnoicmcdmamjoch": "58402CA7F6F58B14298DC66322EA8E4E482E579827B4340E25749951211F57C0", "gdmpgjhgjlgmnjjcebofmbkimgkinjbo": "A06C48095BA239A360AF22A9B3F317C3765C5415B152C0AF7EF894B85804ADB5", "gecfnmoodchdkebjjffmdcmeghkflpib": "B4037CD0E6BB0B6A7C9D5ED5D24C148E109375CB324AA0D0A6E4B7471FFED611", "geieijlhpiifjlffnldmnnjbebmpjgmk": "F424EDDD5A6ADF2D0AA30B8D5B8DD1432DA38319A313B69284E60FC63DF51EE1", "ghbmnnjooekpmoecnnnilnnbdlolhkhi": "D080629B3AEADA3842152ABADBC0F37F27535FB4BD7F69777FA312A2CD3BC17C", "hfmgbegjielnmfghmoohgmplnpeehike": "35DBD9DF06426F7BA60CFC2F19D500EA7EA08BB6C2E5867E5E39C3BF935BCDE7", "iglcjdemknebjbklcgkfaebgojjphkec": "F404F1A3EF40523C71B02AC66F14C564A0D636E027A329A889CF90D360B4321B", "ihmafllikibpmigkcoadcmckbfhibefp": "EBC84DF1F7CE40719E626AA3A987708A06EA0CBBDB830A9E7EF7675D590B1AB7", "iikmkjmpaadaobahmlepeloendndfphd": "FFF012C1F74CAECE8CF82032E57FA9D0705958DE5C0EC6FDB0605E469284B520", "jbleckejnaboogigodiafflhkajdmpcl": "4A6CFF488013463DBF28DCE789DFFC3004BCA77B3F9914BDCDFDB71596496C4E", "jdiccldimpdaibmpdkjnbmckianbfold": "ABE19133805369763533ED7CEB3A10E9DE06884377620F9289A81CC2E0F165E0", "jmjflgjpcpepeafmmgdpfkogkghcpiha": "79D2E155146AC30419B299517E8C2F8BC4005A4A63099CE2C1DAF0117D06A477", "kdlmggoacmfoombiokflpeompajfljga": "BC2D727381AE75D3C880C6190EDDDB9F448635BFB512B3EE8A2D696A76D91D34", "kfihiegbjaloebkmglnjnljoljgkkchm": "5B2ED2BE994424CE05F3BFE8476326D3C6D8B444410DD80B2A834232924C64BC", "kmhegedbanhfblnoboomoeafpmojfdlp": "524BA7B3F5E098E871E6806D72E5CF5B33CF2282EEB5E98498F1CB50DF95949B", "liilgpjgabokdklappibcjfablkpcekh": "DC408DC1C01D4E402D76E94E85C63AFD4483F81E8E0F94D763A780CA129689CF", "ljlelhlcghfgkhckkmplmbgfaajlmemo": "914CB00D60B5B634482CE73E0F39CA41FBC841BA8060C82FAA441A631016C005", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "F347F156768D4D6DAA47ACC7B67EC98DE35B807E1350080BF230B912A3369DA8", "ncbjelpjchkpbikbpkcchkhkblodoama": "6C0ACD6C1999693F0183761F9DC43A74F4A04DDEC6EA972AE1C9800812144A8F", "ngphehpfehdmjellohmlojkplilekadg": "D45EA6639D4B3D6B2572D1A96D9543D8E4B404BABFA21E20423279C89D262059", "nkbndigcebkoaejohleckhekfmcecfja": "5F2BC6413C2635C056806376DCD201B65E5E12DD26A9323FE506FC4666CD59BF", "nkeimhogjdpnpccoofpliimaahmaaome": "190860683E0DF8F2CCC86D3495FC37F24A8A4E66159DDE40EA418F6189DF3A7C", "ofefcgjbeghpigppfmkologfjadafddi": "BE6009FCFD49331E39AB2CEF143133BB4D913AA4A031B2469915C6319569D9C2", "opgbiafapkbbnbnjcdomjaghbckfkglc": "C2ABC62E951349E01130F6DFB5AF42569FF05C6F8ED9902AFFF8BFC74AA3FA00", "pcgaihjmjiakbnobkkbjdhfcchfnbgbj": "DC55A1871B7D4DF91AB60F3B619D1743F52AF768F2BD74EBEEB03018FE67E252"}, "ui": {"developer_mode": "1F89DB1DF63F2C52B95CDF57DCE98C109D1A79A05E4C51B43ACBEE67BBC7AD37"}}, "google": {"services": {"last_signed_in_username": "1476DBCCD710759A3A00DDA4D8906228F8F48E186EEC60FA57B7FAF2364328B1"}}, "homepage": "3D708379264349802A6089C6BA66055D615F1A9B9D0861D0413C8E9E3B68B444", "homepage_is_newtabpage": "0BD0A34C349ADA85649ABD5C8D3CC240D69D048F2A77F38BF427B301A1CC1872", "media": {"cdm": {"origin_data": "8273AFBD1161CC75F5B08BBD4408DAE5095E8A71ABA3C24613E92C5A85901390"}, "storage_id_salt": "83C5AD99603406C0D3683C0A4EB29B988A7B2602D5425197327DD025ED40F77F"}, "pinned_tabs": "B3C35D11039A08566B009DA6BB99440201DD192C9BC6654BD69D3F67106FD9D6", "prefs": {"preference_reset_time": "D54DE5F20656109D5063FC69A938C09E6638DE98B1E21D996085D8AFCE733A4F"}, "safebrowsing": {"incidents_sent": "B5088CC32D5653E49C0187D3CFFD69FDFA8C0C80CBE3320776624497C886CC68"}, "search_provider_overrides": "9C3020E3464E557BB1ACB59BA112C7B9CB8FE6C6A1663406615F8020851F5568", "session": {"restore_on_startup": "44311EF09ECF703B4D8F2966F21E42D8F80FEDCC5D27872DACF3FC00DB4FE7A6", "startup_urls": "5754CA4C0757E92688CE6DE9036A31E25C89EB2CDF4EE223EEE1E94001B6957B"}}, "super_mac": "86F01AC5FC611C515A007F75A51AF75FFC51BAB2F3A30450535EDCB31B6A8501"}, "session": {"startup_urls": ["http://egdh.baidu68861.com"], "startup_urls_edge_lightweight": "068F83007633C81A472AF7A1E593E68B53DB2D93495964F1650C81A85B307F32C83782A7815336FDA95593E2A7AE770D65679234581D16EEF8DF", "startup_urls_edge_lightweight_verify": "ccdf44c3c3d546b3e29274970f895c98"}}