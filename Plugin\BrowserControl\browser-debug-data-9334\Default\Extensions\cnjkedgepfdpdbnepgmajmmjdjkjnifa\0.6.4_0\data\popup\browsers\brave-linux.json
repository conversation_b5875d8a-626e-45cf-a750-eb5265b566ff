[{"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.38 Safari/537.36 Brave/75", "browser": {"name": "Brave", "version": "75", "major": "75"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "75.0.3770.38"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) brave/0.7.16 Chrome/47.0.2526.110 Brave/0.36.8 Safari/537.36", "browser": {"name": "brave", "version": "0.7.16", "major": "0"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "47.0.2526.110"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) brave/0.8.2 Chrome/49.0.2623.87 Brave/0.37.2 Safari/537.36", "browser": {"name": "brave", "version": "0.8.2", "major": "0"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "49.0.2623.87"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) brave/0.8.3 Chrome/49.0.2623.108 Brave/0.37.3 Safari/537.36", "browser": {"name": "brave", "version": "0.8.3", "major": "0"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "49.0.2623.108"}, "os": {"name": "Linux", "version": "x86_64"}}]