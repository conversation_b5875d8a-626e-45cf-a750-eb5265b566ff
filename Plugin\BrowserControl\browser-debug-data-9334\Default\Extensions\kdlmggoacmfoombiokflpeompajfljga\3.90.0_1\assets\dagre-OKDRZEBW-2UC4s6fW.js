import{_ as y,aq as j,ar as F,as as Y,at as H,l as r,d as V,au as q,av as U,af as $,ak as z,ag as P,ae as K,aw as Q,ax as W,ay as Z}from"./MermaidPreview-DN0CF7bz.js";import{G as B}from"./graph-Cuz86YKM.js";import{l as I}from"./layout-C-6EmolC.js";import{i as x}from"./premium-CrQDER7x.js";import{c as L}from"./clone-CpeMRwbc.js";import{m as A}from"./map-CPMUsqym.js";import"./user-config-BrqC82sm.js";import"./merge-CG3iZ9md.js";import"./reduce-d3EV_kCa.js";import"./_baseUniq-CDn7CWnQ.js";import"./min-1QEMBxke.js";import"./_flatRest-DPsYhEwM.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="710eb815-ea4c-4bec-82f9-db78393e5720",e._sentryDebugIdIdentifier="sentry-dbid-710eb815-ea4c-4bec-82f9-db78393e5720")}catch{}})();function X(e){var t={options:{directed:e.isDirected(),multigraph:e.isMultigraph(),compound:e.isCompound()},nodes:ee(e),edges:ne(e)};return x(e.graph())||(t.value=L(e.graph())),t}function ee(e){return A(e.nodes(),function(t){var n=e.node(t),o=e.parent(t),c={v:t};return x(n)||(c.value=n),x(o)||(c.parent=o),c})}function ne(e){return A(e.edges(),function(t){var n=e.edge(t),o={v:t.v,w:t.w};return x(t.name)||(o.name=t.name),x(n)||(o.value=n),o})}var f=new Map,N=new Map,J=new Map,te=y(()=>{N.clear(),J.clear(),f.clear()},"clear"),O=y((e,t)=>{const n=N.get(t)||[];return r.trace("In isDescendant",t," ",e," = ",n.includes(e)),n.includes(e)},"isDescendant"),se=y((e,t)=>{const n=N.get(t)||[];return r.info("Descendants of ",t," is ",n),r.info("Edge is ",e),e.v===t||e.w===t?!1:n?n.includes(e.v)||O(e.v,t)||O(e.w,t)||n.includes(e.w):(r.debug("Tilt, ",t,",not in descendants"),!1)},"edgeInCluster"),G=y((e,t,n,o)=>{r.warn("Copying children of ",e,"root",o,"data",t.node(e),o);const c=t.children(e)||[];e!==o&&c.push(e),r.warn("Copying (nodes) clusterId",e,"nodes",c),c.forEach(a=>{if(t.children(a).length>0)G(a,t,n,o);else{const i=t.node(a);r.info("cp ",a," to ",o," with parent ",e),n.setNode(a,i),o!==t.parent(a)&&(r.warn("Setting parent",a,t.parent(a)),n.setParent(a,t.parent(a))),e!==o&&a!==e?(r.debug("Setting parent",a,e),n.setParent(a,e)):(r.info("In copy ",e,"root",o,"data",t.node(e),o),r.debug("Not Setting parent for node=",a,"cluster!==rootId",e!==o,"node!==clusterId",a!==e));const u=t.edges(a);r.debug("Copying Edges",u),u.forEach(l=>{r.info("Edge",l);const h=t.edge(l.v,l.w,l.name);r.info("Edge data",h,o);try{se(l,o)?(r.info("Copying as ",l.v,l.w,h,l.name),n.setEdge(l.v,l.w,h,l.name),r.info("newGraph edges ",n.edges(),n.edge(n.edges()[0]))):r.info("Skipping copy of edge ",l.v,"-->",l.w," rootId: ",o," clusterId:",e)}catch(C){r.error(C)}})}r.debug("Removing node",a),t.removeNode(a)})},"copy"),R=y((e,t)=>{const n=t.children(e);let o=[...n];for(const c of n)J.set(c,e),o=[...o,...R(c,t)];return o},"extractDescendants"),re=y((e,t,n)=>{const o=e.edges().filter(l=>l.v===t||l.w===t),c=e.edges().filter(l=>l.v===n||l.w===n),a=o.map(l=>({v:l.v===t?n:l.v,w:l.w===t?t:l.w})),i=c.map(l=>({v:l.v,w:l.w}));return a.filter(l=>i.some(h=>l.v===h.v&&l.w===h.w))},"findCommonEdges"),D=y((e,t,n)=>{const o=t.children(e);if(r.trace("Searching children of id ",e,o),o.length<1)return e;let c;for(const a of o){const i=D(a,t,n),u=re(t,n,i);if(i)if(u.length>0)c=i;else return i}return c},"findNonClusterChild"),k=y(e=>!f.has(e)||!f.get(e).externalConnections?e:f.has(e)?f.get(e).id:e,"getAnchorId"),ie=y((e,t)=>{if(!e||t>10){r.debug("Opting out, no graph ");return}else r.debug("Opting in, graph ");e.nodes().forEach(function(n){e.children(n).length>0&&(r.warn("Cluster identified",n," Replacement id in edges: ",D(n,e,n)),N.set(n,R(n,e)),f.set(n,{id:D(n,e,n),clusterData:e.node(n)}))}),e.nodes().forEach(function(n){const o=e.children(n),c=e.edges();o.length>0?(r.debug("Cluster identified",n,N),c.forEach(a=>{const i=O(a.v,n),u=O(a.w,n);i^u&&(r.warn("Edge: ",a," leaves cluster ",n),r.warn("Descendants of XXX ",n,": ",N.get(n)),f.get(n).externalConnections=!0)})):r.debug("Not a cluster ",n,N)});for(let n of f.keys()){const o=f.get(n).id,c=e.parent(o);c!==n&&f.has(c)&&!f.get(c).externalConnections&&(f.get(n).id=c)}e.edges().forEach(function(n){const o=e.edge(n);r.warn("Edge "+n.v+" -> "+n.w+": "+JSON.stringify(n)),r.warn("Edge "+n.v+" -> "+n.w+": "+JSON.stringify(e.edge(n)));let c=n.v,a=n.w;if(r.warn("Fix XXX",f,"ids:",n.v,n.w,"Translating: ",f.get(n.v)," --- ",f.get(n.w)),f.get(n.v)||f.get(n.w)){if(r.warn("Fixing and trying - removing XXX",n.v,n.w,n.name),c=k(n.v),a=k(n.w),e.removeEdge(n.v,n.w,n.name),c!==n.v){const i=e.parent(c);f.get(i).externalConnections=!0,o.fromCluster=n.v}if(a!==n.w){const i=e.parent(a);f.get(i).externalConnections=!0,o.toCluster=n.w}r.warn("Fix Replacing with XXX",c,a,n.name),e.setEdge(c,a,o,n.name)}}),r.warn("Adjusted Graph",X(e)),T(e,0),r.trace(f)},"adjustClustersAndEdges"),T=y((e,t)=>{var c,a;if(r.warn("extractor - ",t,X(e),e.children("D")),t>10){r.error("Bailing out");return}let n=e.nodes(),o=!1;for(const i of n){const u=e.children(i);o=o||u.length>0}if(!o){r.debug("Done, no node has children",e.nodes());return}r.debug("Nodes = ",n,t);for(const i of n)if(r.debug("Extracting node",i,f,f.has(i)&&!f.get(i).externalConnections,!e.parent(i),e.node(i),e.children("D")," Depth ",t),!f.has(i))r.debug("Not a cluster",i,t);else if(!f.get(i).externalConnections&&e.children(i)&&e.children(i).length>0){r.warn("Cluster without external connections, without a parent and with children",i,t);let l=e.graph().rankdir==="TB"?"LR":"TB";(a=(c=f.get(i))==null?void 0:c.clusterData)!=null&&a.dir&&(l=f.get(i).clusterData.dir,r.warn("Fixing dir",f.get(i).clusterData.dir,l));const h=new B({multigraph:!0,compound:!0}).setGraph({rankdir:l,nodesep:50,ranksep:50,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}});r.warn("Old graph before copy",X(e)),G(i,e,h,i),e.setNode(i,{clusterNode:!0,id:i,clusterData:f.get(i).clusterData,label:f.get(i).label,graph:h}),r.warn("New graph after copy node: (",i,")",X(h)),r.debug("Old graph after copy",X(e))}else r.warn("Cluster ** ",i," **not meeting the criteria !externalConnections:",!f.get(i).externalConnections," no parent: ",!e.parent(i)," children ",e.children(i)&&e.children(i).length>0,e.children("D"),t),r.debug(f);n=e.nodes(),r.warn("New list of nodes",n);for(const i of n){const u=e.node(i);r.warn(" Now next level",i,u),u!=null&&u.clusterNode&&T(u.graph,t+1)}},"extractor"),_=y((e,t)=>{if(t.length===0)return[];let n=Object.assign([],t);return t.forEach(o=>{const c=e.children(o),a=_(e,c);n=[...n,...a]}),n},"sorter"),oe=y(e=>_(e,e.children()),"sortNodesByHierarchy"),M=y(async(e,t,n,o,c,a)=>{r.warn("Graph in recursive render:XAX",X(t),c);const i=t.graph().rankdir;r.trace("Dir in recursive render - dir:",i);const u=e.insert("g").attr("class","root");t.nodes()?r.info("Recursive render XXX",t.nodes()):r.info("No nodes found for",t),t.edges().length>0&&r.info("Recursive edges",t.edge(t.edges()[0]));const l=u.insert("g").attr("class","clusters"),h=u.insert("g").attr("class","edgePaths"),C=u.insert("g").attr("class","edgeLabels"),g=u.insert("g").attr("class","nodes");await Promise.all(t.nodes().map(async function(d){const s=t.node(d);if(c!==void 0){const w=JSON.parse(JSON.stringify(c.clusterData));r.trace(`Setting data for parent cluster XXX
 Node.id = `,d,`
 data=`,w.height,`
Parent cluster`,c.height),t.setNode(c.id,w),t.parent(d)||(r.trace("Setting parent",d,c.id),t.setParent(d,c.id,w))}if(r.info("(Insert) Node XXX"+d+": "+JSON.stringify(t.node(d))),s!=null&&s.clusterNode){r.info("Cluster identified XBX",d,s.width,t.node(d));const{ranksep:w,nodesep:m}=t.graph();s.graph.setGraph({...s.graph.graph(),ranksep:w+25,nodesep:m});const E=await M(g,s.graph,n,o,t.node(d),a),S=E.elem;q(s,S),s.diff=E.diff||0,r.info("New compound node after recursive render XAX",d,"width",s.width,"height",s.height),U(S,s)}else t.children(d).length>0?(r.trace("Cluster - the non recursive path XBX",d,s.id,s,s.width,"Graph:",t),r.trace(D(s.id,t)),f.set(s.id,{id:D(s.id,t),node:s})):(r.trace("Node - the non recursive path XAX",d,g,t.node(d),i),await $(g,t.node(d),{config:a,dir:i}))})),await y(async()=>{const d=t.edges().map(async function(s){const w=t.edge(s.v,s.w,s.name);r.info("Edge "+s.v+" -> "+s.w+": "+JSON.stringify(s)),r.info("Edge "+s.v+" -> "+s.w+": ",s," ",JSON.stringify(t.edge(s))),r.info("Fix",f,"ids:",s.v,s.w,"Translating: ",f.get(s.v),f.get(s.w)),await Z(C,w)});await Promise.all(d)},"processEdges")(),r.info("Graph before layout:",JSON.stringify(X(t))),r.info("############################################# XXX"),r.info("###                Layout                 ### XXX"),r.info("############################################# XXX"),I(t),r.info("Graph after layout:",JSON.stringify(X(t)));let b=0,{subGraphTitleTotalMargin:p}=z(a);return await Promise.all(oe(t).map(async function(d){var w;const s=t.node(d);if(r.info("Position XBX => "+d+": ("+s.x,","+s.y,") width: ",s.width," height: ",s.height),s!=null&&s.clusterNode)s.y+=p,r.info("A tainted cluster node XBX1",d,s.id,s.width,s.height,s.x,s.y,t.parent(d)),f.get(s.id).node=s,P(s);else if(t.children(d).length>0){r.info("A pure cluster node XBX1",d,s.id,s.x,s.y,s.width,s.height,t.parent(d)),s.height+=p,t.node(s.parentId);const m=(s==null?void 0:s.padding)/2||0,E=((w=s==null?void 0:s.labelBBox)==null?void 0:w.height)||0,S=E-m||0;r.debug("OffsetY",S,"labelHeight",E,"halfPadding",m),await K(l,s),f.get(s.id).node=s}else{const m=t.node(s.parentId);s.y+=p/2,r.info("A regular node XBX1 - using the padding",s.id,"parent",s.parentId,s.width,s.height,s.x,s.y,"offsetY",s.offsetY,"parent",m,m==null?void 0:m.offsetY,s),P(s)}})),t.edges().forEach(function(d){const s=t.edge(d);r.info("Edge "+d.v+" -> "+d.w+": "+JSON.stringify(s),s),s.points.forEach(S=>S.y+=p/2);const w=t.node(d.v);var m=t.node(d.w);const E=Q(h,s,f,n,w,m,o);W(s,E)}),t.nodes().forEach(function(d){const s=t.node(d);r.info(d,s.type,s.diff),s.isGroup&&(b=s.diff)}),r.warn("Returning from recursive render XAX",u,b),{elem:u,diff:b}},"recursiveRender"),pe=y(async(e,t)=>{var a,i,u,l,h,C;const n=new B({multigraph:!0,compound:!0}).setGraph({rankdir:e.direction,nodesep:((a=e.config)==null?void 0:a.nodeSpacing)||((u=(i=e.config)==null?void 0:i.flowchart)==null?void 0:u.nodeSpacing)||e.nodeSpacing,ranksep:((l=e.config)==null?void 0:l.rankSpacing)||((C=(h=e.config)==null?void 0:h.flowchart)==null?void 0:C.rankSpacing)||e.rankSpacing,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}}),o=t.select("g");j(o,e.markers,e.type,e.diagramId),F(),Y(),H(),te(),e.nodes.forEach(g=>{n.setNode(g.id,{...g}),g.parentId&&n.setParent(g.id,g.parentId)}),r.debug("Edges:",e.edges),e.edges.forEach(g=>{if(g.start===g.end){const v=g.start,b=v+"---"+v+"---1",p=v+"---"+v+"---2",d=n.node(v);n.setNode(b,{domId:b,id:b,parentId:d.parentId,labelStyle:"",label:"",padding:0,shape:"labelRect",style:"",width:10,height:10}),n.setParent(b,d.parentId),n.setNode(p,{domId:p,id:p,parentId:d.parentId,labelStyle:"",padding:0,shape:"labelRect",label:"",style:"",width:10,height:10}),n.setParent(p,d.parentId);const s=structuredClone(g),w=structuredClone(g),m=structuredClone(g);s.label="",s.arrowTypeEnd="none",s.id=v+"-cyclic-special-1",w.arrowTypeStart="none",w.arrowTypeEnd="none",w.id=v+"-cyclic-special-mid",m.label="",d.isGroup&&(s.fromCluster=v,m.toCluster=v),m.id=v+"-cyclic-special-2",m.arrowTypeStart="none",n.setEdge(v,b,s,v+"-cyclic-special-0"),n.setEdge(b,p,w,v+"-cyclic-special-1"),n.setEdge(p,v,m,v+"-cyc<lic-special-2")}else n.setEdge(g.start,g.end,{...g},g.id)}),r.warn("Graph at first:",JSON.stringify(X(n))),ie(n),r.warn("Graph after XAX:",JSON.stringify(X(n)));const c=V();await M(o,n,e.type,e.diagramId,void 0,c)},"render");export{pe as render};
//# sourceMappingURL=dagre-OKDRZEBW-2UC4s6fW.js.map
