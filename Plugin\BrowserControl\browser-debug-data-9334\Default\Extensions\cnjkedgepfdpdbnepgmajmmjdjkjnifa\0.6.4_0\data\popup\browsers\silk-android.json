[{"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.3.1 like Chrome/77.0.3865.116 Safari/537.36", "browser": {"name": "Silk", "version": "77.3.1", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.1.135 like Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Silk", "version": "79.1.135", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.4.9 like Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Silk", "version": "79.4.9", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.3.3 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.3.3", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.3.1 like Chrome/77.0.3865.116 Safari/537.36", "browser": {"name": "Silk", "version": "77.3.1", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.3.1 like Chrome/77.0.3865.116 Safari/537.36", "browser": {"name": "Silk", "version": "77.3.1", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.3.6 like Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Silk", "version": "76.3.6", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.1.135 like Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Silk", "version": "79.1.135", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.3.13 like Chrome/79.0.3945.116 Safari/537.36", "browser": {"name": "Silk", "version": "79.3.13", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/70.4.2 like Chrome/70.0.3538.80 Safari/537.36", "browser": {"name": "Silk", "version": "70.4.2", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "70.0.3538.80"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.3.6 like Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Silk", "version": "76.3.6", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/60.2.12 like Chrome/60.0.3112.107 Safari/537.36", "browser": {"name": "Silk", "version": "60.2.12", "major": "60"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "60.0.3112.107"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.3.6 like Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Silk", "version": "76.3.6", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.2.19 like Chrome/77.0.3865.92 Safari/537.36", "browser": {"name": "Silk", "version": "77.2.19", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.92"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.3.6 like Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Silk", "version": "76.3.6", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.3.1 like Chrome/77.0.3865.116 Safari/537.36", "browser": {"name": "Silk", "version": "77.3.1", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.4.9 like Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Silk", "version": "79.4.9", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.3.1 like Chrome/77.0.3865.116 Safari/537.36", "browser": {"name": "Silk", "version": "77.3.1", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFTT) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.7.4 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.7.4", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.3.3 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.3.3", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.2.1 like Chrome/78.0.3904.96 Safari/537.36", "browser": {"name": "Silk", "version": "78.2.1", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.96"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.2.1 like Chrome/76.0.3809.111 Safari/537.36", "browser": {"name": "Silk", "version": "76.2.1", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.111"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.1.135 like Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Silk", "version": "79.1.135", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.1.135 like Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Silk", "version": "79.1.135", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.3.3 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.3.3", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.4.3 like Chrome/77.0.3865.116 Safari/537.36", "browser": {"name": "Silk", "version": "77.4.3", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.47 like Chrome/37.0.2026.117 Safari/537.36", "browser": {"name": "Silk", "version": "3.47", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "37.0.2026.117"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.3.3 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.3.3", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.4.9 like Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Silk", "version": "79.4.9", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.3.13 like Chrome/79.0.3945.116 Safari/537.36", "browser": {"name": "Silk", "version": "79.3.13", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.4.8 like Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Silk", "version": "75.4.8", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.3.13 like Chrome/79.0.3945.116 Safari/537.36", "browser": {"name": "Silk", "version": "79.3.13", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.3.1 like Chrome/77.0.3865.116 Safari/537.36", "browser": {"name": "Silk", "version": "77.3.1", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFTT) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.7.5 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.7.5", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.4.8 like Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Silk", "version": "75.4.8", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; KFMAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.4.9 like Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Silk", "version": "79.4.9", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.2.19 like Chrome/77.0.3865.92 Safari/537.36", "browser": {"name": "Silk", "version": "77.2.19", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.92"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.1.135 like Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Silk", "version": "79.1.135", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.2.1 like Chrome/76.0.3809.111 Safari/537.36", "browser": {"name": "Silk", "version": "76.2.1", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.111"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; KFMAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.3.3 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.3.3", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/70.4.2 like Chrome/70.0.3538.80 Safari/537.36", "browser": {"name": "Silk", "version": "70.4.2", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "70.0.3538.80"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFMUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.3.6 like Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Silk", "version": "76.3.6", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/74.3.14 like Chrome/74.0.3729.157 Safari/537.36", "browser": {"name": "Silk", "version": "74.3.14", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/64.3.4 like Chrome/64.0.3282.137 Safari/537.36", "browser": {"name": "Silk", "version": "64.3.4", "major": "64"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "64.0.3282.137"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.4; KFJWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.7.5 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.7.5", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFJWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "4.0.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.4.9 like Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Silk", "version": "79.4.9", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.2.19 like Chrome/77.0.3865.92 Safari/537.36", "browser": {"name": "Silk", "version": "77.2.19", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.92"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.2.19 like Chrome/77.0.3865.92 Safari/537.36", "browser": {"name": "Silk", "version": "77.2.19", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.92"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.3.3 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.3.3", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.2.1 like Chrome/78.0.3904.96 Safari/537.36", "browser": {"name": "Silk", "version": "78.2.1", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.96"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.4.8 like Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Silk", "version": "75.4.8", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.6.17 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.6.17", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFOT) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.7.5 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.7.5", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFOT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.4.9 like Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Silk", "version": "79.4.9", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFTBWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.3.6 like Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Silk", "version": "76.3.6", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTBWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFMUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.4.9 like Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Silk", "version": "79.4.9", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.1.135 like Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Silk", "version": "79.1.135", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/63.1.34 like Chrome/63.0.3239.111 Safari/537.36", "browser": {"name": "Silk", "version": "63.1.34", "major": "63"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFASWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.3.6 like Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Silk", "version": "76.3.6", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFASWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.4.9 like Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Silk", "version": "79.4.9", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFMUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.1.135 like Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Silk", "version": "79.1.135", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.2.1 like Chrome/78.0.3904.96 Safari/537.36", "browser": {"name": "Silk", "version": "78.2.1", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.96"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.3; en-us; KFTHWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.47 like Chrome/37.0.2026.117 Safari/537.36", "browser": {"name": "Silk", "version": "3.47", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "37.0.2026.117"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.4; en-us; Silk/1.0.146.3-Gen4_12000410) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 Silk-Accelerated=true", "browser": {"name": "Silk", "version": "1.0.146.3-Gen4_12000410", "major": "1"}, "cpu": {}, "device": {"type": "mobile", "model": "Silk/1.0.146.3-Gen4_12000410"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.2.19 like Chrome/77.0.3865.92 Safari/537.36", "browser": {"name": "Silk", "version": "77.2.19", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.92"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.2.1 like Chrome/76.0.3809.111 Safari/537.36", "browser": {"name": "Silk", "version": "76.2.1", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.111"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.2.22 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.2.22", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.3; en-us; KFSOWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.47 like Chrome/37.0.2026.117 Safari/537.36", "browser": {"name": "Silk", "version": "3.47", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "37.0.2026.117"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/74.3.14 like Chrome/74.0.3729.157 Safari/537.36", "browser": {"name": "Silk", "version": "74.3.14", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/80.2.5 like Chrome/80.0.3987.119 Safari/537.36", "browser": {"name": "Silk", "version": "80.2.5", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/63.1.34 like Chrome/63.0.3239.111 Safari/537.36", "browser": {"name": "Silk", "version": "63.1.34", "major": "63"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.2.1 like Chrome/76.0.3809.111 Safari/537.36", "browser": {"name": "Silk", "version": "76.2.1", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.111"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/52.1.79 like Chrome/52.0.2743.98 Safari/537.36", "browser": {"name": "Silk", "version": "52.1.79", "major": "52"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "52.0.2743.98"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.3.13 like Chrome/79.0.3945.116 Safari/537.36", "browser": {"name": "Silk", "version": "79.3.13", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.3.3 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.3.3", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; de-de; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.68 like Chrome/39.0.2171.93 Safari/537.36", "browser": {"name": "Silk", "version": "3.68", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "39.0.2171.93"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.2.1 like Chrome/76.0.3809.111 Safari/537.36", "browser": {"name": "Silk", "version": "76.2.1", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.111"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFSOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFTT) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.6.17 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.6.17", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.2.1 like Chrome/78.0.3904.96 Safari/537.36", "browser": {"name": "Silk", "version": "78.2.1", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.96"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.3.6 like Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Silk", "version": "76.3.6", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.4.8 like Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Silk", "version": "75.4.8", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-gb; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.47 like Chrome/37.0.2026.117 Safari/537.36", "browser": {"name": "Silk", "version": "3.47", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "37.0.2026.117"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.2.1 like Chrome/76.0.3809.111 Safari/537.36", "browser": {"name": "Silk", "version": "76.2.1", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.111"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.4.8 like Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Silk", "version": "75.4.8", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/62.6.1 like Chrome/62.0.3202.84 Safari/537.36", "browser": {"name": "Silk", "version": "62.6.1", "major": "62"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "62.0.3202.84"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.4; KFJWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.7.4 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.7.4", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFJWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "4.0.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/70.4.2 like Chrome/70.0.3538.80 Safari/537.36", "browser": {"name": "Silk", "version": "70.4.2", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "70.0.3538.80"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/61.2.1 like Chrome/61.0.3163.98 Safari/537.36", "browser": {"name": "Silk", "version": "61.2.1", "major": "61"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "61.0.3163.98"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/80.2.5 like Chrome/80.0.3987.119 Safari/537.36", "browser": {"name": "Silk", "version": "80.2.5", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.3.13 like Chrome/79.0.3945.116 Safari/537.36", "browser": {"name": "Silk", "version": "79.3.13", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.2.19 like Chrome/77.0.3865.92 Safari/537.36", "browser": {"name": "Silk", "version": "77.2.19", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.92"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/58.3.1 like Chrome/58.0.3029.83 Safari/537.36", "browser": {"name": "Silk", "version": "58.3.1", "major": "58"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "58.0.3029.83"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.3.13 like Chrome/79.0.3945.116 Safari/537.36", "browser": {"name": "Silk", "version": "79.3.13", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.1.135 like Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Silk", "version": "79.1.135", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFMUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.3.1 like Chrome/77.0.3865.116 Safari/537.36", "browser": {"name": "Silk", "version": "77.3.1", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.2.1 like Chrome/76.0.3809.111 Safari/537.36", "browser": {"name": "Silk", "version": "76.2.1", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.111"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/54.4.4 like Chrome/54.0.2840.85 Safari/537.36", "browser": {"name": "Silk", "version": "54.4.4", "major": "54"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "54.0.2840.85"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFSOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.3.1 like Chrome/77.0.3865.116 Safari/537.36", "browser": {"name": "Silk", "version": "77.3.1", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.3.6 like Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Silk", "version": "76.3.6", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/74.2.9 like Chrome/74.0.3729.136 Safari/537.36", "browser": {"name": "Silk", "version": "74.2.9", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "74.0.3729.136"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/62.6.1 like Chrome/62.0.3202.84 Safari/537.36", "browser": {"name": "Silk", "version": "62.6.1", "major": "62"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "62.0.3202.84"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFMUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFTBWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTBWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFTBWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.1.135 like Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Silk", "version": "79.1.135", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTBWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/69.3.33 like Chrome/69.0.3497.100 Safari/537.36", "browser": {"name": "Silk", "version": "69.3.33", "major": "69"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.1.117 like Chrome/75.0.3770.89 Safari/537.36", "browser": {"name": "Silk", "version": "75.1.117", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.89"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LMY47O) AppleWebKit/537.36 (KHTML, like Gecko) Silk/47.1.80 like Chrome/47.0.2526.83 Safari/537.36", "browser": {"name": "Silk", "version": "47.1.80", "major": "47"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "47.0.2526.83"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/56.2.4 like Chrome/56.0.2924.87 Safari/537.36", "browser": {"name": "Silk", "version": "56.2.4", "major": "56"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "56.0.2924.87"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/47.1.79 like Chrome/47.0.2526.80 Safari/537.36", "browser": {"name": "Silk", "version": "47.1.79", "major": "47"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "47.0.2526.80"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFTBWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTBWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; KFOT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.47 like Chrome/37.0.2026.117 Safari/537.36", "browser": {"name": "Silk", "version": "3.47", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFOT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "37.0.2026.117"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.3; en-us; KFASWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.47 like Chrome/37.0.2026.117 Safari/537.36", "browser": {"name": "Silk", "version": "3.47", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFASWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "37.0.2026.117"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.2.1 like Chrome/78.0.3904.96 Safari/537.36", "browser": {"name": "Silk", "version": "78.2.1", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.96"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/57.1.109 like Chrome/57.0.2987.132 Safari/537.36", "browser": {"name": "Silk", "version": "57.1.109", "major": "57"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "57.0.2987.132"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.4.8 like Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Silk", "version": "75.4.8", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.2.1 like Chrome/78.0.3904.96 Safari/537.36", "browser": {"name": "Silk", "version": "78.2.1", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.96"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; KFJWI Build/IMM76D) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.47 like Chrome/37.0.2026.117 Safari/537.36", "browser": {"name": "Silk", "version": "3.47", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFJWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "37.0.2026.117"}, "os": {"name": "Android", "version": "4.0.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFASWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.4.8 like Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Silk", "version": "75.4.8", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFASWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/50.2.1 like Chrome/50.0.2661.89 Safari/537.36", "browser": {"name": "Silk", "version": "50.2.1", "major": "50"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "50.0.2661.89"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/64.3.4 like Chrome/64.0.3282.137 Safari/537.36", "browser": {"name": "Silk", "version": "64.3.4", "major": "64"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "64.0.3282.137"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; de-de; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.70 like Chrome/39.0.2171.93 Safari/537.36", "browser": {"name": "Silk", "version": "3.70", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "39.0.2171.93"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFMEWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.3.3 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.3.3", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMEWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFMUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.3.13 like Chrome/79.0.3945.116 Safari/537.36", "browser": {"name": "Silk", "version": "79.3.13", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/63.1.34 like Chrome/63.0.3239.111 Safari/537.36", "browser": {"name": "Silk", "version": "63.1.34", "major": "63"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; KFMAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/59.3.1 like Chrome/59.0.3071.117 Safari/537.36", "browser": {"name": "Silk", "version": "59.3.1", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "59.0.3071.117"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/74.3.14 like Chrome/74.0.3729.157 Safari/537.36", "browser": {"name": "Silk", "version": "74.3.14", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFOT) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.6.17 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.6.17", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFOT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/60.2.12 like Chrome/60.0.3112.107 Safari/537.36", "browser": {"name": "Silk", "version": "60.2.12", "major": "60"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "60.0.3112.107"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFARWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.3.6 like Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Silk", "version": "76.3.6", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFARWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; KFMAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.3.13 like Chrome/79.0.3945.116 Safari/537.36", "browser": {"name": "Silk", "version": "79.3.13", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.4.9 like Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Silk", "version": "79.4.9", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.68 like Chrome/39.0.2171.93 Safari/537.36", "browser": {"name": "Silk", "version": "3.68", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "39.0.2171.93"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFASWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFASWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFASWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFASWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFMEWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.3.6 like Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Silk", "version": "76.3.6", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMEWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/65.2.3 like Chrome/65.0.3325.144 Safari/537.36", "browser": {"name": "Silk", "version": "65.2.3", "major": "65"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "65.0.3325.144"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFASWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.4.9 like Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Silk", "version": "79.4.9", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFASWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.6.17 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.6.17", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.1.117 like Chrome/75.0.3770.89 Safari/537.36", "browser": {"name": "Silk", "version": "75.1.117", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.89"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.3; en-us; KFARWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.47 like Chrome/37.0.2026.117 Safari/537.36", "browser": {"name": "Silk", "version": "3.47", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFARWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "37.0.2026.117"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.2.22 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.2.22", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/68.2.6 like Chrome/68.0.3440.85 Safari/537.36", "browser": {"name": "Silk", "version": "68.2.6", "major": "68"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "68.0.3440.85"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/63.1.34 like Chrome/63.0.3239.111 Safari/537.36", "browser": {"name": "Silk", "version": "63.1.34", "major": "63"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFAPWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAPWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFAPWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.3.1 like Chrome/77.0.3865.116 Safari/537.36", "browser": {"name": "Silk", "version": "77.3.1", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAPWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFMEWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMEWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LMY47O) AppleWebKit/537.36 (KHTML, like Gecko) Silk/50.2.1 like Chrome/50.0.2661.89 Safari/537.36", "browser": {"name": "Silk", "version": "50.2.1", "major": "50"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "50.0.2661.89"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/47.1.81 like Chrome/47.0.2526.83 Mobile Safari/537.36", "browser": {"name": "Silk", "version": "47.1.81", "major": "47"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "47.0.2526.83"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFASWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/69.2.14 like Chrome/69.0.3497.100 Safari/537.36", "browser": {"name": "Silk", "version": "69.2.14", "major": "69"}, "cpu": {}, "device": {"type": "tablet", "model": "KFASWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/68.2.6 like Chrome/68.0.3440.85 Safari/537.36", "browser": {"name": "Silk", "version": "68.2.6", "major": "68"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "68.0.3440.85"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFTBWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.3.3 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.3.3", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTBWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/66.3.1 like Chrome/66.0.3359.158 Safari/537.36", "browser": {"name": "Silk", "version": "66.3.1", "major": "66"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "66.0.3359.158"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.3.13 like Chrome/79.0.3945.116 Safari/537.36", "browser": {"name": "Silk", "version": "79.3.13", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/62.6.1 like Chrome/62.0.3202.84 Safari/537.36", "browser": {"name": "Silk", "version": "62.6.1", "major": "62"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "62.0.3202.84"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFOT) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.7.4 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.7.4", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFOT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFARWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFARWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFASWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.3.3 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.3.3", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFASWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.35 like Chrome/34.0.1847.137 Safari/537.36", "browser": {"name": "Silk", "version": "3.35", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "34.0.1847.137"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/80.2.5 like Chrome/80.0.3987.119 Safari/537.36", "browser": {"name": "Silk", "version": "80.2.5", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/63.1.34 like Chrome/63.0.3239.111 Safari/537.36", "browser": {"name": "Silk", "version": "63.1.34", "major": "63"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/62.6.1 like Chrome/62.0.3202.84 Safari/537.36", "browser": {"name": "Silk", "version": "62.6.1", "major": "62"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "62.0.3202.84"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.2.22 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.2.22", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.1.117 like Chrome/75.0.3770.89 Safari/537.36", "browser": {"name": "Silk", "version": "75.1.117", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.89"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/51.2.1 like Chrome/51.0.2704.81 Safari/537.36", "browser": {"name": "Silk", "version": "51.2.1", "major": "51"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "51.0.2704.81"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.70 like Chrome/39.0.2171.93 Safari/537.36", "browser": {"name": "Silk", "version": "3.70", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "39.0.2171.93"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/74.2.9 like Chrome/74.0.3729.136 Safari/537.36", "browser": {"name": "Silk", "version": "74.2.9", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "74.0.3729.136"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/56.2.4 like Chrome/56.0.2924.87 Safari/537.36", "browser": {"name": "Silk", "version": "56.2.4", "major": "56"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "56.0.2924.87"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFMUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.2.1 like Chrome/78.0.3904.96 Safari/537.36", "browser": {"name": "Silk", "version": "78.2.1", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.96"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFMEWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.4.8 like Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Silk", "version": "75.4.8", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMEWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/67.4.1 like Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Silk", "version": "67.4.1", "major": "67"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.2.1 like Chrome/78.0.3904.96 Safari/537.36", "browser": {"name": "Silk", "version": "78.2.1", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.96"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.41 like Chrome/37.0.2026.117 Safari/537.36", "browser": {"name": "Silk", "version": "3.41", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "37.0.2026.117"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFMEWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.5.26 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.5.26", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMEWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFMUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.3.6 like Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Silk", "version": "76.3.6", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.6.17 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.6.17", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.3; en-us; KFAPWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.47 like Chrome/37.0.2026.117 Safari/537.36", "browser": {"name": "Silk", "version": "3.47", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAPWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "37.0.2026.117"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/63.1.34 like Chrome/63.0.3239.111 Safari/537.36", "browser": {"name": "Silk", "version": "63.1.34", "major": "63"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/68.3.20 like Chrome/68.0.3440.85 Safari/537.36", "browser": {"name": "Silk", "version": "68.3.20", "major": "68"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "68.0.3440.85"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/50.2.1 like Chrome/50.0.2661.89 Safari/537.36", "browser": {"name": "Silk", "version": "50.2.1", "major": "50"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "50.0.2661.89"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/50.2.1 like Chrome/50.0.2661.89 Safari/537.36", "browser": {"name": "Silk", "version": "50.2.1", "major": "50"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "50.0.2661.89"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/50.2.1 like Chrome/50.0.2661.89 Safari/537.36", "browser": {"name": "Silk", "version": "50.2.1", "major": "50"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "50.0.2661.89"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFTBWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.4.3 like Chrome/77.0.3865.116 Safari/537.36", "browser": {"name": "Silk", "version": "77.4.3", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTBWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.1.117 like Chrome/75.0.3770.89 Safari/537.36", "browser": {"name": "Silk", "version": "75.1.117", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.89"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; de-de; KFOT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.70 like Chrome/39.0.2171.93 Safari/537.36", "browser": {"name": "Silk", "version": "3.70", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFOT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "39.0.2171.93"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/53.3.5 like Chrome/53.0.2785.134 Safari/537.36", "browser": {"name": "Silk", "version": "53.3.5", "major": "53"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "53.0.2785.134"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.2.22 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.2.22", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-gb; KFOT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.47 like Chrome/37.0.2026.117 Safari/537.36", "browser": {"name": "Silk", "version": "3.47", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFOT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "37.0.2026.117"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.4.8 like Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Silk", "version": "75.4.8", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/57.2.2 like Chrome/57.0.2987.132 Safari/537.36", "browser": {"name": "Silk", "version": "57.2.2", "major": "57"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "57.0.2987.132"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFASWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.4.3 like Chrome/77.0.3865.116 Safari/537.36", "browser": {"name": "Silk", "version": "77.4.3", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFASWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFSOWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/63.1.34 like Chrome/63.0.3239.111 Safari/537.36", "browser": {"name": "Silk", "version": "63.1.34", "major": "63"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/66.2.1 like Chrome/66.0.3359.126 Safari/537.36", "browser": {"name": "Silk", "version": "66.2.1", "major": "66"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "66.0.3359.126"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFTBWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.4.8 like Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Silk", "version": "75.4.8", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTBWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/80.2.5 like Chrome/80.0.3987.119 Safari/537.36", "browser": {"name": "Silk", "version": "80.2.5", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFSOWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/45.1.99 like Chrome/45.0.2454.94 Safari/537.36", "browser": {"name": "Silk", "version": "45.1.99", "major": "45"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "45.0.2454.94"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/69.4.17 like Chrome/69.0.3497.100 Safari/537.36", "browser": {"name": "Silk", "version": "69.4.17", "major": "69"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFAPWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.3.6 like Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Silk", "version": "76.3.6", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAPWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/70.4.2 like Chrome/70.0.3538.80 Safari/537.36", "browser": {"name": "Silk", "version": "70.4.2", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "70.0.3538.80"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.6.17 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.6.17", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/68.2.6 like Chrome/68.0.3440.85 Safari/537.36", "browser": {"name": "Silk", "version": "68.2.6", "major": "68"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "68.0.3440.85"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFSOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/77.2.19 like Chrome/77.0.3865.92 Safari/537.36", "browser": {"name": "Silk", "version": "77.2.19", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "77.0.3865.92"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/65.2.3 like Chrome/65.0.3325.144 Safari/537.36", "browser": {"name": "Silk", "version": "65.2.3", "major": "65"}, "cpu": {}, "device": {"type": "tablet", "model": "KFDOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "65.0.3325.144"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.4; KFJWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.6.17 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.6.17", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFJWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "4.0.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/74.3.14 like Chrome/74.0.3729.157 Safari/537.36", "browser": {"name": "Silk", "version": "74.3.14", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFTBWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/79.4.9 like Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Silk", "version": "79.4.9", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTBWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.1.117 like Chrome/75.0.3770.89 Safari/537.36", "browser": {"name": "Silk", "version": "75.1.117", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.89"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/64.3.4 like Chrome/64.0.3282.137 Safari/537.36", "browser": {"name": "Silk", "version": "64.3.4", "major": "64"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "64.0.3282.137"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFSOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.2.1 like Chrome/76.0.3809.111 Safari/537.36", "browser": {"name": "Silk", "version": "76.2.1", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.111"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFARWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFARWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFSOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.4.8 like Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Silk", "version": "75.4.8", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/73.6.17 like Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Silk", "version": "73.6.17", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/70.4.2 like Chrome/70.0.3538.80 Safari/537.36", "browser": {"name": "Silk", "version": "70.4.2", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "70.0.3538.80"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFSOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/78.4.6 like Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Silk", "version": "78.4.6", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFARWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.3.3 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.3.3", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFARWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFAUWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/65.5.3 like Chrome/65.0.3325.144 Safari/537.36", "browser": {"name": "Silk", "version": "65.5.3", "major": "65"}, "cpu": {}, "device": {"type": "tablet", "model": "KFAUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "65.0.3325.144"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/48.2.2 like Chrome/48.0.2564.95 Safari/537.36", "browser": {"name": "Silk", "version": "48.2.2", "major": "48"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "48.0.2564.95"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.3; en-gb; KFTHWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.47 like Chrome/37.0.2026.117 Safari/537.36", "browser": {"name": "Silk", "version": "3.47", "major": "3"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "37.0.2026.117"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/55.2.6 like Chrome/55.0.2883.91 Safari/537.36", "browser": {"name": "Silk", "version": "55.2.6", "major": "55"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "55.0.2883.91"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFASWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.2.1 like Chrome/76.0.3809.111 Safari/537.36", "browser": {"name": "Silk", "version": "76.2.1", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFASWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.111"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; AFTT) AppleWebKit/537.36 (KHTML, like Gecko) Silk/70.5.4 like Chrome/70.0.3538.110 Safari/537.36", "browser": {"name": "Silk", "version": "70.5.4", "major": "70"}, "cpu": {}, "device": {"type": "smarttv", "model": "T", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "70.0.3538.110"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWA) AppleWebKit/537.36 (KHTML, like Gecko) Silk/70.2.2 like Chrome/70.0.3538.64 Safari/537.36", "browser": {"name": "Silk", "version": "70.2.2", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWA", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "70.0.3538.64"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/70.5.4 like Chrome/70.0.3538.110 Safari/537.36", "browser": {"name": "Silk", "version": "70.5.4", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "70.0.3538.110"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/59.3.1 like Chrome/59.0.3071.117 Safari/537.36", "browser": {"name": "Silk", "version": "59.3.1", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "59.0.3071.117"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFOT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/59.3.1 like Chrome/59.0.3071.117 Safari/537.36", "browser": {"name": "Silk", "version": "59.3.1", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "KFOT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "59.0.3071.117"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/59.3.1 like Chrome/59.0.3071.117 Safari/537.36", "browser": {"name": "Silk", "version": "59.3.1", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "59.0.3071.117"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/59.3.1 like Chrome/59.0.3071.117 Safari/537.36", "browser": {"name": "Silk", "version": "59.3.1", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "59.0.3071.117"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.4; KFJWI Build/IMM76D) AppleWebKit/537.36 (KHTML, like Gecko) Silk/59.2.2 like Chrome/59.0.3071.92 Safari/537.36", "browser": {"name": "Silk", "version": "59.2.2", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "KFJWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "59.0.3071.92"}, "os": {"name": "Android", "version": "4.0.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/59.3.1 like Chrome/59.0.3071.117 Safari/537.36", "browser": {"name": "Silk", "version": "59.3.1", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "KFFOWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "59.0.3071.117"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSAWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/59.1.92 like Chrome/59.0.3071.92 Safari/537.36", "browser": {"name": "Silk", "version": "59.1.92", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "59.0.3071.92"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; AFTB) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.2.22 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.2.22", "major": "75"}, "cpu": {}, "device": {"type": "smarttv", "model": "B", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.3.3 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.3.3", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.3; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/58.4.1 like Chrome/58.0.3029.83 Safari/537.36", "browser": {"name": "Silk", "version": "58.4.1", "major": "58"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTT", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "58.0.3029.83"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.4.8 like Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Silk", "version": "75.4.8", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFMEWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.4.8 like Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Silk", "version": "75.4.8", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMEWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFMEWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/76.1.107 like Chrome/76.0.3809.89 Safari/537.36", "browser": {"name": "Silk", "version": "76.1.107", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMEWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "76.0.3809.89"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFARWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/58.3.1 like Chrome/58.0.3029.83 Safari/537.36", "browser": {"name": "Silk", "version": "58.3.1", "major": "58"}, "cpu": {}, "device": {"type": "tablet", "model": "KFARWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "58.0.3029.83"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/75.2.22 like Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Silk", "version": "75.2.22", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "KFGIWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFMEWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/60.2.12 like Chrome/60.0.3112.107 Safari/537.36", "browser": {"name": "Silk", "version": "60.2.12", "major": "60"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMEWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "60.0.3112.107"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/69.2.14 like Chrome/69.0.3497.100 Safari/537.3", "browser": {"name": "Silk", "version": "69.2.14", "major": "69"}, "cpu": {}, "device": {"type": "tablet", "model": "KFTHWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "4.4.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; haw-US; KFJWI Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Silk/2.1 Mobile Safari/535.19 Silk-Accelerated=false", "browser": {"name": "Silk", "version": "2.1", "major": "2"}, "cpu": {}, "device": {"type": "tablet", "model": "KFJWI", "vendor": "Amazon"}, "engine": {"name": "WebKit", "version": "535.19"}, "os": {"name": "Android", "version": "4.0.3"}}]