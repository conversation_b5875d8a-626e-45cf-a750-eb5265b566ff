[{"ua": "Mozilla/5.0 (X11; U; FreeBSD amd64; en-us) AppleWebKit/531.2+ (KHTML, like Gecko) Safari/531.2+ Epiphany/2.30.0", "browser": {"name": "Epiphany", "version": "2.30.0", "major": "2"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "531.2"}, "os": {"name": "FreeBSD"}}, {"ua": "Mozilla/5.0 (X11; U; FreeBSD i386; pl; rv:********) Gecko/20080213 Epiphany/2.20 Firefox/********", "browser": {"name": "Epiphany", "version": "2.20", "major": "2"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Gecko", "version": "********"}, "os": {"name": "FreeBSD"}}, {"ua": "Mozilla/5.0 (X11; U; FreeBSD i386; en; rv:********) Gecko/20080213 Epiphany/2.20 Firefox/********", "browser": {"name": "Epiphany", "version": "2.20", "major": "2"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Gecko", "version": "********"}, "os": {"name": "FreeBSD"}}, {"ua": "Mozilla/5.0 (X11; U; FreeBSD i386; en-US; rv:1.8.1) Gecko/20070322 Epiphany/2.18", "browser": {"name": "Epiphany", "version": "2.18", "major": "2"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Gecko", "version": "1.8.1"}, "os": {"name": "FreeBSD"}}]