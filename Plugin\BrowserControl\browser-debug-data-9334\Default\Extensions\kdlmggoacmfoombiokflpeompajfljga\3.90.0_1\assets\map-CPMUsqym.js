import{a as u,c as d}from"./_baseUniq-CDn7CWnQ.js";import{d as i,a as b,e as l,f as o}from"./user-config-BrqC82sm.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},n=new Error().stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="3a0c944c-260b-41e1-9a1a-0378163613ba",e._sentryDebugIdIdentifier="sentry-dbid-3a0c944c-260b-41e1-9a1a-0378163613ba")}catch{}})();function y(e){var n=e==null?0:e.length;return n?u(e):[]}function h(e){var n=e==null?0:e.length;return n?e[n-1]:void 0}function g(e,n){var a=-1,t=i(e)?Array(e.length):[];return d(e,function(r,s,f){t[++a]=n(r,s,f)}),t}function m(e,n){var a=l(e)?o:g;return a(e,b(n))}export{g as b,y as f,h as l,m};
//# sourceMappingURL=map-CPMUsqym.js.map
