// Plugin/BrowserControl/test_chrome_launch.js - 测试Chrome启动
const { spawn } = require('child_process');
const CDP = require('chrome-remote-interface');

async function testChromeLaunch() {
    console.log('=== 测试Chrome启动和CDP连接 ===\n');
    
    try {
        // 方法1: 使用spawn直接启动Chrome
        console.log('方法1: 使用spawn启动Chrome...');
        
        const chromeArgs = [
            '--remote-debugging-port=9222',
            '--new-window',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--no-first-run',
            '--disable-default-apps',
            'about:blank'
        ];
        
        // 尝试不同的Chrome可执行文件路径
        const chromePaths = [
            'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
            'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
            'chrome',
            'google-chrome',
            'chromium'
        ];

        let chromeProcess = null;
        let chromePath = null;

        for (const path of chromePaths) {
            try {
                console.log(`尝试启动Chrome: ${path}`);
                chromeProcess = spawn(path, chromeArgs, {
                    detached: false,
                    stdio: ['ignore', 'pipe', 'pipe']
                });

                // 等待一下看是否启动成功
                await new Promise(resolve => setTimeout(resolve, 1000));

                if (!chromeProcess.killed) {
                    chromePath = path;
                    console.log(`Chrome启动成功: ${path}`);
                    break;
                } else {
                    console.log(`Chrome进程已退出: ${path}`);
                }
            } catch (error) {
                console.log(`启动失败: ${path} - ${error.message}`);
            }
        }
        
        if (!chromeProcess) {
            throw new Error('无法找到Chrome可执行文件');
        }
        
        // 等待Chrome启动
        console.log('等待Chrome启动...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // 测试CDP连接
        console.log('测试CDP连接...');
        
        let connected = false;
        for (let i = 0; i < 10; i++) {
            try {
                console.log(`尝试连接CDP (${i + 1}/10)...`);
                
                // 获取可用的标签页
                const targets = await CDP.List({ port: 9222 });
                console.log(`找到 ${targets.length} 个标签页`);
                
                if (targets.length > 0) {
                    console.log('标签页列表:');
                    targets.forEach((target, index) => {
                        console.log(`  ${index + 1}. ${target.title} - ${target.url}`);
                    });
                    
                    // 连接到第一个标签页
                    const target = targets[0];
                    const client = await CDP({ target: target.id, port: 9222 });
                    
                    const { Page, Runtime } = client;
                    await Page.enable();
                    await Runtime.enable();
                    
                    console.log('CDP连接成功!');
                    
                    // 测试导航
                    console.log('测试导航到Bilibili...');
                    await Page.navigate({ url: 'https://www.bilibili.com' });
                    await Page.loadEventFired();
                    
                    console.log('页面加载完成!');
                    
                    // 获取页面信息
                    const result = await Runtime.evaluate({
                        expression: `({
                            title: document.title,
                            url: window.location.href,
                            readyState: document.readyState
                        })`,
                        returnByValue: true
                    });
                    
                    console.log('页面信息:', result.result.value);
                    
                    await client.close();
                    connected = true;
                    break;
                }
            } catch (error) {
                console.log(`连接失败: ${error.message}`);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        if (connected) {
            console.log('\n=== 测试成功! ===');
            console.log('Chrome启动和CDP连接都正常工作');
        } else {
            console.log('\n=== 测试失败 ===');
            console.log('无法建立CDP连接');
        }
        
    } catch (error) {
        console.error('测试过程中发生错误:', error.message);
        console.error('错误堆栈:', error.stack);
    }
}

// 运行测试
if (require.main === module) {
    testChromeLaunch().catch(console.error);
}

module.exports = { testChromeLaunch };
