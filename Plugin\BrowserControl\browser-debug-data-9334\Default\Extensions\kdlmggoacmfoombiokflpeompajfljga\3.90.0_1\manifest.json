{"action": {}, "background": {"service_worker": "service-worker-loader.js", "type": "module"}, "commands": {"open-app": {"description": "Open ChatHub app", "suggested_key": {"default": "Alt+J", "linux": "Alt+J", "mac": "Command+J", "windows": "Alt+J"}}}, "declarative_net_request": {"rule_resources": [{"enabled": true, "id": "ruleset_x_frame_options", "path": "src/rules/x-frame-options.json"}, {"enabled": true, "id": "ruleset_chatgpt", "path": "src/rules/chatgpt.json"}, {"enabled": true, "id": "ruleset_bing", "path": "src/rules/bing.json"}, {"enabled": true, "id": "ruleset_ddg", "path": "src/rules/ddg.json"}, {"enabled": true, "id": "ruleset_qianwen", "path": "src/rules/qianwen.json"}, {"enabled": true, "id": "ruleset_ollama", "path": "src/rules/ollama.json"}, {"enabled": true, "id": "ruleset_pplx", "path": "src/rules/pplx.json"}, {"enabled": true, "id": "ruleset_anthropic", "path": "src/rules/anthropic.json"}]}, "default_locale": "en", "description": "__MSG_appDesc__", "host_permissions": ["<all_urls>"], "icons": {"128": "src/assets/icon.png", "16": "src/assets/icon.png", "32": "src/assets/icon.png", "48": "src/assets/icon.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAos6UBxsKjXHiMNnRNz7xV12cCJWbFq5qA5r1GBHSmyOECbb2/cNqCOAvsin/KLqW6A6jiFcckJR4lqM97Z5pXIYy/Bh9/cEpF4+g1lHK0dzW717B6ba2cS3gIq4NDNg1wGPXI/9SqrmHGFYPBuEH/m384fqrtcFE888v1pY9zHb7mMGL3O/axQPHda/ntGcHHBVSkwKk0FDlmTRYWtTp8zTO2mvMV0EXCnqwLZVHzdlutjMTjG1AZYXfSUwxTWQs4ljpSpJCbV5PgFvSczKynUgsyt5br+2qSdxg6XM5h1ypL6i6Yr/zttuL1SKhfy7Yw2qwgkevrOs0ApNoBbTAOQIDAQAB", "manifest_version": 3, "name": "__MSG_appName__", "permissions": ["storage", "unlimitedStorage", "sidePanel", "declarativeNetRequestWithHostAccess"], "side_panel": {"default_path": "sidepanel.html"}, "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "3.90.0"}