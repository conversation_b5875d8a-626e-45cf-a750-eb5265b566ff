[{"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-G955U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/5.4 Chrome/51.0.2704.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "5.4", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G955U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "51.0.2704.106"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G965U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-G532M Build/MMB29T) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G532M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A102U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A102U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G930F Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-S767VL) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-S767VL", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-G950F Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/5.2 Chrome/51.0.2704.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "5.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "51.0.2704.106"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0.2; C6833 Build/14.5.A.0.242) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "C6833", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "5.0.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0.2; SAMSUNG SM-G530H Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.3 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.3", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G530H", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "5.0.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0.2; SAMSUNG SM-T355 Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T355", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "5.0.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0.2; SAMSUNG SM-T815) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T815", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "5.0.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0; SAMSUNG SM-N900A Build/LRX21V) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N900A", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "5.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG SM-A310F Build/LMY47X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A310F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG SM-G388F/G388FXXS1BPL2) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G388F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG SM-G925P Build/LMY47X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G925P", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG SM-J105M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J105M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG SM-J200GU) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J200GU", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG SM-J320M Build/LMY47V) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J320M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG SM-J700F Build/LMY48B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J700F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG SM-J5108 Build/LMY47X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J5108", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG SM-T335/T335XXS1BRL3) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T335", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; 6055K Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "6055K"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; NX531J) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "NX531J"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-A320FL Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A320FL", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-A700H Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A700H", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-A9000 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A9000", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-G389F/G389FXXU1ARF2) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G389F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-G900F/G900FXXU1CPJ6) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G900F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-G920W8 Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G920W8", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-G5510) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G5510", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-J320FN Build/RE Dream UX V2.5) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J320FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-J500G) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J500G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-J710GN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J710GN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-N920A) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N920A", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-S120VL) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-S120VL", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-T815) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T815", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; DIG-L21HN Build/HUAWEIDIG-L21HN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "DIG-L21HN", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; EVA-L19 Build/HUAWEIEVA-L19) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "EVA-L19", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Redmi Note 4X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 4X", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-A310F/A310FXXU4CRB1 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A310F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-A710M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A710M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-G390F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G390F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-G610K/KKU1BQL3 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G610K", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-G920F/G920FXXS6ETI6) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G920F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-G925F/G925FXXU6ERF5 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G925F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-G925S) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G925S", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-G930U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-G935F/G935FXXU2DRD1 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G935F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-G5700 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G5700", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-J710F/J710FXXU5BRG3) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J710F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-J727R4) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J727R4", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-N920R6) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N920R6", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-T585) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T585", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-T719Y Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T719Y", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-T819Y Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T819Y", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SCV32 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SCV32"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Wileyfox Spark X Build/NBD92Q) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "Wileyfox Spark X"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; MI MAX 2 Build/NMF26F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "MI MAX 2", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SAMSUNG SM-A530F/A530FXXU2ARD1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A530F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SAMSUNG SM-G5528 Build/NMF26X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G5528", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SAMSUNG SM-J250F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/88.0.4324.93 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J250F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SAMSUNG SM-J510FQ Build/NMF26X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J510FQ", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SAMSUNG SM-N9500 Build/NMF26X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N9500", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SAMSUNG SM-T350 Build/NMF26X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T350", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SAMSUNG SM-T355 Build/NMF26X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T355", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SAMSUNG SM-T380 Build/NMF26X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/84.0.4147.89 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T380", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SAMSUNG SM-T395) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T395", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SAMSUNG SM-T560NU; in-id) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T560N", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi 4) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 4", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; SM-J500F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J500F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Z999) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "Z999"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; AUM-L29 Build/HONORAUM-L29) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "AUM-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; F5122 Build/34.4.A.2.118) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "F5122", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; H4413 Build/50.1.A.13.83) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "H4413"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LLD-L31 Build/HONORLLD-L31) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "LLD-L31", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; PIC-LX9) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "PIC-LX9", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-A320FL/A320FLXXS7CTFK Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A320FL", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-A320FL/A320FLXXU3CSA2 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A320FL", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-A520F/A520FXXSFCTG8 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A520F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-A520F/A520FXXU8CSC1 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A520F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-A520F/A520FXXUECTDA Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A520F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-A520L) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A520L", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-A520W Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A520W", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-A530F/A530FXXU2BRG1 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A530F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-A600FN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A600FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-A810YZ Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A810YZ", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-C701F Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-C701F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-C7000 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-C7000", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G570F Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/54.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G570F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "54.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G570Y) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G570Y", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G885F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G885F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G930A Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930A", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G930F/G930FXXS7ESL5 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G930F/G930FXXU5ESD2 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G930K/KKU2ERJ1 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930K", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G930R4 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930R4", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G930S Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930S", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G935F Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/54.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G935F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "54.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G935K/KKU2ERJ1 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G935K", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G935L Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G935L", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G935S Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G935S", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G935V Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G935V", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G950F/G950FXXU3CRGH Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G955F/G955FXXU4CRI5 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G955F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G955U1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G955U1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G960F/G960FXXU2BRJ3 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G960XU Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960XU", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G965N Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965N", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G9550 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G9550", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-J330FN Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J330FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-J330G Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J330G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-J337P Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/80.0.0403.49393 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J337P", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "80.0.0403.49393"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-J600FN Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/54.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J600FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "54.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-J600GT Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J600GT", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-J737U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J737U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-J810M Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J810M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-N9508 Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N9508", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SC-02H Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SC-02", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SC-04J Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SC-04", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; STF-L09 Build/HUAWEISTF-L09) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "STF-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; AGM A9 ru) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "AGM A9 ru"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; COL-L29 Build/HUAWEICOL-L29) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "COL-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; COR-L29 Build/HUAWEICOR-L29) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "COR-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; DUB-LX1 Build/HUAWEIDUB-LX1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "DUB-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; JSN-L21 Build/HONORJSN-L21) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "JSN-L21", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; LM-Q610.FGN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "LM-Q610.FGN", "vendor": "LG"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; MI PAD 4 PLUS Build/OPM1.171019.019) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.3 Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.3", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "MI PAD 4 PLUS", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; ONEPLUS A5010 Build/OPM1.171019.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "A5010", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; PowerFiveMax2) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "PowerFiveMax2"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Redmi Note 5 Pro) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5 Pro", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-G610L) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G610L", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-G615F Build/M1AJQ) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/54.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G615F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "54.0.3071.125"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-G5510 Build/M1AJQ) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G5510", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-J327W) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J327W", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-J415FN/J415FNXXU1ARK5) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J415FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-J530G Build/M1AJQ) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J530G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-J610FN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J610FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-J701M Build/M1AJQ) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J701M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-J710F/J710FXXU5CRJ8) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J710F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-J727P Build/M1AJQ) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J727P", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-J730GM) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J730GM", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-M205F Build/M1AJQ) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.3 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.3", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M205F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-M305F Build/M1AJQ) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.3 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.3", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M305F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-N960W Build/M1AJQ) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.0 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N960W", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-P585Y Build/M1AJQ) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-P585Y", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-T580 Build/M1AJQ) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T580", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-T585/T585XXS6CTJ5) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T585", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-T830 Build/M1AJQ) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.0 Chrome/81.0.4044.138 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T830", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; View2 Plus) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "View2 Plus"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; ZTE BLADE V0920) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "BLADE V0920", "vendor": "ZTE"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; Mi 9 SE Build/PKQ1.181121.001) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.3 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.3", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi 9 SE", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; BND-L21) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "BND-L21", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; COR-L29 Build/HUAWEICOR-L29) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "COR-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; EML-L29 Build/HUAWEIEML-L29) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "EML-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; H96 Max X3) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "H96 Max X3"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; INE-LX1r) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "INE-LX1r"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; JSN-L21 Build/HONORJSN-L21) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "JSN-L21", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; LLD-L31 Build/HONORLLD-L31) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.3 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.3", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "LLD-L31", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; LYA-L29 Build/HUAWEILYA-L29) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "LYA-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; MI 8 Build/PKQ1.180729.001) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "MI 8", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Mi A1 Build/PKQ1.180917.001) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi A1", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Mi MIX 3 Build/PKQ1.180729.001) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi MIX 3"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ONEPLUS A6010 Build/PKQ1.180716.001) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "A6010", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; RMX1941) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "RMX1941", "vendor": "Realme"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Redmi Note 5) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.0 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG A80Pro) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "A80Pro", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A105F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A105F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A105FN/A105FNXXU2ASJ2 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A105FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A202F/A202FXXS2ASI3 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A202F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A202F/A202FXXU2ASJ3 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A202F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A205U1 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A205U1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A307FN/A307FNXXU2ATB1 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A307FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A405FN/A405FNXXU1ASC7 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A405FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A405FN/A405FNXXU2ASK2 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A405FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A505FN/A505FNXXS3ASK9) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A505FN/A505FNXXU1ASBG Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A530F/A530FXXS8CSL2 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A530F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A530F/A530FXXUFCTK2 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A530F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A600F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A600F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A600U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A600U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A605FN Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/85.0.4183.81 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A605FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A606Y Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A606Y", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A705FN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A705FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A705MN Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A705MN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A805F/A805FXXU1ASED) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A805F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-A2070) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A2070", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G611M Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G611M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G892U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G892U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G950F/G950FXXSBDTJ1 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G950N/KSU3DSD1 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950N", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G950W-parrot Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950W", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G955F/G955FXXSBDUA3) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G955F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G955U1 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G955U1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G960F/G960FXXU2CSB9) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G960F/G960FXXU7CSK1 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G970F/G970FXXU1ASBA Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G970F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G973N Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973N", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G975U1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G975U1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G8870 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G8870", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-G9650 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G9650", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J260AZ) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J260AZ", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J330FN/J330FNXXU4CTH2 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J330FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J337W Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J337W", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J400M Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J400M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J415FN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/88.0.4324.93 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J415FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J415G Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J415G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J530F/J530FXXS8CTK1 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J530F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J600F Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J600F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J600GF Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J600GF", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J610G Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J610G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J727S) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J727S", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J810F Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J810F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J810G Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J810G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-M105G Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.3 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.3", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M105G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-M205FN Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.3 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.3", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M205FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-M305M Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M305M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-N950F/N950FXXS8DSL3 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N950F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-N950N/KSU5DTD2) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N950N", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-N960N Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.0 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N960N", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-N975U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.0 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N975U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-P200 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-P200", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-T295 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Safari/537.36,gzip(gfe)", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T295", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-T385) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T385", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-T510 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T510", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-T720 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T720", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-T865) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.0 Chrome/71.0.3578.99 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T865", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G930F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Nokia 7.2) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "7.2", "vendor": "Nokia"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; POT-LX1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "POT-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 7 Pro) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 7 Pro", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 8T) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 8T", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A015F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/80.0.3987.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A015F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "80.0.3987.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A015G) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A015G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A025F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A025F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A105F Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A105F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A105FN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A105FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A105FN/A105FNXXU3BTD4) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A105FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A105FN/A105FNXXU5BTL1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A105FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A105G) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A105G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A107F/A107FXXS8BUC1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A107F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A107M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A107M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A115F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A115F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A115U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A115U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A202F Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A202F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A202F/A202FXXS3BTI2) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A202F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A202F/A202FXXU3BTD1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A202F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A202K/KKU5BTI1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A202K", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A205FN Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A205FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A205U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A205U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A207F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A207F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A215U1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A215U1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A217F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A217F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A217F/A217FXXU3ATJ3) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A217F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A217M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A217M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A305FN Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A305FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A305GT) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A305GT", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A307FN/A307FNXXU2BTI4) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A307FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A315G Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/87.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A315G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A405FN/A405FNXXU3BTK4) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A405FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A415F/A415FXXU1BTK5) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A415F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A505FM) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505FM", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A505FN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A505FN/A505FNXXS5BTK1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A505G) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A505YN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505YN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A515F/A515FXXU4CTJ1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A515F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A516B/A516BXXU1ATE6) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A516B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A600FN Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A600FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A600FN/A600FNXXU5CTC6 Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A600FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A600G Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A600G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A605FN Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A605FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A605FN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A605FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A605G Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A605G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A705FN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A705FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A705MN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A705MN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A716U1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A716U1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A750FN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A750FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A750N) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A750N", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A920F Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/80.0.3987.162 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A920F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "80.0.3987.162"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-A7050) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A7050", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-F9160) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-F9160", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-G770F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G770F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-G960F/G960FXXSBETH2 Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-G960N/KSU3ETH3) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960N", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-G965F/G965FXXSCFTK2 Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-G970F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G970F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-G973N) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973N", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G975F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-G977U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G977U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G981B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-G988B/G988BXXU2ATE6) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G988B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-G9700) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G9700", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J400F Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.4 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.4", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J400F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J400F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J400F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J400G Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J400G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J600F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J600F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J600G Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J600G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J600GT Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J600GT", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J610F Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J610F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J610FN Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J610FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J610FN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J610FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J610G Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J610G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J720F Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.2 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.2", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J720F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J810F Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J810F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J810G Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J810G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J810M Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J810M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-J810Y) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J810Y", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-M025F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M025F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-M105G Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.3 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.3", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M105G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-M105M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M105M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-M115F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M115F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-M205G Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.3 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.3", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M205G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-M305F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M305F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-M307F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M307F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-M315F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M315F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-N770F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N770F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-N970F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.0 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N970F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-N981B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N981B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-P200) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-P200", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-S102DL) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-S102DL", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-S115DL) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-S115DL", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-T290 Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.2", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T290", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-T295) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T295", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-T295) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T295", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-T500) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T500", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-T510 Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.0 Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T510", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-T510) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T510", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-T515) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T515", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-T595 Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T595", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-T595) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T595", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-T725 Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "9.4", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T725", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-T835) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T835", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SC-03K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SC-03", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SH-R10E) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SH-R10E"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; motorola one) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.1", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "one", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; M2007J17G) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "M2007J17G", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-A405FM) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A405FM", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-A426B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A426B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-A505FM) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505FM", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-A505FN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-A515F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A515F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-A515F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A515F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-A705FN) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A705FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-A715F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A715F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-A805F/A805FXXU5DUC7) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A805F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-F415F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-F415F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G770F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G770F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G970F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G970F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G970U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G970U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G975F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G980F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G980F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G985F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G985F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G985F/G985FXXU7DUC7) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G985F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G988B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G988B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G9880) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G9880", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-M215F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M215F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-M307F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M307F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-M315F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M315F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-M315F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M315F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-M315F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M315F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-M315F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M315F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-M317F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M317F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-M317F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M317F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-M317F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M317F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-M515F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M515F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-M625F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M625F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N770F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N770F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N770F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N770F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N970F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N970F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N970F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N970F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N970F/N970FXXS6EUA1) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N970F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N970U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N970U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N971N) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N971N", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N971N) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N971N", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N971N/KSU1FUCD) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N971N", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N975F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N975F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N975F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N975F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N975U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N975U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N980F/N980FXXU1DUB5) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N980F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N985F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N985F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N9750) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.1 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.1", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N9750", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-P610) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.0 Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-P610", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-P610) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-P610", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-P615) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-P615", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-P615) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-P615", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-T870) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/12.1 Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T870", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SC-04L) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SC-04", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SCV45) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.2 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "13.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "SCV45"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 8.1.0; SM-J410G Build/M1AJB) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.0 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J410G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux;Android 7.0;SAMSUNG SM-T830 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/84.0.4147.89 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T830", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux;Android 7.0;SAMSUNG SM-T830 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/86.0.4240.111 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "7.2", "major": "7"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T830", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.111"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-N975F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/14.0 Chrome/87.0.4280.141 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "14.0", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N975F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-T505) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/15.0 Chrome/90.0.4430.210 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "15.0", "major": "15"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T505", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "90.0.4430.210"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-J327W Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/60.0.3112.116 Mobile Safari/537.36 SamsungBrowser/CrossApp/0.1.118", "browser": {"name": "Samsung Internet", "version": "CrossApp", "major": ""}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J327W", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "60.0.3112.116"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G986B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/15.0 Chrome/90.0.4430.210 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "15.0", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G986B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "90.0.4430.210"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/25.0 Chrome/121.0.0.0 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "25.0", "major": "25"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "121.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/26.0 Chrome/122.0.0.0 Mobile Safari/537.36", "browser": {"name": "Samsung Internet", "version": "26.0", "major": "26"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "122.0.0.0"}, "os": {"name": "Android", "version": "10"}}]