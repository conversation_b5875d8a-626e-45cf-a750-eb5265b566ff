// Plugin/BrowserControl/BrowserControl.js - 智能浏览器控制VCP插件
const CDP = require('chrome-remote-interface');
const { chromium } = require('playwright');
const open = require('open');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');

class BrowserControl {
    constructor() {
        this.browser = null;
        this.page = null;
        this.cdpClient = null;
        this.config = {
            apiKey: process.env.BROWSER_CONTROL_API_KEY || 'sk-t8zcWN8dFJxaD18REKRrdLzlngOJlmpkzvfomfyLwaYMNcO6',
            apiEndpoint: process.env.BROWSER_CONTROL_API_ENDPOINT || 'https://yuanplus.cloud',
            model: process.env.BROWSER_CONTROL_MODEL || 'gemini-2.0-flash-exp',
            headless: process.env.BROWSER_CONTROL_HEADLESS === 'true',
            timeout: parseInt(process.env.BROWSER_CONTROL_TIMEOUT) || 30000,
            screenshotFormat: process.env.BROWSER_CONTROL_SCREENSHOT_FORMAT || 'webp'
        };
        this.screenshotDir = path.join(__dirname, 'screenshots');
    }

    async initialize() {
        try {
            // 确保截图目录存在
            await fs.mkdir(this.screenshotDir, { recursive: true });
            
            // 启动浏览器
            this.browser = await chromium.launch({
                headless: this.config.headless,
                args: ['--remote-debugging-port=9222', '--no-sandbox', '--disable-setuid-sandbox']
            });
            
            // 创建新页面
            this.page = await this.browser.newPage();
            
            // 设置视口
            await this.page.setViewportSize({ width: 1920, height: 1080 });
            
            this.log('浏览器初始化成功');
            return true;
        } catch (error) {
            this.log(`浏览器初始化失败: ${error.message}`);
            throw error;
        }
    }

    async execute(input) {
        try {
            const args = typeof input === 'string' ? JSON.parse(input) : input;
            
            if (!this.browser || !this.page) {
                await this.initialize();
            }

            const { action, url, query, selector, text, wait_time, scroll_direction, scroll_amount, ai_instruction } = args;

            let result = {};

            switch (action) {
                case 'open_url':
                    result = await this.openUrl(url);
                    break;
                case 'search':
                    result = await this.search(query, ai_instruction);
                    break;
                case 'click':
                    result = await this.click(selector, ai_instruction);
                    break;
                case 'screenshot':
                    result = await this.takeScreenshot();
                    break;
                case 'fill_form':
                    result = await this.fillForm(selector, text, ai_instruction);
                    break;
                case 'scroll':
                    result = await this.scroll(scroll_direction, scroll_amount);
                    break;
                case 'wait':
                    result = await this.wait(wait_time);
                    break;
                case 'get_elements':
                    result = await this.getInteractiveElements();
                    break;
                case 'navigate':
                    result = await this.navigate(args.direction);
                    break;
                case 'close':
                    result = await this.close();
                    break;
                default:
                    throw new Error(`不支持的操作类型: ${action}`);
            }

            return JSON.stringify({
                status: 'success',
                action: action,
                result: result,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            this.log(`执行失败: ${error.message}`);
            return JSON.stringify({
                status: 'error',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    async openUrl(url) {
        if (!url) {
            throw new Error('URL参数不能为空');
        }

        await this.page.goto(url, { waitUntil: 'networkidle' });
        const title = await this.page.title();
        
        this.log(`成功打开页面: ${title}`);
        
        return {
            url: url,
            title: title,
            message: '页面加载成功'
        };
    }

    async search(query, aiInstruction) {
        if (!query) {
            throw new Error('搜索关键词不能为空');
        }

        // 获取页面元素
        const elements = await this.getInteractiveElements();
        
        // 使用AI分析如何执行搜索
        const aiDecision = await this.getAIDecision(
            `在当前页面搜索"${query}"`,
            aiInstruction,
            elements
        );

        // 执行AI建议的操作
        const result = await this.executeAIDecision(aiDecision);
        
        return {
            query: query,
            ai_decision: aiDecision,
            result: result,
            message: '搜索操作完成'
        };
    }

    async click(selector, aiInstruction) {
        let element;
        
        if (selector) {
            // 使用提供的选择器
            element = await this.page.$(selector);
            if (!element) {
                throw new Error(`找不到元素: ${selector}`);
            }
        } else if (aiInstruction) {
            // 使用AI分析要点击的元素
            const elements = await this.getInteractiveElements();
            const aiDecision = await this.getAIDecision(
                `点击操作: ${aiInstruction}`,
                aiInstruction,
                elements
            );
            
            return await this.executeAIDecision(aiDecision);
        } else {
            throw new Error('必须提供selector或ai_instruction参数');
        }

        await element.click();
        
        return {
            selector: selector,
            message: '点击操作成功'
        };
    }

    async takeScreenshot() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `screenshot-${timestamp}.${this.config.screenshotFormat}`;
        const filepath = path.join(this.screenshotDir, filename);
        
        const screenshot = await this.page.screenshot({
            path: filepath,
            type: this.config.screenshotFormat,
            fullPage: true
        });

        // 转换为base64
        const base64 = screenshot.toString('base64');
        const dataUrl = `data:image/${this.config.screenshotFormat};base64,${base64}`;

        return {
            filename: filename,
            filepath: filepath,
            dataUrl: dataUrl,
            message: '截图成功'
        };
    }

    async fillForm(selector, text, aiInstruction) {
        let element;
        
        if (selector) {
            element = await this.page.$(selector);
            if (!element) {
                throw new Error(`找不到表单元素: ${selector}`);
            }
        } else if (aiInstruction) {
            const elements = await this.getInteractiveElements();
            const aiDecision = await this.getAIDecision(
                `填写表单: ${aiInstruction}，内容: ${text}`,
                aiInstruction,
                elements
            );
            
            return await this.executeAIDecision(aiDecision);
        } else {
            throw new Error('必须提供selector或ai_instruction参数');
        }

        await element.fill(text);
        
        return {
            selector: selector,
            text: text,
            message: '表单填写成功'
        };
    }

    async scroll(direction, amount = 500) {
        const scrollMap = {
            'up': { x: 0, y: -amount },
            'down': { x: 0, y: amount },
            'left': { x: -amount, y: 0 },
            'right': { x: amount, y: 0 }
        };

        const scrollDelta = scrollMap[direction];
        if (!scrollDelta) {
            throw new Error(`不支持的滚动方向: ${direction}`);
        }

        await this.page.mouse.wheel(scrollDelta.x, scrollDelta.y);
        
        return {
            direction: direction,
            amount: amount,
            message: '滚动操作成功'
        };
    }

    async wait(waitTime) {
        await this.page.waitForTimeout(waitTime);
        
        return {
            wait_time: waitTime,
            message: '等待完成'
        };
    }

    async navigate(direction) {
        switch (direction) {
            case 'back':
                await this.page.goBack();
                break;
            case 'forward':
                await this.page.goForward();
                break;
            case 'refresh':
                await this.page.reload();
                break;
            default:
                throw new Error(`不支持的导航方向: ${direction}`);
        }
        
        return {
            direction: direction,
            message: '导航操作成功'
        };
    }

    async close() {
        if (this.page) {
            await this.page.close();
            this.page = null;
        }
        
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
        }
        
        return {
            message: '浏览器已关闭'
        };
    }

    async getInteractiveElements() {
        try {
            const elements = await this.page.evaluate(() => {
                const interactiveElements = [];
                const selectors = [
                    'a[href]', 'button', 'input', 'textarea', 'select',
                    '[onclick]', '[role="button"]', '[role="link"]',
                    '.btn', '.button', '.link', '[data-testid]',
                    '[aria-label]', '[title]'
                ];

                selectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach((el, index) => {
                        if (el.offsetParent !== null) { // 只获取可见元素
                            const rect = el.getBoundingClientRect();
                            const elementInfo = {
                                tagName: el.tagName.toLowerCase(),
                                selector: selector,
                                index: index,
                                text: el.textContent?.trim().substring(0, 100) || '',
                                placeholder: el.placeholder || '',
                                title: el.title || '',
                                ariaLabel: el.getAttribute('aria-label') || '',
                                className: el.className || '',
                                id: el.id || '',
                                href: el.href || '',
                                type: el.type || '',
                                value: el.value || '',
                                position: {
                                    x: Math.round(rect.left),
                                    y: Math.round(rect.top),
                                    width: Math.round(rect.width),
                                    height: Math.round(rect.height)
                                },
                                visible: rect.width > 0 && rect.height > 0
                            };

                            if (elementInfo.visible) {
                                interactiveElements.push(elementInfo);
                            }
                        }
                    });
                });

                return interactiveElements.slice(0, 50); // 限制返回数量
            });

            return elements;
        } catch (error) {
            this.log(`获取页面元素失败: ${error.message}`);
            return [];
        }
    }

    async getAIDecision(task, instruction, elements) {
        try {
            const prompt = `你是一个浏览器自动化专家。请分析当前页面的可交互元素，并决定如何执行用户的指令。

任务: ${task}
用户指令: ${instruction || '无特殊指令'}

当前页面的可交互元素:
${JSON.stringify(elements, null, 2)}

请返回一个JSON对象，包含以下字段：
{
  "action": "操作类型(click/fill/scroll/wait等)",
  "target_element": "目标元素的详细信息",
  "selector": "CSS选择器或坐标",
  "text": "要填写的文本(如果需要)",
  "reasoning": "选择这个操作的原因",
  "next_steps": "后续可能需要的操作"
}

注意：
1. 优先使用具体的CSS选择器
2. 如果是搜索操作，找到搜索框和搜索按钮
3. 如果是点击操作，选择最相关的元素
4. 考虑页面的实际布局和用户意图`;

            const response = await axios.post(`${this.config.apiEndpoint}/v1/chat/completions`, {
                model: this.config.model,
                messages: [
                    {
                        role: 'user',
                        content: prompt + '\n\n请直接返回JSON格式的决策结果，不要使用工具调用。'
                    }
                ],
                temperature: 0.1,
                max_tokens: 1000
            }, {
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            let decision;
            try {
                const content = response.data.choices[0].message.content;
                // 尝试提取JSON
                const jsonMatch = content.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    decision = JSON.parse(jsonMatch[0]);
                } else {
                    // 如果没有找到JSON，创建一个默认决策
                    decision = {
                        action: 'click',
                        selector: 'input[type="search"], .search-input, [placeholder*="搜索"], [placeholder*="search"]',
                        text: task.includes('搜索') ? instruction : '',
                        reasoning: '基于常见搜索框模式进行操作'
                    };
                }
            } catch (e) {
                // 创建默认决策
                decision = {
                    action: task.includes('搜索') ? 'fill' : 'click',
                    selector: task.includes('搜索') ? 'input[type="search"], .search-input' : 'a, button',
                    text: task.includes('搜索') ? instruction : '',
                    reasoning: '使用默认策略'
                };
            }

            this.log(`AI决策: ${decision.reasoning}`);
            return decision;

        } catch (error) {
            this.log(`AI决策失败: ${error.message}`);
            throw new Error(`AI决策失败: ${error.message}`);
        }
    }

    async executeAIDecision(decision) {
        try {
            const { action, selector, text, target_element } = decision;

            switch (action) {
                case 'click':
                    if (selector) {
                        const element = await this.page.$(selector);
                        if (element) {
                            await element.click();
                            await this.page.waitForTimeout(1000); // 等待页面响应
                        } else if (target_element && target_element.position) {
                            // 使用坐标点击
                            await this.page.mouse.click(
                                target_element.position.x + target_element.position.width / 2,
                                target_element.position.y + target_element.position.height / 2
                            );
                        }
                    }
                    break;

                case 'fill':
                    if (selector && text) {
                        const element = await this.page.$(selector);
                        if (element) {
                            await element.fill(text);
                        }
                    }
                    break;

                case 'type':
                    if (text) {
                        await this.page.keyboard.type(text);
                    }
                    break;

                case 'press':
                    if (text) {
                        await this.page.keyboard.press(text);
                    }
                    break;

                case 'scroll':
                    await this.page.mouse.wheel(0, 500);
                    break;

                case 'wait':
                    await this.page.waitForTimeout(2000);
                    break;

                default:
                    this.log(`未知的AI决策操作: ${action}`);
            }

            return {
                executed_action: action,
                selector: selector,
                text: text,
                reasoning: decision.reasoning,
                message: 'AI决策执行成功'
            };

        } catch (error) {
            this.log(`执行AI决策失败: ${error.message}`);
            throw error;
        }
    }

    log(message) {
        console.log(`[BrowserControl] ${new Date().toISOString()} - ${message}`);
    }
}

// 主执行逻辑
async function main() {
    const browserControl = new BrowserControl();
    
    // 读取输入
    let input = '';
    process.stdin.setEncoding('utf8');
    
    for await (const chunk of process.stdin) {
        input += chunk;
    }
    
    try {
        const result = await browserControl.execute(input.trim());
        console.log(result);
    } catch (error) {
        console.log(JSON.stringify({
            status: 'error',
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = BrowserControl;
