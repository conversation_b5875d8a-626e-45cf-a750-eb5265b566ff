[{"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.220 Whale/1.3.51.7 Safari/537.36", "browser": {"name": "Whale", "version": "1.3.51.7", "major": "1"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "65.0.3325.220"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Whale/1.4.64.6 Safari/537.36", "browser": {"name": "Whale", "version": "1.4.64.6", "major": "1"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "70.0.3538.110"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.146 Whale/2.6.90.18 Safari/537.36", "browser": {"name": "Whale", "version": "2.6.90.18", "major": "2"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "76.0.3809.146"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.146 Whale/2.6.90.18 Safari/537.36", "browser": {"name": "Whale", "version": "2.6.90.18", "major": "2"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "76.0.3809.146"}, "os": {"name": "Windows", "version": "7"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Whale/2.8.105.22 Safari/537.36", "browser": {"name": "Whale", "version": "2.8.105.22", "major": "2"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Whale/2.8.108.15 Safari/537.36", "browser": {"name": "Whale", "version": "2.8.108.15", "major": "2"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Whale/2.8.105.22 Safari/537.36", "browser": {"name": "Whale", "version": "2.8.105.22", "major": "2"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.272 Whale/2.9.118.16 Safari/537.36", "browser": {"name": "Whale", "version": "2.9.118.16", "major": "2"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "86.0.4240.272"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.118 Whale/2.11.126.22 Safari/537.36", "browser": {"name": "Whale", "version": "2.11.126.22", "major": "2"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "94.0.4606.118"}, "os": {"name": "Windows", "version": "7"}}, {"ua": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.118 Whale/2.11.126.22 Safari/537.36", "browser": {"name": "Whale", "version": "2.11.126.22", "major": "2"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "94.0.4606.118"}, "os": {"name": "Windows", "version": "7"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Whale/2.11.126.19 Safari/537.36", "browser": {"name": "Whale", "version": "2.11.126.19", "major": "2"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "94.0.4606.81"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.70 Whale/3.13.131.27 Safari/537.36", "browser": {"name": "Whale", "version": "3.13.131.27", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "98.0.4758.70"}, "os": {"name": "Windows", "version": "8.1"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.70 Whale/3.13.131.27 Safari/537.36", "browser": {"name": "Whale", "version": "3.13.131.27", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "98.0.4758.70"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.57 Whale/3.14.133.23 Safari/537.36", "browser": {"name": "Whale", "version": "3.14.133.23", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "100.0.4896.57"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.151 Whale/3.14.134.62 Safari/537.36", "browser": {"name": "Whale", "version": "3.14.134.62", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "100.0.4896.151"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.114 Whale/3.17.145.12 Safari/537.36", "browser": {"name": "Whale", "version": "3.17.145.12", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "106.0.5249.114"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Whale/3.20.182.12 Safari/537.36", "browser": {"name": "Whale", "version": "3.20.182.12", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "112.0.0.0"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Whale/3.21.192.18 Safari/537.36", "browser": {"name": "Whale", "version": "3.21.192.18", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "114.0.0.0"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Whale/3.26.244.14 Safari/537.36", "browser": {"name": "Whale", "version": "3.26.244.14", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "124.0.0.0"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Whale/3.18.154.13 Safari/537.36", "browser": {"name": "Whale", "version": "3.18.154.13", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "108.0.0.0"}, "os": {"name": "Windows", "version": "8.1"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Whale/3.25.232.19 Safari/537.36", "browser": {"name": "Whale", "version": "3.25.232.19", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "122.0.0.0"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Whale/3.26.244.20 Safari/537.36", "browser": {"name": "Whale", "version": "3.26.244.20", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "124.0.0.0"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Whale/3.25.232.19 Safari/537.36", "browser": {"name": "Whale", "version": "3.25.232.19", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "122.0.0.0"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Whale/3.26.244.21 Safari/537.36", "browser": {"name": "Whale", "version": "3.26.244.21", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "124.0.0.0"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Whale/3.27.254.15 Safari/537.36", "browser": {"name": "Whale", "version": "3.27.254.15", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "126.0.0.0"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Whale/4.29.282.14 Safari/537.36", "browser": {"name": "Whale", "version": "4.29.282.14", "major": "4"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "130.0.0.0"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Whale/3.28.266.14 Safari/537.36", "browser": {"name": "Whale", "version": "3.28.266.14", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "128.0.0.0"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Whale/3.27.254.15 Safari/537.36", "browser": {"name": "Whale", "version": "3.27.254.15", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "126.0.0.0"}, "os": {"name": "Windows", "version": "10"}}, {"ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Whale/4.31.304.16 Safari/537.36", "browser": {"name": "Whale", "version": "4.31.304.16", "major": "4"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "134.0.0.0"}, "os": {"name": "Windows", "version": "10"}}]