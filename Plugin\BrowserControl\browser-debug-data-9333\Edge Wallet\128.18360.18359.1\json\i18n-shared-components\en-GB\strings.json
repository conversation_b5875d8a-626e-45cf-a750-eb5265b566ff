{"aboutPrivacyStatementLinkText": "Microsoft Privacy Statement", "aboutTermsOfUseLinkText": "Microsoft Edge Terms of Use", "affirmDescriptionText": "Buy now, pay over time with Affirm. $1", "affirmDisclaimerLinkText": "Contact Affirm", "affirmDisclaimerText": "The Affirm Card is a limited use virtual card and funded by either Cross River Bank, Member FDIC or Affirm Loan Services, LLC, depending on the terms of your loan agreement, and issued by Sutton Bank, Member FDIC, pursuant to licence from Visa U.S.A. Inc. The Affirm Card is issued by Marqeta. $1 if you have any questions.", "affirmEstimatedFeesText": "No late fees apply", "affirmLogoDesc": "Buy now, pay over time with Affirm", "affirmPolicy": "By continuing, you agree to $1 and acknowledge that your total purchase amount along with where you shop will be shared with Affirm, in accordance with $2. If you have recently been approved for a transaction by Affirm, the payment terms with that approval may apply.", "cancel": "Cancel", "challengeDescriptionAPPToAPP": "Sign in to your online banking app to receive a verification code.", "challengeDescriptionCustomerService": "Call your bank at $1 to receive a verification code.", "challengeDescriptionEmail": "Your bank is sending you a verification code to your email address $1.", "challengeDescriptionOnlineBanking": "Enter the code you received from your bank below.", "challengeDescriptionOutboundCall": "Your bank will call your mobile phone number $1 with a verification code.", "challengeDescriptionSMS": "Your bank is sending you a verification code to your mobile phone number $1.", "clearButtonLabel": "Clear", "close": "Close", "commonSubtitle": "See if you qualify to pay over time", "dismiss": "<PERSON><PERSON><PERSON>", "donationPaymentMethodItemTitle": "Payment method", "feedbackDislike": "Dislike", "feedbackLike": "Like", "feedbackOptionAutofillDontWork": "Autofill did not work", "feedbackOptionOthers": "Other", "feedbackOptionTookTooMuchTime": "Took too much time", "klarnaDescriptionText": "Shop now. Pay over time with Klarna. $1", "klarnaDisclaimerText": "The Klarna Visa® Commercial Card is issued by Sutton Bank, Member FDIC, pursuant to a licence from Visa U.S.A Inc. The Klarna Card is powered by Marqeta. Customer Service $1", "klarnaEstimatedFeesText": "Klarna service and late fees may apply", "klarnaFeeTermLinkText": "Klarna Pay Later in $1", "klarnaLogoDesc": "Save money with <PERSON><PERSON><PERSON>.", "klarnaPolicy": "By continuing, you agree to $1 and acknowledge that your total purchase amount along with where you shop will be shared with Klarna, in accordance with $2. If you have recently been approved for a transaction by Klarna, the payment terms with that approval may apply.", "learnMore": "Learn more", "manageYourPaymentMethodsLinkText": "Manage your payment methods", "msPayAddCardAgreement": "By continuing, you agree to the $1, $2, and $3 regarding how your data is handled.", "nextButtonAriaLabel": "Go to next page", "paginationButtonAriaLabel": "Go to page $1", "paymentServiceTermsLinkText": "Payment Service Terms", "previousButtonAriaLabel": "Go to previous page", "textOfChangeButton": "Change", "textOfCreateButton": "Create", "tokenizationEnroll": "Enrol", "tokenizationEnrollConfirmDialogTitle": "Pay safely with a virtual card", "tokenizationEnrollConfirmDialogTitleForRewards": "Set up a virtual card and earn 20 Microsoft Rewards points", "tokenizationEnrollConfirmFooterDescription": "Your card issuer may send a verification code or request the card's security code to verify it's you.", "tokenizationEnrollConfirmModalDescription": "A virtual card hides your card details from merchants when you shop online. If any of them experiences a data breach, your details remain protected. $1", "tokenizationTerms": "By continuing, you agree to the $1.", "tokenizeCardBack": "Back", "tokenizeCardChallengeMethodAPPToAPP": "Mobile banking app", "tokenizeCardChallengeMethodCustomerService": "Bank customer service", "tokenizeCardChallengeMethodEmail": "Email", "tokenizeCardChallengeMethodOnlineBanking": "Bank account", "tokenizeCardChallengeMethodOutboundCall": "Phone call", "tokenizeCardChallengeMethodSMS": "Text message", "tokenizeCardCvvInvalidError": "Please verify your security code and try again.", "tokenizeCardEnterCode": "Enter code", "tokenizeCardEnterCodeStepTitle": "Let's get you verified", "tokenizeCardEnterCvvStepDescription": "Your bank requires you to verify the CVV security code associated with your $1 ending in $2.", "tokenizeCardErrorStepTitle": "Something went wrong", "tokenizeCardFetchCodeError": "Your card issuer had an issue sending the code. Request another code or use a different verification method.", "tokenizeCardFetchCodeErrorCustomerService": "Your card issuer had an issue generating the code. Please try a different verification method or try again later.", "tokenizeCardGeneralError": "Your card issuer is having issues at the moment, please try again later.", "tokenizeCardLoadingTitle": "Contacting your bank…", "tokenizeCardNext": "Next", "tokenizeCardResendCode": "Resend code", "tokenizeCardSelectMethodDescription": "Choose one of the following verification methods:", "tokenizeCardSelectMethodStepTitle": "Let's get you verified", "tokenizeCardSendingCode": "Code sent!", "tokenizeCardTryAgain": "Try again", "tokenizeCardValidationError": "Your card issuer is unable to verify. Try again shortly.", "tokenizeCardVerify": "Verify", "tokenizeCardVerifyWithAnotherMethod": "Verify with another method", "tokenizeCardVerifying": "Verifying", "walletCommonAccountCount": "$1 accounts", "walletDrawerAutoFillErrorMessage": "We are unable to autofill your card information from $1. Please manually enter your $1 card information during checkout.", "walletDrawerAutoFillMessage": "We've automatically filled your card information from $1.", "walletDrawerAutoFillMessageWithBillingAddress": "Your payment details from $1 have been filled in. If not, click the card and address details below to copy.", "walletDrawerAutoFillMessageWithoutBillingAddress": "Your payment details from $1 have been filled in. If not, click the card details below to copy. To complete this purchase, please fill in your home billing address.", "walletDrawerCvcLabel": "CVC", "walletDrawerExpirationLabel": "Expiration", "walletDrawerLabelCopyCardNumber": "Copy card number", "walletDrawerLabelCopyCvc": "Copy CVC", "walletDrawerLabelCopyNameAddress": "Copy name and address", "walletDrawerLabelShowCardNumber": "Show card number", "walletDrawerLabelShowCvc": "Show CVC", "walletDrawerLabelShowExpirationDate": "Copy expiration date", "walletDrawerLinkErrorMessage": "We are unable to link your Microsoft account to $1. Please try again later.", "walletDrawerNameAndAddressLabel": "Your name & billing address", "walletDrawerPayWithVirtualCard": "Pay with your $1 $2 card", "walletDrawerSystemErrorHeader": "Uh oh", "walletDrawerVirtualCardNumberLabel": "Virtual card number", "walletFeedbackThankYouText": "Thank you for your feedback. It will help us improve Wallet.", "walletMicrofeedbackPrompt": "Satisfied with <PERSON><PERSON>?", "walletNotInterestedText": "Not interested", "walletPWAAddShortcut": "Add shortcut", "walletPWACommonPromoContent": "Install now for faster access to Wallet.", "walletPWAPromoContent": "Install now and earn $1 Microsoft Rewards points.", "walletPWAPromoTitle": "Add a shortcut to <PERSON><PERSON>", "walletSeeDetailsAriaLabel": "see details", "walletUXAddPaymentButtonTitle": "Add new card", "walletUXManagePaymentButtonTitle": "Manage payment methods", "walletUXMaskedUnavailableCardsDescriptionNew": "You’re unable to use $1 due to restricted network access or connectivity issues", "walletUXMaskedUnavailableCardsTheseCards": "these cards", "walletUXMaskedUnavailableCardsThisCard": "this card", "walletUXMaskedUnavailableCardsTitle": "Unavailable cards", "walletUXSInEligibleCardLogoContent": "This website doesn't support virtual card", "walletUXSTokenCardLogoContent": "Virtual card number •••• $1 activated to protect your payment information", "zipDescriptionText": "Shop now, pay over 6 weeks with Postcode. $1", "zipEstimatedFeesText": "Late fees may apply", "zipFeeTermLinkText": "Zip’s Terms of Service", "zipLogoDesc": "Zip, buy now, pay later", "zipPolicy": "By continuing, you agree to our $1 and acknowledge that your total purchase amount will be shared with Zip, in accordance with $2."}