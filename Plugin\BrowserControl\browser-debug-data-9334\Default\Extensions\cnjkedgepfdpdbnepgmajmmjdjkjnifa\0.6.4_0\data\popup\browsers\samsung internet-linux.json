[{"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.2 Chrome/71.0.3578.99 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.2", "major": "10"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/8.0 Chrome/63.0.3239.111 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "8.0", "major": "8"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.0 Chrome/71.0.3578.99 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "10.0", "major": "10"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Safari/537.36,gzip(gfe),gzip(gfe", "browser": {"name": "Samsung Internet", "version": "13.0", "major": "13"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/15.0 Chrome/90.0.4430.210 Safari/537.36", "browser": {"name": "Samsung Internet", "version": "15.0", "major": "15"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "90.0.4430.210"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Safari/537.36", "browser": {"name": "Samsung Internet", "version": "27.0", "major": "27"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "*********"}, "os": {"name": "Linux", "version": "x86_64"}}]