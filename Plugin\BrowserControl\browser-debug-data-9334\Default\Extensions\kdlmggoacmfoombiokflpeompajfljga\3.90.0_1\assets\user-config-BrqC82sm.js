(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},a=new Error().stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="eb259b6b-d2f7-45db-be77-9f00d6821709",e._sentryDebugIdIdentifier="sentry-dbid-eb259b6b-d2f7-45db-be77-9f00d6821709")}catch{}})();var La=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};La.SENTRY_RELEASE={id:"v3.90.0"};var qa=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ba(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ca={exports:{}};(function(e,a){(function(n,r){r(e)})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:qa,function(n){if(!(globalThis.chrome&&globalThis.chrome.runtime&&globalThis.chrome.runtime.id))throw new Error("This script should only be loaded in a browser extension.");if(globalThis.browser&&globalThis.browser.runtime&&globalThis.browser.runtime.id)n.exports=globalThis.browser;else{const r="The message port closed before a response was received.",t=i=>{const o={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(o).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class d extends WeakMap{constructor(c,f=void 0){super(f),this.createItem=c}get(c){return this.has(c)||this.set(c,this.createItem(c)),super.get(c)}}const g=l=>l&&typeof l=="object"&&typeof l.then=="function",s=(l,c)=>(...f)=>{i.runtime.lastError?l.reject(new Error(i.runtime.lastError.message)):c.singleCallbackArg||f.length<=1&&c.singleCallbackArg!==!1?l.resolve(f[0]):l.resolve(f)},u=l=>l==1?"argument":"arguments",A=(l,c)=>function(h,...T){if(T.length<c.minArgs)throw new Error(`Expected at least ${c.minArgs} ${u(c.minArgs)} for ${l}(), got ${T.length}`);if(T.length>c.maxArgs)throw new Error(`Expected at most ${c.maxArgs} ${u(c.maxArgs)} for ${l}(), got ${T.length}`);return new Promise((C,k)=>{if(c.fallbackToNoCallback)try{h[l](...T,s({resolve:C,reject:k},c))}catch{h[l](...T),c.fallbackToNoCallback=!1,c.noCallback=!0,C()}else c.noCallback?(h[l](...T),C()):h[l](...T,s({resolve:C,reject:k},c))})},m=(l,c,f)=>new Proxy(c,{apply(h,T,C){return f.call(T,l,...C)}});let b=Function.call.bind(Object.prototype.hasOwnProperty);const w=(l,c={},f={})=>{let h=Object.create(null),T={has(k,p){return p in l||p in h},get(k,p,I){if(p in h)return h[p];if(!(p in l))return;let x=l[p];if(typeof x=="function")if(typeof c[p]=="function")x=m(l,l[p],c[p]);else if(b(f,p)){let H=A(p,f[p]);x=m(l,l[p],H)}else x=x.bind(l);else if(typeof x=="object"&&x!==null&&(b(c,p)||b(f,p)))x=w(x,c[p],f[p]);else if(b(f,"*"))x=w(x,c[p],f["*"]);else return Object.defineProperty(h,p,{configurable:!0,enumerable:!0,get(){return l[p]},set(H){l[p]=H}}),x;return h[p]=x,x},set(k,p,I,x){return p in h?h[p]=I:l[p]=I,!0},defineProperty(k,p,I){return Reflect.defineProperty(h,p,I)},deleteProperty(k,p){return Reflect.deleteProperty(h,p)}},C=Object.create(l);return new Proxy(C,T)},y=l=>({addListener(c,f,...h){c.addListener(l.get(f),...h)},hasListener(c,f){return c.hasListener(l.get(f))},removeListener(c,f){c.removeListener(l.get(f))}}),S=new d(l=>typeof l!="function"?l:function(f){const h=w(f,{},{getContent:{minArgs:0,maxArgs:0}});l(h)}),P=new d(l=>typeof l!="function"?l:function(f,h,T){let C=!1,k,p=new Promise(Y=>{k=function(M){C=!0,Y(M)}}),I;try{I=l(f,h,k)}catch(Y){I=Promise.reject(Y)}const x=I!==!0&&g(I);if(I!==!0&&!x&&!C)return!1;const H=Y=>{Y.then(M=>{T(M)},M=>{let pe;M&&(M instanceof Error||typeof M.message=="string")?pe=M.message:pe="An unexpected error occurred",T({__mozWebExtensionPolyfillReject__:!0,message:pe})}).catch(M=>{})};return H(x?I:p),!0}),O=({reject:l,resolve:c},f)=>{i.runtime.lastError?i.runtime.lastError.message===r?c():l(new Error(i.runtime.lastError.message)):f&&f.__mozWebExtensionPolyfillReject__?l(new Error(f.message)):c(f)},_=(l,c,f,...h)=>{if(h.length<c.minArgs)throw new Error(`Expected at least ${c.minArgs} ${u(c.minArgs)} for ${l}(), got ${h.length}`);if(h.length>c.maxArgs)throw new Error(`Expected at most ${c.maxArgs} ${u(c.maxArgs)} for ${l}(), got ${h.length}`);return new Promise((T,C)=>{const k=O.bind(null,{resolve:T,reject:C});h.push(k),f.sendMessage(...h)})},X={devtools:{network:{onRequestFinished:y(S)}},runtime:{onMessage:y(P),onMessageExternal:y(P),sendMessage:_.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:_.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},B={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return o.privacy={network:{"*":B},services:{"*":B},websites:{"*":B}},w(i,X,o)};n.exports=t(chrome)}})})(ca);var Ua=ca.exports;const N=Ba(Ua);var ga=typeof global=="object"&&global&&global.Object===Object&&global,Fa=typeof self=="object"&&self&&self.Object===Object&&self,R=ga||Fa||Function("return this")(),W=R.Symbol,da=Object.prototype,za=da.hasOwnProperty,Na=da.toString,$=W?W.toStringTag:void 0;function Wa(e){var a=za.call(e,$),n=e[$];try{e[$]=void 0;var r=!0}catch{}var t=Na.call(e);return r&&(a?e[$]=n:delete e[$]),t}var Ja=Object.prototype,Ka=Ja.toString;function Za(e){return Ka.call(e)}var Ga="[object Null]",Xa="[object Undefined]",Fe=W?W.toStringTag:void 0;function Q(e){return e==null?e===void 0?Xa:Ga:Fe&&Fe in Object(e)?Wa(e):Za(e)}function V(e){return e!=null&&typeof e=="object"}var Ha="[object Symbol]";function ke(e){return typeof e=="symbol"||V(e)&&Q(e)==Ha}function ja(e,a){for(var n=-1,r=e==null?0:e.length,t=Array(r);++n<r;)t[n]=a(e[n],n,e);return t}var D=Array.isArray,Va=1/0,ze=W?W.prototype:void 0,Ne=ze?ze.toString:void 0;function ua(e){if(typeof e=="string")return e;if(D(e))return ja(e,ua)+"";if(ke(e))return Ne?Ne.call(e):"";var a=e+"";return a=="0"&&1/e==-Va?"-0":a}function ie(e){var a=typeof e;return e!=null&&(a=="object"||a=="function")}function Ie(e){return e}var Qa="[object AsyncFunction]",_a="[object Function]",Ya="[object GeneratorFunction]",$a="[object Proxy]";function ma(e){if(!ie(e))return!1;var a=Q(e);return a==_a||a==Ya||a==Qa||a==$a}var fe=R["__core-js_shared__"],We=function(){var e=/[^.]+$/.exec(fe&&fe.keys&&fe.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function en(e){return!!We&&We in e}var an=Function.prototype,nn=an.toString;function Z(e){if(e!=null){try{return nn.call(e)}catch{}try{return e+""}catch{}}return""}var rn=/[\\^$.*+?()[\]{}|]/g,tn=/^\[object .+?Constructor\]$/,on=Function.prototype,sn=Object.prototype,ln=on.toString,cn=sn.hasOwnProperty,gn=RegExp("^"+ln.call(cn).replace(rn,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function dn(e){if(!ie(e)||en(e))return!1;var a=ma(e)?gn:tn;return a.test(Z(e))}function un(e,a){return e==null?void 0:e[a]}function G(e,a){var n=un(e,a);return dn(n)?n:void 0}var Te=G(R,"WeakMap");function mn(e,a,n){switch(n.length){case 0:return e.call(a);case 1:return e.call(a,n[0]);case 2:return e.call(a,n[0],n[1]);case 3:return e.call(a,n[0],n[1],n[2])}return e.apply(a,n)}var pn=800,fn=16,An=Date.now;function hn(e){var a=0,n=0;return function(){var r=An(),t=fn-(r-n);if(n=r,t>0){if(++a>=pn)return arguments[0]}else a=0;return e.apply(void 0,arguments)}}function vn(e){return function(){return e}}var le=function(){try{var e=G(Object,"defineProperty");return e({},"",{}),e}catch{}}(),bn=le?function(e,a){return le(e,"toString",{configurable:!0,enumerable:!1,value:vn(a),writable:!0})}:Ie,wn=hn(bn),yn=9007199254740991,xn=/^(?:0|[1-9]\d*)$/;function Me(e,a){var n=typeof e;return a=a??yn,!!a&&(n=="number"||n!="symbol"&&xn.test(e))&&e>-1&&e%1==0&&e<a}function Tn(e,a,n){a=="__proto__"&&le?le(e,a,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[a]=n}function ge(e,a){return e===a||e!==e&&a!==a}var Je=Math.max;function Sn(e,a,n){return a=Je(a===void 0?e.length-1:a,0),function(){for(var r=arguments,t=-1,i=Je(r.length-a,0),o=Array(i);++t<i;)o[t]=r[a+t];t=-1;for(var d=Array(a+1);++t<a;)d[t]=r[t];return d[a]=n(o),mn(e,this,d)}}function Pn(e,a){return wn(Sn(e,a,Ie),e+"")}var Cn=9007199254740991;function Ee(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Cn}function De(e){return e!=null&&Ee(e.length)&&!ma(e)}function On(e,a,n){if(!ie(n))return!1;var r=typeof a;return(r=="number"?De(n)&&Me(a,n.length):r=="string"&&a in n)?ge(n[a],e):!1}var kn=Object.prototype;function pa(e){var a=e&&e.constructor,n=typeof a=="function"&&a.prototype||kn;return e===n}function In(e,a){for(var n=-1,r=Array(e);++n<e;)r[n]=a(n);return r}var Mn="[object Arguments]";function Ke(e){return V(e)&&Q(e)==Mn}var fa=Object.prototype,En=fa.hasOwnProperty,Dn=fa.propertyIsEnumerable,Aa=Ke(function(){return arguments}())?Ke:function(e){return V(e)&&En.call(e,"callee")&&!Dn.call(e,"callee")};function Rn(){return!1}var ha=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Ze=ha&&typeof module=="object"&&module&&!module.nodeType&&module,Ln=Ze&&Ze.exports===ha,Ge=Ln?R.Buffer:void 0,qn=Ge?Ge.isBuffer:void 0,Se=qn||Rn,Bn="[object Arguments]",Un="[object Array]",Fn="[object Boolean]",zn="[object Date]",Nn="[object Error]",Wn="[object Function]",Jn="[object Map]",Kn="[object Number]",Zn="[object Object]",Gn="[object RegExp]",Xn="[object Set]",Hn="[object String]",jn="[object WeakMap]",Vn="[object ArrayBuffer]",Qn="[object DataView]",_n="[object Float32Array]",Yn="[object Float64Array]",$n="[object Int8Array]",er="[object Int16Array]",ar="[object Int32Array]",nr="[object Uint8Array]",rr="[object Uint8ClampedArray]",tr="[object Uint16Array]",ir="[object Uint32Array]",v={};v[_n]=v[Yn]=v[$n]=v[er]=v[ar]=v[nr]=v[rr]=v[tr]=v[ir]=!0;v[Bn]=v[Un]=v[Vn]=v[Fn]=v[Qn]=v[zn]=v[Nn]=v[Wn]=v[Jn]=v[Kn]=v[Zn]=v[Gn]=v[Xn]=v[Hn]=v[jn]=!1;function or(e){return V(e)&&Ee(e.length)&&!!v[Q(e)]}function sr(e){return function(a){return e(a)}}var va=typeof exports=="object"&&exports&&!exports.nodeType&&exports,ne=va&&typeof module=="object"&&module&&!module.nodeType&&module,lr=ne&&ne.exports===va,Ae=lr&&ga.process,Xe=function(){try{var e=ne&&ne.require&&ne.require("util").types;return e||Ae&&Ae.binding&&Ae.binding("util")}catch{}}(),He=Xe&&Xe.isTypedArray,ba=He?sr(He):or,cr=Object.prototype,gr=cr.hasOwnProperty;function wa(e,a){var n=D(e),r=!n&&Aa(e),t=!n&&!r&&Se(e),i=!n&&!r&&!t&&ba(e),o=n||r||t||i,d=o?In(e.length,String):[],g=d.length;for(var s in e)(a||gr.call(e,s))&&!(o&&(s=="length"||t&&(s=="offset"||s=="parent")||i&&(s=="buffer"||s=="byteLength"||s=="byteOffset")||Me(s,g)))&&d.push(s);return d}function dr(e,a){return function(n){return e(a(n))}}var ur=dr(Object.keys,Object),mr=Object.prototype,pr=mr.hasOwnProperty;function fr(e){if(!pa(e))return ur(e);var a=[];for(var n in Object(e))pr.call(e,n)&&n!="constructor"&&a.push(n);return a}function Re(e){return De(e)?wa(e):fr(e)}function Ar(e){var a=[];if(e!=null)for(var n in Object(e))a.push(n);return a}var hr=Object.prototype,vr=hr.hasOwnProperty;function br(e){if(!ie(e))return Ar(e);var a=pa(e),n=[];for(var r in e)r=="constructor"&&(a||!vr.call(e,r))||n.push(r);return n}function wr(e){return De(e)?wa(e,!0):br(e)}var yr=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,xr=/^\w*$/;function Le(e,a){if(D(e))return!1;var n=typeof e;return n=="number"||n=="symbol"||n=="boolean"||e==null||ke(e)?!0:xr.test(e)||!yr.test(e)||a!=null&&e in Object(a)}var re=G(Object,"create");function Tr(){this.__data__=re?re(null):{},this.size=0}function Sr(e){var a=this.has(e)&&delete this.__data__[e];return this.size-=a?1:0,a}var Pr="__lodash_hash_undefined__",Cr=Object.prototype,Or=Cr.hasOwnProperty;function kr(e){var a=this.__data__;if(re){var n=a[e];return n===Pr?void 0:n}return Or.call(a,e)?a[e]:void 0}var Ir=Object.prototype,Mr=Ir.hasOwnProperty;function Er(e){var a=this.__data__;return re?a[e]!==void 0:Mr.call(a,e)}var Dr="__lodash_hash_undefined__";function Rr(e,a){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=re&&a===void 0?Dr:a,this}function K(e){var a=-1,n=e==null?0:e.length;for(this.clear();++a<n;){var r=e[a];this.set(r[0],r[1])}}K.prototype.clear=Tr;K.prototype.delete=Sr;K.prototype.get=kr;K.prototype.has=Er;K.prototype.set=Rr;function Lr(){this.__data__=[],this.size=0}function de(e,a){for(var n=e.length;n--;)if(ge(e[n][0],a))return n;return-1}var qr=Array.prototype,Br=qr.splice;function Ur(e){var a=this.__data__,n=de(a,e);if(n<0)return!1;var r=a.length-1;return n==r?a.pop():Br.call(a,n,1),--this.size,!0}function Fr(e){var a=this.__data__,n=de(a,e);return n<0?void 0:a[n][1]}function zr(e){return de(this.__data__,e)>-1}function Nr(e,a){var n=this.__data__,r=de(n,e);return r<0?(++this.size,n.push([e,a])):n[r][1]=a,this}function L(e){var a=-1,n=e==null?0:e.length;for(this.clear();++a<n;){var r=e[a];this.set(r[0],r[1])}}L.prototype.clear=Lr;L.prototype.delete=Ur;L.prototype.get=Fr;L.prototype.has=zr;L.prototype.set=Nr;var te=G(R,"Map");function Wr(){this.size=0,this.__data__={hash:new K,map:new(te||L),string:new K}}function Jr(e){var a=typeof e;return a=="string"||a=="number"||a=="symbol"||a=="boolean"?e!=="__proto__":e===null}function ue(e,a){var n=e.__data__;return Jr(a)?n[typeof a=="string"?"string":"hash"]:n.map}function Kr(e){var a=ue(this,e).delete(e);return this.size-=a?1:0,a}function Zr(e){return ue(this,e).get(e)}function Gr(e){return ue(this,e).has(e)}function Xr(e,a){var n=ue(this,e),r=n.size;return n.set(e,a),this.size+=n.size==r?0:1,this}function q(e){var a=-1,n=e==null?0:e.length;for(this.clear();++a<n;){var r=e[a];this.set(r[0],r[1])}}q.prototype.clear=Wr;q.prototype.delete=Kr;q.prototype.get=Zr;q.prototype.has=Gr;q.prototype.set=Xr;var Hr="Expected a function";function qe(e,a){if(typeof e!="function"||a!=null&&typeof a!="function")throw new TypeError(Hr);var n=function(){var r=arguments,t=a?a.apply(this,r):r[0],i=n.cache;if(i.has(t))return i.get(t);var o=e.apply(this,r);return n.cache=i.set(t,o)||i,o};return n.cache=new(qe.Cache||q),n}qe.Cache=q;var jr=500;function Vr(e){var a=qe(e,function(r){return n.size===jr&&n.clear(),r}),n=a.cache;return a}var Qr=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,_r=/\\(\\)?/g,Yr=Vr(function(e){var a=[];return e.charCodeAt(0)===46&&a.push(""),e.replace(Qr,function(n,r,t,i){a.push(t?i.replace(_r,"$1"):r||n)}),a});function $r(e){return e==null?"":ua(e)}function ya(e,a){return D(e)?e:Le(e,a)?[e]:Yr($r(e))}var et=1/0;function me(e){if(typeof e=="string"||ke(e))return e;var a=e+"";return a=="0"&&1/e==-et?"-0":a}function xa(e,a){a=ya(a,e);for(var n=0,r=a.length;e!=null&&n<r;)e=e[me(a[n++])];return n&&n==r?e:void 0}function at(e,a,n){var r=e==null?void 0:xa(e,a);return r===void 0?n:r}function nt(e,a){for(var n=-1,r=a.length,t=e.length;++n<r;)e[t+n]=a[n];return e}function rt(){this.__data__=new L,this.size=0}function tt(e){var a=this.__data__,n=a.delete(e);return this.size=a.size,n}function it(e){return this.__data__.get(e)}function ot(e){return this.__data__.has(e)}var st=200;function lt(e,a){var n=this.__data__;if(n instanceof L){var r=n.__data__;if(!te||r.length<st-1)return r.push([e,a]),this.size=++n.size,this;n=this.__data__=new q(r)}return n.set(e,a),this.size=n.size,this}function E(e){var a=this.__data__=new L(e);this.size=a.size}E.prototype.clear=rt;E.prototype.delete=tt;E.prototype.get=it;E.prototype.has=ot;E.prototype.set=lt;function ct(e,a){for(var n=-1,r=e==null?0:e.length,t=0,i=[];++n<r;){var o=e[n];a(o,n,e)&&(i[t++]=o)}return i}function gt(){return[]}var dt=Object.prototype,ut=dt.propertyIsEnumerable,je=Object.getOwnPropertySymbols,mt=je?function(e){return e==null?[]:(e=Object(e),ct(je(e),function(a){return ut.call(e,a)}))}:gt;function pt(e,a,n){var r=a(e);return D(e)?r:nt(r,n(e))}function Ve(e){return pt(e,Re,mt)}var Pe=G(R,"DataView"),Ce=G(R,"Promise"),Oe=G(R,"Set"),Qe="[object Map]",ft="[object Object]",_e="[object Promise]",Ye="[object Set]",$e="[object WeakMap]",ea="[object DataView]",At=Z(Pe),ht=Z(te),vt=Z(Ce),bt=Z(Oe),wt=Z(Te),z=Q;(Pe&&z(new Pe(new ArrayBuffer(1)))!=ea||te&&z(new te)!=Qe||Ce&&z(Ce.resolve())!=_e||Oe&&z(new Oe)!=Ye||Te&&z(new Te)!=$e)&&(z=function(e){var a=Q(e),n=a==ft?e.constructor:void 0,r=n?Z(n):"";if(r)switch(r){case At:return ea;case ht:return Qe;case vt:return _e;case bt:return Ye;case wt:return $e}return a});var aa=R.Uint8Array,yt="__lodash_hash_undefined__";function xt(e){return this.__data__.set(e,yt),this}function Tt(e){return this.__data__.has(e)}function ce(e){var a=-1,n=e==null?0:e.length;for(this.__data__=new q;++a<n;)this.add(e[a])}ce.prototype.add=ce.prototype.push=xt;ce.prototype.has=Tt;function St(e,a){for(var n=-1,r=e==null?0:e.length;++n<r;)if(a(e[n],n,e))return!0;return!1}function Pt(e,a){return e.has(a)}var Ct=1,Ot=2;function Ta(e,a,n,r,t,i){var o=n&Ct,d=e.length,g=a.length;if(d!=g&&!(o&&g>d))return!1;var s=i.get(e),u=i.get(a);if(s&&u)return s==a&&u==e;var A=-1,m=!0,b=n&Ot?new ce:void 0;for(i.set(e,a),i.set(a,e);++A<d;){var w=e[A],y=a[A];if(r)var S=o?r(y,w,A,a,e,i):r(w,y,A,e,a,i);if(S!==void 0){if(S)continue;m=!1;break}if(b){if(!St(a,function(P,O){if(!Pt(b,O)&&(w===P||t(w,P,n,r,i)))return b.push(O)})){m=!1;break}}else if(!(w===y||t(w,y,n,r,i))){m=!1;break}}return i.delete(e),i.delete(a),m}function kt(e){var a=-1,n=Array(e.size);return e.forEach(function(r,t){n[++a]=[t,r]}),n}function It(e){var a=-1,n=Array(e.size);return e.forEach(function(r){n[++a]=r}),n}var Mt=1,Et=2,Dt="[object Boolean]",Rt="[object Date]",Lt="[object Error]",qt="[object Map]",Bt="[object Number]",Ut="[object RegExp]",Ft="[object Set]",zt="[object String]",Nt="[object Symbol]",Wt="[object ArrayBuffer]",Jt="[object DataView]",na=W?W.prototype:void 0,he=na?na.valueOf:void 0;function Kt(e,a,n,r,t,i,o){switch(n){case Jt:if(e.byteLength!=a.byteLength||e.byteOffset!=a.byteOffset)return!1;e=e.buffer,a=a.buffer;case Wt:return!(e.byteLength!=a.byteLength||!i(new aa(e),new aa(a)));case Dt:case Rt:case Bt:return ge(+e,+a);case Lt:return e.name==a.name&&e.message==a.message;case Ut:case zt:return e==a+"";case qt:var d=kt;case Ft:var g=r&Mt;if(d||(d=It),e.size!=a.size&&!g)return!1;var s=o.get(e);if(s)return s==a;r|=Et,o.set(e,a);var u=Ta(d(e),d(a),r,t,i,o);return o.delete(e),u;case Nt:if(he)return he.call(e)==he.call(a)}return!1}var Zt=1,Gt=Object.prototype,Xt=Gt.hasOwnProperty;function Ht(e,a,n,r,t,i){var o=n&Zt,d=Ve(e),g=d.length,s=Ve(a),u=s.length;if(g!=u&&!o)return!1;for(var A=g;A--;){var m=d[A];if(!(o?m in a:Xt.call(a,m)))return!1}var b=i.get(e),w=i.get(a);if(b&&w)return b==a&&w==e;var y=!0;i.set(e,a),i.set(a,e);for(var S=o;++A<g;){m=d[A];var P=e[m],O=a[m];if(r)var _=o?r(O,P,m,a,e,i):r(P,O,m,e,a,i);if(!(_===void 0?P===O||t(P,O,n,r,i):_)){y=!1;break}S||(S=m=="constructor")}if(y&&!S){var X=e.constructor,B=a.constructor;X!=B&&"constructor"in e&&"constructor"in a&&!(typeof X=="function"&&X instanceof X&&typeof B=="function"&&B instanceof B)&&(y=!1)}return i.delete(e),i.delete(a),y}var jt=1,ra="[object Arguments]",ta="[object Array]",oe="[object Object]",Vt=Object.prototype,ia=Vt.hasOwnProperty;function Qt(e,a,n,r,t,i){var o=D(e),d=D(a),g=o?ta:z(e),s=d?ta:z(a);g=g==ra?oe:g,s=s==ra?oe:s;var u=g==oe,A=s==oe,m=g==s;if(m&&Se(e)){if(!Se(a))return!1;o=!0,u=!1}if(m&&!u)return i||(i=new E),o||ba(e)?Ta(e,a,n,r,t,i):Kt(e,a,g,n,r,t,i);if(!(n&jt)){var b=u&&ia.call(e,"__wrapped__"),w=A&&ia.call(a,"__wrapped__");if(b||w){var y=b?e.value():e,S=w?a.value():a;return i||(i=new E),t(y,S,n,r,i)}}return m?(i||(i=new E),Ht(e,a,n,r,t,i)):!1}function Be(e,a,n,r,t){return e===a?!0:e==null||a==null||!V(e)&&!V(a)?e!==e&&a!==a:Qt(e,a,n,r,Be,t)}var _t=1,Yt=2;function $t(e,a,n,r){var t=n.length,i=t;if(e==null)return!i;for(e=Object(e);t--;){var o=n[t];if(o[2]?o[1]!==e[o[0]]:!(o[0]in e))return!1}for(;++t<i;){o=n[t];var d=o[0],g=e[d],s=o[1];if(o[2]){if(g===void 0&&!(d in e))return!1}else{var u=new E,A;if(!(A===void 0?Be(s,g,_t|Yt,r,u):A))return!1}}return!0}function Sa(e){return e===e&&!ie(e)}function ei(e){for(var a=Re(e),n=a.length;n--;){var r=a[n],t=e[r];a[n]=[r,t,Sa(t)]}return a}function Pa(e,a){return function(n){return n==null?!1:n[e]===a&&(a!==void 0||e in Object(n))}}function ai(e){var a=ei(e);return a.length==1&&a[0][2]?Pa(a[0][0],a[0][1]):function(n){return n===e||$t(n,e,a)}}function ni(e,a){return e!=null&&a in Object(e)}function ri(e,a,n){a=ya(a,e);for(var r=-1,t=a.length,i=!1;++r<t;){var o=me(a[r]);if(!(i=e!=null&&n(e,o)))break;e=e[o]}return i||++r!=t?i:(t=e==null?0:e.length,!!t&&Ee(t)&&Me(o,t)&&(D(e)||Aa(e)))}function ti(e,a){return e!=null&&ri(e,a,ni)}var ii=1,oi=2;function si(e,a){return Le(e)&&Sa(a)?Pa(me(e),a):function(n){var r=at(n,e);return r===void 0&&r===a?ti(n,e):Be(a,r,ii|oi)}}function li(e){return function(a){return a==null?void 0:a[e]}}function ci(e){return function(a){return xa(a,e)}}function gi(e){return Le(e)?li(me(e)):ci(e)}function di(e){return typeof e=="function"?e:e==null?Ie:typeof e=="object"?D(e)?si(e[0],e[1]):ai(e):gi(e)}function ui(e){return function(a,n,r){for(var t=-1,i=Object(a),o=r(a),d=o.length;d--;){var g=o[++t];if(n(i[g],g,i)===!1)break}return a}}var mi=ui();function pi(e,a){return e&&mi(e,a,Re)}var Ca=Object.prototype,fi=Ca.hasOwnProperty,Ai=Pn(function(e,a){e=Object(e);var n=-1,r=a.length,t=r>2?a[2]:void 0;for(t&&On(a[0],a[1],t)&&(r=1);++n<r;)for(var i=a[n],o=wr(i),d=-1,g=o.length;++d<g;){var s=o[d],u=e[s];(u===void 0||ge(u,Ca[s])&&!fi.call(e,s))&&(e[s]=i[s])}return e});function hi(e,a){var n={};return a=di(a),pi(e,function(r,t,i){Tn(n,t,a(r,t,i))}),n}const U="/assets/anthropic-KpkKHV99.png",vi="/assets/chatgpt-CyTlPkJQ.png",bi="/assets/cohere-lYYjmGpZ.png",wi="/assets/copilot-BhQb9_2K.png",se="/assets/deepseek-Dl2r2h9V.png",yi="data:image/png;base64,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",ee="/assets/gemini-BHNSVL96.png",xi="/assets/gemma-CfUxr8D5.png",Ti="data:image/png;base64,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",Si="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMkAAADJCAMAAAC+GOY3AAAAQlBMVEX1TzX1TzX1TzX1TzX1TzX2WkL4e2j6p5r7sqf9083+9PL////+6eb5kYH4hnT7vbP3cFv8yMD93tn2ZU75nI31TzW9T89eAAAABXRSTlOPv2CAQLFEbcUAAAUSSURBVHic7ZzrmqMgDIbnsIra2mpb7/9Wt4c5dElQMGH57JPv90zlFXKQQN7e3l9DH2/v02vo00jgZCR4MhI8GQmejARPRoInI8GTkeDJSPBkJHgyEjwZCZ6MBE9GgicjwZOR4MlI8GQkeDKSKFW1a9q22920b1vX1/melY+kOhz3O6rWDXmel4vkMDIUX9o3WWBykFSuC3PcderVH5qBpDouYDwmRp1Fm2R5Pn5YDqoP1iapOSsPqT1rPlqVpGoSOK7qNJeYJsmQMiEPHSu1pyuS9LEW8qyTmkfWI3ErOK7qtFDUSKJ8L4ui5MO0SFaDXKVj90okEhClBaZDcpGAKKGokBxkINd4r+CMNUjOYffbja5+vPD64NowSisehArJKYRB0vdwsu/Eo1AgCQQSPts9hzJMsanISQZ+Pi6hvw9k/SfhMBRI2LU1zpkwnzBL15eYpOdGFZyQh9hp6YT+S0zCvN+I8MDZ1lE2ECkJMyVRcY6bStmHl5SETklkwGZQGtFIhCTMeGJ356ityCxFSELDdrwLok5PlBTLSM5kMAlpB01yRDFFRkJz4BSrlf23LxkJWSBp4Y24i4U4NCsRCVlciTZbay4vEQnxXKkZB5kUgfcSkRBHmrrOyasQFFhEJP4rHVN/oJJO6pNEJP440uOB/+Ul+HaUkBCDTXei/vLarx6MiMTfiFgxDPKZtnowIhI/NU82k4ku0PWxUZNkjbn6idt65yUh8Z3wmgiNQaIxCn9ejcRIHnodEj9Ar7F4DBINL4xJsiYyYpBoJE0YJAoZJAiJQlaPQiL+0oIhEX/9wpCQDat0PwxCIt0lmmBI6C5P8qSgkJDzXF2qpaCQ0Gpp6uYICglTOUhMI2FImEpQWmEdhmSi5wTSjtPgkDBF3CQUHJKKObyRgoJDwh7tSjgQCETCHS3Y7ZrYYI9EQr5S7oo9a45EQgP9Q23UoKBIqtBJtf1lOXeBIpmG8OnBU7NwowmLhD8ZFdbzaMFIQqayQZK0k8/QJEko2CQpKOAkCSjoJPEeDJ5kqiMvBeGTTNXMzdJtkUTe1doESdRFwG2QTNN50YltheTGMr/GtkNyXWN9KNPfGslV534MzczzaP2lCEhy09C77+YLodFinFvRkL8Kt0viz9f6XypMQjZm1v9UYRLfdRU60akg30wE94LKkpBCpeBgfVkSkqQJbjUWJSEb/Z3gx4qSkOKL5PpcSRJae5FcbypJQtN/yZWzgiT0Vr3obmY5EuZWvai9VzESplYhuOgwlSPhii6yHh8ZSIZ22XA5ENmUZCAZuuUyNlsGE7ZdUSe5F7gWmkCxm5XSpgXaJN+VupmmBYEtMWlfQmWS35Jj5wIsPd8bS3alfNIm+ad2yrFUAQ6F3jGqJKQIPPbPdeyqD2/pyXteqvZU48a5b52r69q5cW4zT3J1+UuKJDNl+SUJm2HcpUciADlpdLtTIykNokcy0wjqv4AUqf1mAdG0+HUosx2MUqTphRMP4twlDu0/0o2MqU06tToo3qSbrUSWsb+l2gJWOxdOaGar2v81x5dWZIPhYK68Vhm+fmOaJXeNam/hm7LsSMyXfm9HPZXn46ZceytDEzKYTK3Rc+4SnXvSsX4/XnK1q8++31UfnHNt2zbOHeoMa+pXpWu/ejISPBkJnowET0aCJyPBk5HgyUjwZCR4MhI8GQmejARPRoInI8GTkeDJSPBkJHgyEjwZCZ5eieTj8zX05y/Z/fRFIEbR7gAAAABJRU5ErkJggg==",ve="/assets/llama-CkjhBMGW.png",Pi="/assets/minimax-BWmTPobK.png",ae="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAMAAABHPGVmAAAA6lBMVEVHcEz/LQ3/owD/DRf/awH/oQD/bwD/lwD/BhAAAAD/Sgr/zQD/Sgv/cAD/Sgr/0AiIVwD/TxH/oQAAAADWrADwaQDWBQ3/oQD/cADWPggAAAD/cAD/t4AAAAAAAAA9GgAAAAAAAAD/bQH/pAD/CBD/Dxf/SwkAAACATACABAj/lgD/lwD8bgD4agH/MxT/nRkAAAClXQD/vQD/agL/LA2IWAD/uAPXBA3FfQCAJgWANQHWJgqAFgbkqAD8awH/lgD/ogAAAAD/Sgr/bwD/zQD/Bg//vQCAJQWAZwCAUQCAOACAAwhbOgCAXwCW+RcEAAAAQHRSTlMA9FuH+oD9/YAqfICpgICH/Yf9gPL68vLy8vTrPFv9/fqp8S1ARFIV/YD7f+76gpwr+4B9elqxeXap+uz08+/x1V4+zgAAAP9JREFUaIHt18dSQkEQRmEUAxhAQVQMgImMimIOSDDL+78Oix4Wv3BxXFKcbze3p+vULG8oBACTq2U+uiLusxp1uxu6u0yECBEiRP4b6b2J61lx5C7vmyv7evtjnnT3IjDy+SISepzXy2s63Rp9mQgRIkSI/B3JmOKiKOvxbM9kTV2nTT1WAl/y1RELnXF+Tbf1uEqECBEiRLwjOXM/Jw53TcrURk6rbvqs08vAl7y3xfrY6ZINB/8naZ3GiBAhQoSId2THPM6IUzc9MHcrIm/Dc7f7oLuNwJd8v4rw8MVhg5ds6m6ECBEiRIh4R5KmFBEnPpGC273R3WOfXQCYcn2EynHIr+Pi5QAAAABJRU5ErkJggg==",Ci="/assets/moonshot-CzN61IMt.png",Oi="/assets/nova-D9iT0THU.png",F="/assets/openai-Wym7AITM.png",ki="/assets/pi-2SxyuHC7.png",be="/assets/pplx-DjxnmSOd.png",j="data:image/png;base64,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",Ii="/assets/wizardlm-Bwib7mtC.png",Mi="/assets/yi-S5JqrID1.png";globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,a){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,a),a)}};const Ei=["cloud-gpt-4.1","cloud-gpt-4.1-mini","cloud-gpt-4o","cloud-gpt-4o-image","cloud-o4-mini","cloud-o3","cloud-claude-sonnet-4","cloud-claude-sonnet-4-thinking","cloud-claude-opus-4","cloud-claude-3.7-sonnet","cloud-claude-3.7-sonnet-thinking","cloud-claude-3.5-haiku","cloud-gemini-2.5-pro","cloud-gemini-2.5-flash","cloud-gemini-2.0-flash","cloud-deepseek-r1","cloud-deepseek-v3","cloud-grok-3","cloud-llama4","cloud-llama3","cloud-mistral-large","cloud-mistral-medium","cloud-magistral-medium","cloud-pplx-sonar-online","cloud-qwen3","cloud-qwen2.5-max","cloud-qwen2.5-72b","cloud-qwq-32b","cloud-qvq-max","cloud-command-a","cloud-doubao-1.5-pro","cloud-yi-large","cloud-wizardlm-2","cloud-amazon-nova-pro"],Oa={"cloud-gpt-4.1":{model:"openai/gpt-4.1",tier:"advanced",capabilities:["vision"]},"cloud-gpt-4.1-mini":{model:"openai/gpt-4.1-mini",tier:"basic",capabilities:["vision"]},"cloud-gpt-4o":{model:"openai/gpt-4o",tier:"advanced",capabilities:["vision"]},"cloud-gpt-4o-image":{model:"openai/gpt-4o-image",tier:"advanced",capabilities:["vision"]},"cloud-o4-mini":{model:"openai/o4-mini",tier:"advanced",capabilities:["vision"]},"cloud-o3":{model:"openai/o3",tier:"advanced",capabilities:["vision"]},"cloud-gemini-2.5-pro":{model:"google/gemini-2.5-pro",tier:"advanced",capabilities:["vision"]},"cloud-gemini-2.5-flash":{model:"google/gemini-2.5-flash",tier:"basic",capabilities:["vision"]},"cloud-gemini-2.0-flash":{model:"google/gemini-2.0-flash",tier:"basic",capabilities:["vision"]},"cloud-llama4":{model:"meta/llama4",tier:"basic",capabilities:[]},"cloud-llama3":{model:"meta/llama3",tier:"basic",capabilities:[]},"cloud-claude-sonnet-4":{model:"anthropic/claude-sonnet-4",tier:"advanced",capabilities:["vision"]},"cloud-claude-sonnet-4-thinking":{model:"anthropic/claude-sonnet-4-thinking",tier:"advanced",capabilities:["vision"]},"cloud-claude-opus-4":{model:"anthropic/claude-opus-4",tier:"advanced",capabilities:["vision"]},"cloud-claude-3.5-haiku":{model:"anthropic/claude-3.5-haiku",tier:"advanced",capabilities:[]},"cloud-claude-3.7-sonnet":{model:"anthropic/claude-3.7-sonnet",tier:"advanced",capabilities:["vision"]},"cloud-claude-3.7-sonnet-thinking":{model:"anthropic/claude-3.7-sonnet-thinking",tier:"advanced",capabilities:["vision"]},"cloud-mistral-large":{model:"mistral/mistral-large",tier:"advanced",capabilities:[]},"cloud-mistral-medium":{model:"mistral/mistral-medium",tier:"advanced",capabilities:["vision"]},"cloud-magistral-medium":{model:"mistral/magistral-medium",tier:"advanced",capabilities:[]},"cloud-pplx-sonar-online":{model:"perplexity/sonar-online",tier:"basic",capabilities:[]},"cloud-command-a":{model:"cohere/command-a",tier:"advanced",capabilities:[]},"cloud-qwen3":{model:"qwen/qwen3",tier:"basic",capabilities:[]},"cloud-qwen2.5-max":{model:"qwen/qwen2.5-max",tier:"advanced",capabilities:[]},"cloud-qwen2.5-72b":{model:"qwen/qwen2.5-72b",tier:"advanced",capabilities:[]},"cloud-qwq-32b":{model:"qwen/qwq-32b",tier:"advanced",capabilities:[]},"cloud-qvq-max":{model:"qwen/qvq-max",tier:"advanced",capabilities:["vision"]},"cloud-deepseek-v3":{model:"deepseek/deepseek-v3",tier:"basic",capabilities:[]},"cloud-deepseek-r1":{model:"deepseek/deepseek-r1",tier:"basic",capabilities:[]},"cloud-yi-large":{model:"01-ai/yi-large",tier:"advanced",capabilities:[]},"cloud-wizardlm-2":{model:"microsoft/wizardlm-2",tier:"advanced",capabilities:[]},"cloud-grok-3":{model:"x-ai/grok-3",tier:"advanced",capabilities:[]},"cloud-amazon-nova-pro":{model:"amazon/nova-pro-v1",tier:"advanced",capabilities:["vision"]},"cloud-doubao-1.5-pro":{model:"douban/doubao-1.5-pro",tier:"basic",capabilities:[]}};function Qi(e){return Oa[e]}function _i(e){return Object.entries(Oa).filter(([a,n])=>n.tier===e).map(([a])=>a)}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,a){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,a),a)}};const Yi=["openai","gemini","claude-api","perplexity-api","azure-openai","mistral-api","moonshot-api","minimax-api","deepseek-api","groq-api"],$i={chatgpt:{name:"ChatGPT",avatar:vi,logo:"chatgpt.png",vendor:"OpenAI"},openai:{name:"OpenAI",avatar:F,logo:"openai.png",vendor:"OpenAI"},"azure-openai":{name:"Azure OpenAI",avatar:F,logo:"openai.png",vendor:"OpenAI"},bard:{name:"Gemini",avatar:ee,logo:"gemini.png",vendor:"Google"},claude:{name:"Claude",avatar:U,logo:"anthropic.png",vendor:"Anthropic"},"claude-api":{name:"Claude",avatar:U,logo:"anthropic.png",vendor:"Anthropic"},deepseek:{name:"DeepSeek",avatar:se,logo:"deepseek.png",vendor:"DeepSeek"},llama:{name:"Llama 3",avatar:ve,logo:"llama.png",vendor:"Meta"},bing:{name:"MS Copilot",avatar:wi,logo:"copilot.png",vendor:"Microsoft"},perplexity:{name:"Perplexity",avatar:be,logo:"pplx.png",vendor:"Perplexity"},"perplexity-api":{name:"Perplexity",avatar:be,logo:"pplx.png",vendor:"Perplexity"},gemini:{name:"Gemini",avatar:ee,logo:"gemini.png",vendor:"Google"},mistral:{name:"Mistral",avatar:ae,logo:"mistral.png",vendor:"Mistral AI"},pi:{name:"Pi",avatar:ki,logo:"pi.png",vendor:"Pi"},gemma:{name:"Gemma",avatar:xi,logo:"gemma.png",vendor:"Google"},qianwen:{name:"Qianwen",avatar:j,logo:"qianwen.png",vendor:"Alibaba"},"moonshot-api":{name:"Moonshot AI",avatar:Ci,logo:"moonshot.png",vendor:"Moonshot AI"},"minimax-api":{name:"MiniMax",avatar:Pi,logo:"minimax.png",vendor:"MiniMax"},"mistral-api":{name:"Mistral",avatar:ae,logo:"mistral.png",vendor:"Mistral AI"},"groq-api":{name:"Groq",avatar:Si,logo:"groq.png",vendor:"Groq"},"deepseek-api":{name:"DeepSeek",avatar:se,logo:"deepseek.png",vendor:"DeepSeek"},"cloud-gpt-4.1":{name:"GPT-4.1",description:"GPT-4.1 is a flagship large language model optimized for advanced instruction following, real-world software engineering, and long-context reasoning. It supports a 1 million token context window and outperforms GPT-4o and GPT-4.5 across coding (54.6% SWE-bench Verified), instruction compliance (87.4% IFEval), and multimodal understanding benchmarks. It is tuned for precise code diffs, agent reliability, and high recall in large document contexts, making it ideal for agents, IDE tooling, and enterprise knowledge retrieval.",avatar:F,logo:"openai.png",vendor:"OpenAI"},"cloud-gpt-4.1-mini":{name:"GPT-4.1 mini",description:"GPT-4.1 mini is a mid-sized model delivering performance competitive with GPT-4o at substantially lower latency and cost. It retains a 1 million token context window and scores 45.1% on hard instruction evals, 35.8% on MultiChallenge, and 84.1% on IFEval. Mini also shows strong coding ability (e.g., 31.6% on Aider’s polyglot diff benchmark) and vision understanding, making it suitable for interactive applications with tight performance constraints.",avatar:F,logo:"openai.png",vendor:"OpenAI"},"cloud-gpt-4o":{name:"GPT-4o",description:"OpenAI's high-intelligence flagship model for complex, multi-step tasks. GPT-4o is cheaper and faster than GPT-4 Turbo.",avatar:F,logo:"openai.png",vendor:"OpenAI"},"cloud-gpt-4o-image":{name:"GPT-4o Image",description:"GPT-4o with image generation capabilities. It's a powerful model for creating images from text.",avatar:F,logo:"openai.png",vendor:"OpenAI",note:"This is a special version of GPT-4o for experimenting with image generation (yes, the one that can generate Studio Ghibli style images)"},"cloud-o4-mini":{name:"o4-mini-high",description:"OpenAI o4-mini is a compact reasoning model in the o-series, optimized for fast, cost-efficient performance while retaining strong multimodal and agentic capabilities. It supports tool use and demonstrates competitive reasoning and coding performance across benchmarks like AIME (99.5% with Python) and SWE-bench, outperforming its predecessor o3-mini and even approaching o3 in some domains.",avatar:F,logo:"openai.png",vendor:"OpenAI"},"cloud-o3":{name:"o3",description:"OpenAI o3 is a reflective generative pre-trained transformer (GPT) model developed by OpenAI as a successor to OpenAI o1.",avatar:F,logo:"openai.png",vendor:"OpenAI"},"cloud-claude-sonnet-4":{name:"Claude Sonnet 4",description:"Claude Sonnet 4 significantly enhances the capabilities of its predecessor, Sonnet 3.7, excelling in both coding and reasoning tasks with improved precision and controllability. Achieving state-of-the-art performance on SWE-bench (72.7%), Sonnet 4 balances capability and computational efficiency, making it suitable for a broad range of applications from routine coding tasks to complex software development projects. Key enhancements include improved autonomous codebase navigation, reduced error rates in agent-driven workflows, and increased reliability in following intricate instructions. Sonnet 4 is optimized for practical everyday use, providing advanced reasoning capabilities while maintaining efficiency and responsiveness in diverse internal and external scenarios.",avatar:U,logo:"anthropic.png",vendor:"Anthropic"},"cloud-claude-sonnet-4-thinking":{name:"Claude Sonnet 4 Thinking",description:"Claude Sonnet 4 significantly enhances the capabilities of its predecessor, Sonnet 3.7, excelling in both coding and reasoning tasks with improved precision and controllability. Achieving state-of-the-art performance on SWE-bench (72.7%), Sonnet 4 balances capability and computational efficiency, making it suitable for a broad range of applications from routine coding tasks to complex software development projects. Key enhancements include improved autonomous codebase navigation, reduced error rates in agent-driven workflows, and increased reliability in following intricate instructions. Sonnet 4 is optimized for practical everyday use, providing advanced reasoning capabilities while maintaining efficiency and responsiveness in diverse internal and external scenarios.",avatar:U,logo:"anthropic.png",vendor:"Anthropic"},"cloud-claude-opus-4":{name:"Claude Opus 4",description:"Claude Opus 4 is benchmarked as the world’s best coding model, at time of release, bringing sustained performance on complex, long-running tasks and agent workflows. It sets new benchmarks in software engineering, achieving leading results on SWE-bench (72.5%) and Terminal-bench (43.2%). Opus 4 supports extended, agentic workflows, handling thousands of task steps continuously for hours without degradation.",avatar:U,logo:"anthropic.png",vendor:"Anthropic"},"cloud-claude-3.5-haiku":{name:"Claude 3.5 Haiku",description:"Claude 3.5 Haiku features offers enhanced capabilities in speed, coding accuracy, and tool use. Engineered to excel in real-time applications, it delivers quick response times that are essential for dynamic tasks such as chat interactions and immediate coding suggestions.",avatar:U,logo:"anthropic.png",vendor:"Anthropic"},"cloud-claude-3.7-sonnet":{name:"Claude 3.7 Sonnet",description:"Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes.",avatar:U,logo:"anthropic.png",vendor:"Anthropic"},"cloud-claude-3.7-sonnet-thinking":{name:"Claude 3.7 Sonnet Thinking",description:"Claude 3.7 Sonnet with thinking mode.",avatar:U,logo:"anthropic.png",vendor:"Anthropic"},"cloud-llama4":{name:"Llama 4",description:"The Llama 4 collection of models are natively multimodal AI models that enable text and multimodal experiences. These models leverage a mixture-of-experts architecture to offer industry-leading performance in text and image understanding.",avatar:ve,logo:"llama.png",vendor:"Meta"},"cloud-llama3":{name:"Llama 3.3 70B",description:"The Meta Llama 3.3 multilingual large language model (LLM) is a pretrained and instruction tuned generative model in 70B (text in/text out). The Llama 3.3 instruction tuned text only model is optimized for multilingual dialogue use cases and outperform many of the available open source and closed chat models on common industry benchmarks.",avatar:ve,logo:"llama.png",vendor:"Meta"},"cloud-gemini-2.5-pro":{name:"Gemini 2.5 Pro",description:"Gemini 2.5 Pro is Google's state-of-the-art AI model designed for advanced reasoning, coding, mathematics, and scientific tasks. It employs “thinking” capabilities, enabling it to reason through responses with enhanced accuracy and nuanced context handling. Gemini 2.5 Pro achieves top-tier performance on multiple benchmarks, including first-place positioning on the LMArena leaderboard, reflecting superior human-preference alignment and complex problem-solving abilities.",avatar:ee,logo:"gemini.png",vendor:"Google"},"cloud-gemini-2.5-flash":{name:"Gemini 2.5 Flash",description:"Gemini 2.5 Flash is Google's first fully hybrid reasoning model that allows developers to toggle thinking capabilities on or off according to their needs, offering enhanced reasoning abilities while maintaining the speed and cost-effectiveness of its predecessor.",avatar:ee,logo:"gemini.png",vendor:"Google"},"cloud-gemini-2.0-flash":{name:"Gemini 2.0 Flash",description:"Gemini 2.0 Flash builds on the success of 1.5 Flash, offering improved performance and twice the speed of 1.5 Pro on key benchmarks. It supports multimodal inputs like images, video, and audio, as well as outputs such as generated images, text, and multilingual text-to-speech. Additionally, it can natively integrate with tools like Google Search, execute code, and use third-party functions.",avatar:ee,logo:"gemini.png",vendor:"Google"},"cloud-mistral-large":{name:"Mistral Large",description:"Mistral Large is a cutting-edge language model developed by Mistral AI, renowned for its advanced reasoning capabilities. It excels in multilingual tasks, code generation, and complex problem-solving, making it ideal for diverse text-based applications.",avatar:ae,logo:"mistral.png",vendor:"Mistral AI"},"cloud-mistral-medium":{name:"Mistral Medium 3",description:"Mistral Medium 3 is a cutting-edge language model designed for enterprise use, offering state-of-the-art performance at 8 times lower cost compared to competitors. It excels in professional applications like coding and multimodal understanding, while providing flexible deployment options and seamless integration into enterprise systems.",avatar:ae,logo:"mistral.png",vendor:"Mistral AI"},"cloud-magistral-medium":{name:"Magistral Medium",description:"The first reasoning model by Mistral AI — excelling in domain-specific, transparent, and multilingual reasoning.",avatar:ae,logo:"mistral.png",vendor:"Mistral AI"},"cloud-pplx-sonar-online":{name:"Perplexity Sonar",description:"The Perplexity Sonar Online model is a state-of-the-art large language model developed by Perplexity AI. It offers real-time internet access, ensuring up-to-date information retrieval. Known for its cost-efficiency, speed, and enhanced performance, it surpasses previous models in the Sonar family, making it ideal for dynamic and accurate data processing.",avatar:be,logo:"pplx.png",vendor:"Perplexity"},"cloud-command-a":{name:"Command A",description:"Command A is an open-weights 111B parameter model with a 256k context window focused on delivering great performance across agentic, multilingual, and coding use cases.",avatar:bi,logo:"cohere.png",vendor:"Cohere"},"cloud-qwen3":{name:"Qwen3 235B",description:'Qwen3-235B-A22B is a 235B parameter mixture-of-experts (MoE) model developed by Qwen, activating 22B parameters per forward pass. It supports seamless switching between a "thinking" mode for complex reasoning, math, and code tasks, and a "non-thinking" mode for general conversational efficiency. The model demonstrates strong reasoning ability, multilingual support (100+ languages and dialects), advanced instruction-following, and agent tool-calling capabilities. It natively handles a 32K token context window and extends up to 131K tokens using YaRN-based scaling.',avatar:j,logo:"qianwen.png",vendor:"Alibaba"},"cloud-qwen2.5-max":{name:"Qwen2.5 Max",description:"Qwen2.5-Max is a large MoE LLM pretrained on massive data and post-trained with curated SFT and RLHF recipes. It achieves competitive performance against the top-tier models, and outcompetes DeepSeek V3 in benchmarks like Arena Hard, LiveBench, LiveCodeBench, GPQA-Diamond.",avatar:j,logo:"qianwen.png",vendor:"Alibaba"},"cloud-qwen2.5-72b":{name:"Qwen2.5 72B",description:"Qwen2.5 is a model pretrained on a large-scale dataset of up to 18 trillion tokens, offering significant improvements in knowledge, coding, mathematics, and instruction following compared to its predecessor Qwen2. The model also features enhanced capabilities in generating long texts, understanding structured data, and generating structured outputs, while supporting multilingual capabilities for over 29 languages.",avatar:j,logo:"qianwen.png",vendor:"Alibaba"},"cloud-qwq-32b":{name:"QwQ 32B",description:"QwQ is an experimental research model developed by the Qwen Team, designed to advance AI reasoning capabilities. This model embodies the spirit of philosophical inquiry, approaching problems with genuine wonder and doubt. QwQ demonstrates impressive analytical abilities, achieving scores of 65.2% on GPQA, 50.0% on AIME, 90.6% on MATH-500, and 50.0% on LiveCodeBench. With its contemplative approach and exceptional performance on complex problems.",avatar:j,logo:"qianwen.png",vendor:"Alibaba"},"cloud-qvq-max":{name:"QvQ Max",description:'QVQ-Max, the inaugural official visual reasoning model from the Qwen Team, was released in March 2025. This model builds upon their earlier exploratory work with QVQ-72B-Preview. QVQ-Max is engineered to not only "see" content within images and videos but also to analyze and reason based on this visual data. Furthermore, it can generate solutions for diverse challenges, spanning mathematical problems, everyday scenarios, programming code, and artistic endeavors. Although this marks its first iteration, QVQ-Max showcases considerable promise as a practical visual agent equipped with both strong visual perception and analytical capabilities.',avatar:j,logo:"qianwen.png",vendor:"Alibaba"},"cloud-yi-large":{name:"Yi-Large",description:"The Yi Large model was designed by 01.AI with the following usecases in mind: knowledge search, data classification, human-like chat bots, and customer service.  It stands out for its multilingual proficiency, particularly in Spanish, Chinese, Japanese, German, and French.",avatar:Mi,logo:"yi.png",vendor:"01.AI"},"cloud-deepseek-v3":{name:"DeepSeek V3",description:"DeepSeek-V3 is the latest open-source model from DeepSeek, DeepSeek-V3 has outperformed other open-source models like Qwen2.5-72B and Llama-3.1-405B in various evaluations, and its performance is on par with world-class closed-source models like GPT-4o and Claude-3.5-Sonnet.",avatar:se,logo:"deepseek.png",vendor:"DeepSeek"},"cloud-deepseek-r1":{name:"DeepSeek R1",description:"DeepSeek R1 is a cutting-edge AI model developed by DeepSeek that was released as a competitor to OpenAI's o1 model. This model emphasizes strong reasoning capabilities in areas such as complex math, coding, and logic. Designed to compete with leading AI models, it offers both transparency and competitive performance, making it a significant step forward in open-source AI development.",avatar:se,logo:"deepseek.png",vendor:"DeepSeek"},"cloud-wizardlm-2":{name:"WizardLM 2",description:"WizardLM-2 8x22B is Microsoft AI's most advanced Wizard model. It demonstrates highly competitive performance compared to leading proprietary models, and it consistently outperforms all existing state-of-the-art opensource models.",avatar:Ii,logo:"wizardlm.png",vendor:"Microsoft"},"cloud-grok-3":{name:"Grok 3",description:"Grok 3 is the most advanced model from xAI. Grok 3 displays significant improvements in reasoning, mathematics, coding, world knowledge, and instruction-following tasks.",avatar:Ti,logo:"grok.png",vendor:"xAI"},"cloud-amazon-nova-pro":{name:"Amazon Nova Pro",description:"Amazon Nova Pro 1.0 is a capable multimodal model from Amazon focused on providing a combination of accuracy, speed, and cost for a wide range of tasks. As of December 2024, it achieves state-of-the-art performance on key benchmarks including visual question answering (TextVQA) and video understanding (VATEX).",avatar:Oi,logo:"nova.png",vendor:"Amazon"},"cloud-doubao-1.5-pro":{name:"Doubao 1.5 Pro",description:"Doubao 1.5 Pro is a Chinese model from Doubao.",avatar:yi,logo:"doubao.png",vendor:"Doubao"}},eo=["cloud-gpt-4.1","cloud-claude-sonnet-4","cloud-gemini-2.5-pro","cloud-llama4","cloud-deepseek-v3","cloud-qwen2.5-72b"];globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,a){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,a),a)}};const Di="all",ao="You are ChatGPT, a large language model trained by OpenAI. Current date: {current_date}.",Ri=["chatgpt","bard","claude","llama","bing","perplexity","mistral","gemma"],ka="https://app.chathub.gg",no=`${ka}/profile`,ro=`${ka}/subscription/checkout?app=extension`;var Ia=Symbol(),we=Symbol();function Li(e){return async(a,n)=>{let r;if("~standard"in e){let t=e["~standard"].validate(a);if(t instanceof Promise&&(t=await t),t.issues)throw t.issues;r=t.value}else r=await e.parseAsync(a);return n(r,!1)}}function qi({fallbackToCache:e,checkValue:a,...n},r){const t=n.ttl??1/0,i=n.swr??n.staleWhileRevalidate??0;return{...{checkValue:typeof a=="function"?a:typeof a=="object"?Li(a):()=>!0,ttl:t,staleWhileRevalidate:i,fallbackToCache:e===!1?0:e===!0||e===void 0?1/0:e,staleRefreshTimeout:0,forceFresh:!1,...n,metadata:Ma({ttl:t,swr:i}),waitUntil:n.waitUntil??(()=>{})},report:()=>{}}}function Bi(e){return(typeof e.swr>"u"?e.swv:e.swr)||null}function Ma({ttl:e=null,swr:a=0,createdTime:n=Date.now()}={}){return{ttl:e===1/0?null:e,swr:a===1/0?null:a,createdTime:n}}function Ui(e,a){return{value:e,metadata:Ma(a)}}function ye(e){return e?`for ${e} `:""}function Fi(e,a){if(!oa(e))throw new Error(`Cache entry ${ye(a)}is not a cache entry object, it's a ${typeof e}`);if(!oa(e.metadata)||typeof e.metadata.createdTime!="number"||e.metadata.ttl!=null&&typeof e.metadata.ttl!="number"||e.metadata.swr!=null&&typeof e.metadata.swr!="number")throw new Error(`Cache entry ${ye(a)}does not have valid metadata property`);if(!("value"in e))throw new Error(`Cache entry for ${ye(a)}does not have a value property`)}function oa(e){return typeof e=="object"&&e!==null&&!Array.isArray(e)}function Ue(e){if(e.ttl===null)return!1;const a=e.createdTime+(e.ttl||0),n=a+(Bi(e)||0),r=Date.now();return r<=a?!1:r<=n?"stale":!0}async function Ea(e,a){try{const n=await e.checkValue(a,(r,t=!0)=>({[we]:t,value:r}));return typeof n=="string"?{success:!1,reason:n}:n==null||n===!0?{success:!0,value:a,migrated:!1}:n&&typeof n[we]=="boolean"?{success:!0,migrated:n[we],value:n.value}:{success:!1,reason:"unknown"}}catch(n){return{success:!1,reason:n}}}var J=Symbol();async function Da({key:e,cache:a},n){n({name:"getCachedValueStart"});const r=await a.get(e);return n({name:"getCachedValueRead",entry:r}),r?(Fi(r,e),r):J}async function zi(e,a,n){const{key:r,cache:t,staleWhileRevalidate:i,staleRefreshTimeout:o,metadata:d,getFreshValue:{[Ia]:g}}=e;try{const s=await Da(e,a);if(s===J)return a({name:"getCachedValueEmpty"}),J;const u=Ue(s.metadata),A=u==="stale"||u===!0&&i===1/0;if(u===!0&&a({name:"getCachedValueOutdated",...s}),A&&e.waitUntil(Ra({...e,async getFreshValue({metadata:m}){return await sa(o),a({name:"refreshValueStart"}),e.getFreshValue({metadata:m,background:!0})},forceFresh:!0,fallbackToCache:!1}).then(m=>{a({name:"refreshValueSuccess",value:m})}).catch(m=>{a({name:"refreshValueError",error:m})})),!u||A){const m=await Ea(e,s.value);if(m.success)return a({name:"getCachedValueSuccess",value:m.value,migrated:m.migrated}),A||g==null||g(),m.migrated&&e.waitUntil(Promise.resolve().then(async()=>{try{await sa(0);const b=await e.cache.get(e.key);b&&b.metadata.createdTime===d.createdTime&&!n()&&await e.cache.set(e.key,{...b,value:m.value})}catch{}})),m.value;a({name:"checkCachedValueErrorObj",reason:m.reason}),a({name:"checkCachedValueError",reason:m.reason instanceof Error?m.reason.message:String(m.reason)}),await t.delete(r)}}catch(s){a({name:"getCachedValueError",error:s}),await t.delete(r)}return J}function sa(e){return new Promise(a=>setTimeout(a,e))}async function Ni(e,a,n){const{fallbackToCache:r,key:t,getFreshValue:i,forceFresh:o,cache:d}=e;let g;try{n({name:"getFreshValueStart"});const u=await i({metadata:e.metadata,background:!1});g=u,n({name:"getFreshValueSuccess",value:u})}catch(u){if(n({name:"getFreshValueError",error:u}),o&&r>0){const A=await Da(e,n);if(A===J||A.metadata.createdTime+r<Date.now())throw u;g=A.value,n({name:"getFreshValueCacheFallback",value:g})}else throw u}const s=await Ea(e,g);if(!s.success)throw n({name:"checkFreshValueErrorObj",reason:s.reason}),n({name:"checkFreshValueError",reason:s.reason instanceof Error?s.reason.message:String(s.reason)}),new Error(`check failed for fresh value of ${t}`,{cause:s.reason});try{const u=Ue(a)!==!0;u&&await d.set(t,Ui(g,a)),n({name:"writeFreshValueSuccess",metadata:a,migrated:s.migrated,written:u})}catch(u){n({name:"writeFreshValueError",error:u})}return s.value}var xe=new WeakMap;async function Ra(e,a){var w,y;const n=qi(e),{key:r,cache:t,forceFresh:i,report:o,metadata:d}=n;xe.has(t)||xe.set(t,new Map);const g=xe.get(t),u=i?J:await zi(n,o,()=>g.has(r));if(u!==J)return o({name:"done",value:u}),u;if(g.has(r)){const{value:S,metadata:P}=g.get(r);if(!Ue(P)){(y=(w=n.getFreshValue)[Ia])==null||y.call(w),o({name:"getFreshValueHookPending"});const O=await S;return o({name:"done",value:O}),O}}let A;const m=Promise.race([Ni(n,d,o),new Promise(S=>{A=S})]).finally(()=>{g.delete(r)});if(g.has(r)){const{resolve:S}=g.get(r);m.then(P=>S(P))}g.set(r,{metadata:d,value:m,resolve:A});const b=await m;return o({name:"done",value:b}),b}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,a){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,a),a)}};var Wi=(e=>(e.Auto="auto",e["GPT-4o"]="gpt-4o",e["GPT-4o-mini"]="gpt-4o-mini",e["o3-mini"]="o3-mini",e["o3-mini-high"]="o3-mini-high",e.o1="o1",e["o1-mini"]="o1-mini",e["o1-preview"]="o1-preview",e["GPT-4"]="gpt-4",e["GPT-3.5"]="gpt-3.5",e))(Wi||{});const la={startupPage:Di,sendWithCtrlEnter:!1,expandCompactInputByDefault:!1,enabledBots:Ri,disabledCloudBots:[],chatgptWebappModelName:"auto",chatgptWebappHistoryDisabled:!1,openaiApiKey:"",openaiApiHost:"https://api.openai.com",chatgptApiModel:"gpt-4o",chatgptApiTemperature:1,chatgptApiSystemMessage:"",bingConversationStyle:"balanced",bingDisableSearch:!1,azureOpenAIApiKey:"",azureOpenAIApiInstanceName:"",azureOpenAIApiDeploymentName:"",claudeApiKey:"",claudeApiModel:"claude-3-5-sonnet",claudeApiBaseUrl:"",claudeApiSystemMessage:"",perplexityApiKey:"",perplexityApiModel:"sonar",geminiApiKey:"",geminiApiModel:"gemini-1.5-pro",geminiApiBaseUrl:"",moonshotApiKey:"",moonshotApiModel:"moonshot-v1-32k",mistralApiKey:"",mistralApiModel:"mistral-large-latest",groqApiKey:"",groqApiModel:"llama3-70b-8192",minimaxApiKey:"",minimaxApiModel:"MiniMax-Text-01",deepseekApiKey:"",deepseekApiModel:"deepseek-reasoner",customBots:{}};async function Ji(){const a=(await N.storage.local.get("customBots")).customBots||{};return hi(a,n=>({...n,apiEndpoint:Vi(n.vendor,n.apiEndpoint)}))}async function Ki(e){await N.storage.local.set({customBots:e}),await N.storage.sync.remove("customBots")}async function Zi(){const[e,a]=await Promise.all([N.storage.sync.get(Object.keys(la)),Ji()]);e.chatgptApiModel==="gpt-3.5-turbo-16k"?e.chatgptApiModel="gpt-3.5-turbo":e.chatgptApiModel==="gpt-4-32k"&&(e.chatgptApiModel="gpt-4");const n=Ai(e,la);return n.customBots={...n.customBots,...a},n}const Gi=new Map;async function Xi(){return Ra({key:"user-config",cache:Gi,getFreshValue:Zi})}async function to(e){const{customBots:a,...n}=e;await N.storage.sync.set(n),a&&await Ki(a);for(const[r,t]of Object.entries(e))t===void 0&&await N.storage.sync.remove(r)}const Hi=new Set(["chatgpt","openai","azure-openai","claude","claude-api","gemini","mistral-api","moonshot-api","groq-api","minimax-api"]);async function ji(e){if(Hi.has(e)||Ei.includes(e))return!0;const n=(await Xi()).customBots[e];return n?!!n.supportFunctionCalling:!1}async function io(e){if(!await ji(e))return!1;const a=`web-access-${e}`,{[a]:n}=await N.storage.sync.get(a);return!!n}async function oo(e,a){const n=`web-access-${e}`;await N.storage.sync.set({[n]:a})}function Vi(e,a){if(e==="custom"||e==="straico")return a;const[n,r="1"]=a.split(/\/v(\d+(alpha|beta)?)/);return n.endsWith("/")?`${n}v${r}`:`${n}/v${r}`}export{vi as $,Di as A,N as B,ke as C,Ie as D,W as E,nt as F,pi as G,Oe as H,It as I,ce as J,Pt as K,xa as L,sr as M,ti as N,hi as O,Ai as P,$r as Q,qe as R,E as S,ya as T,me as U,Yr as V,eo as W,at as X,ka as Y,no as Z,ro as _,di as a,F as a0,U as a1,ee as a2,ae as a3,ve as a4,be as a5,_i as a6,$i as a7,ji as a8,oo as a9,io as aa,ao as ab,Wi as ac,Ei as ad,Yi as ae,to as af,dr as ag,R as ah,mt as ai,gt as aj,pt as ak,aa as al,pa as am,z as an,Xe as ao,Ve as ap,fr as aq,Me as ar,li as as,ua as at,In as au,Ri as av,Ra as aw,Xi as ax,Qi as ay,St as az,Pn as b,vn as c,De as d,D as e,ja as f,Zi as g,ct as h,ma as i,qa as j,Re as k,Ba as l,On as m,V as n,Sn as o,Q as p,ge as q,Tn as r,wn as s,wr as t,Se as u,ba as v,Aa as w,ie as x,mi as y,ri as z};
//# sourceMappingURL=user-config-BrqC82sm.js.map
