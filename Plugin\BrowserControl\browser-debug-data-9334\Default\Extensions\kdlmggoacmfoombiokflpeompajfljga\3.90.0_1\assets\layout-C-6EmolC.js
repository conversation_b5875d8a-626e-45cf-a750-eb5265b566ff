import{G as g}from"./graph-Cuz86YKM.js";import{aR as z}from"./MermaidPreview-DN0CF7bz.js";import{e as fe,a as Re,f,v as x}from"./_baseUniq-CDn7CWnQ.js";import{b as Ie,f as _,m as w,l as I}from"./map-CPMUsqym.js";import{y as Te,t as Me,G as Fe,D as ce,a as le,C as Q,f as j,e as he,L as Se,M as je,N as Ve,m as $,b as Be,c as Ae,O as M,P as Ge}from"./user-config-BrqC82sm.js";import{F as De,G as V,I as Ye,i as b,J as $e}from"./premium-CrQDER7x.js";import{b as ve,a as qe,m as L,h as pe,f as X}from"./min-1QEMBxke.js";import{f as C,r as F}from"./reduce-d3EV_kCa.js";import{f as We}from"./_flatRest-DPsYhEwM.js";import{m as q}from"./merge-CG3iZ9md.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},n=new Error().stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="ded9a15f-3346-4b10-ac4e-9d393adfa93c",e._sentryDebugIdIdentifier="sentry-dbid-ded9a15f-3346-4b10-ac4e-9d393adfa93c")}catch{}})();function ze(e,n){return e==null?e:Te(e,fe(n),Me)}function Xe(e,n){return e&&Fe(e,fe(n))}function Ue(e,n){return e>n}function y(e){return e&&e.length?ve(e,ce,Ue):void 0}function U(e,n){return e&&e.length?ve(e,le(n),qe):void 0}function He(e,n){var r=e.length;for(e.sort(n);r--;)e[r]=e[r].value;return e}function Je(e,n){if(e!==n){var r=e!==void 0,t=e===null,a=e===e,o=Q(e),i=n!==void 0,u=n===null,d=n===n,s=Q(n);if(!u&&!s&&!o&&e>n||o&&i&&d&&!u&&!s||t&&i&&d||!r&&d||!a)return 1;if(!t&&!o&&!s&&e<n||s&&r&&a&&!t&&!o||u&&r&&a||!i&&a||!d)return-1}return 0}function Ze(e,n,r){for(var t=-1,a=e.criteria,o=n.criteria,i=a.length,u=r.length;++t<i;){var d=Je(a[t],o[t]);if(d){if(t>=u)return d;var s=r[t];return d*(s=="desc"?-1:1)}}return e.index-n.index}function Ke(e,n,r){n.length?n=j(n,function(o){return he(o)?function(i){return Se(i,o.length===1?o[0]:o)}:o}):n=[ce];var t=-1;n=j(n,je(le));var a=Ie(e,function(o,i,u){var d=j(n,function(s){return s(o)});return{criteria:d,index:++t,value:o}});return He(a,function(o,i){return Ze(o,i,r)})}function Qe(e,n){return De(e,n,function(r,t){return Ve(e,t)})}var T=We(function(e,n){return e==null?{}:Qe(e,n)}),en=Math.ceil,nn=Math.max;function rn(e,n,r,t){for(var a=-1,o=nn(en((n-e)/(r||1)),0),i=Array(o);o--;)i[++a]=e,e+=r;return i}function tn(e){return function(n,r,t){return t&&typeof t!="number"&&$(n,r,t)&&(r=t=void 0),n=V(n),r===void 0?(r=n,n=0):r=V(r),t=t===void 0?n<r?1:-1:V(t),rn(n,r,t)}}var E=tn(),R=Be(function(e,n){if(e==null)return[];var r=n.length;return r>1&&$(e,n[0],n[1])?n=[]:r>2&&$(n[0],n[1],n[2])&&(n=[n[0]]),Ke(e,Re(n),[])});function an(e,n,r){for(var t=-1,a=e.length,o=n.length,i={};++t<a;){var u=t<o?n[t]:void 0;r(i,e[t],u)}return i}function on(e,n){return an(e||[],n||[],Ye)}class un{constructor(){var n={};n._next=n._prev=n,this._sentinel=n}dequeue(){var n=this._sentinel,r=n._prev;if(r!==n)return ee(r),r}enqueue(n){var r=this._sentinel;n._prev&&n._next&&ee(n),n._next=r._next,r._next._prev=n,r._next=n,n._prev=r}toString(){for(var n=[],r=this._sentinel,t=r._prev;t!==r;)n.push(JSON.stringify(t,dn)),t=t._prev;return"["+n.join(", ")+"]"}}function ee(e){e._prev._next=e._next,e._next._prev=e._prev,delete e._next,delete e._prev}function dn(e,n){if(e!=="_next"&&e!=="_prev")return n}var sn=Ae(1);function fn(e,n){if(e.nodeCount()<=1)return[];var r=ln(e,n||sn),t=cn(r.graph,r.buckets,r.zeroIdx);return _(w(t,function(a){return e.outEdges(a.v,a.w)}))}function cn(e,n,r){for(var t=[],a=n[n.length-1],o=n[0],i;e.nodeCount();){for(;i=o.dequeue();)B(e,n,r,i);for(;i=a.dequeue();)B(e,n,r,i);if(e.nodeCount()){for(var u=n.length-2;u>0;--u)if(i=n[u].dequeue(),i){t=t.concat(B(e,n,r,i,!0));break}}}return t}function B(e,n,r,t,a){var o=a?[]:void 0;return f(e.inEdges(t.v),function(i){var u=e.edge(i),d=e.node(i.v);a&&o.push({v:i.v,w:i.w}),d.out-=u,W(n,r,d)}),f(e.outEdges(t.v),function(i){var u=e.edge(i),d=i.w,s=e.node(d);s.in-=u,W(n,r,s)}),e.removeNode(t.v),o}function ln(e,n){var r=new g,t=0,a=0;f(e.nodes(),function(u){r.setNode(u,{v:u,in:0,out:0})}),f(e.edges(),function(u){var d=r.edge(u.v,u.w)||0,s=n(u),c=d+s;r.setEdge(u.v,u.w,c),a=Math.max(a,r.node(u.v).out+=s),t=Math.max(t,r.node(u.w).in+=s)});var o=E(a+t+3).map(function(){return new un}),i=t+1;return f(r.nodes(),function(u){W(o,i,r.node(u))}),{graph:r,buckets:o,zeroIdx:i}}function W(e,n,r){r.out?r.in?e[r.out-r.in+n].enqueue(r):e[e.length-1].enqueue(r):e[0].enqueue(r)}function hn(e){var n=e.graph().acyclicer==="greedy"?fn(e,r(e)):vn(e);f(n,function(t){var a=e.edge(t);e.removeEdge(t),a.forwardName=t.name,a.reversed=!0,e.setEdge(t.w,t.v,a,z("rev"))});function r(t){return function(a){return t.edge(a).weight}}}function vn(e){var n=[],r={},t={};function a(o){Object.prototype.hasOwnProperty.call(t,o)||(t[o]=!0,r[o]=!0,f(e.outEdges(o),function(i){Object.prototype.hasOwnProperty.call(r,i.w)?n.push(i):a(i.w)}),delete r[o])}return f(e.nodes(),a),n}function pn(e){f(e.edges(),function(n){var r=e.edge(n);if(r.reversed){e.removeEdge(n);var t=r.forwardName;delete r.reversed,delete r.forwardName,e.setEdge(n.w,n.v,r,t)}})}function O(e,n,r,t){var a;do a=z(t);while(e.hasNode(a));return r.dummy=n,e.setNode(a,r),a}function wn(e){var n=new g().setGraph(e.graph());return f(e.nodes(),function(r){n.setNode(r,e.node(r))}),f(e.edges(),function(r){var t=n.edge(r.v,r.w)||{weight:0,minlen:1},a=e.edge(r);n.setEdge(r.v,r.w,{weight:t.weight+a.weight,minlen:Math.max(t.minlen,a.minlen)})}),n}function we(e){var n=new g({multigraph:e.isMultigraph()}).setGraph(e.graph());return f(e.nodes(),function(r){e.children(r).length||n.setNode(r,e.node(r))}),f(e.edges(),function(r){n.setEdge(r,e.edge(r))}),n}function ne(e,n){var r=e.x,t=e.y,a=n.x-r,o=n.y-t,i=e.width/2,u=e.height/2;if(!a&&!o)throw new Error("Not possible to find intersection inside of the rectangle");var d,s;return Math.abs(o)*i>Math.abs(a)*u?(o<0&&(u=-u),d=u*a/o,s=u):(a<0&&(i=-i),d=i,s=i*o/a),{x:r+d,y:t+s}}function S(e){var n=w(E(me(e)+1),function(){return[]});return f(e.nodes(),function(r){var t=e.node(r),a=t.rank;b(a)||(n[a][t.order]=r)}),n}function mn(e){var n=L(w(e.nodes(),function(r){return e.node(r).rank}));f(e.nodes(),function(r){var t=e.node(r);pe(t,"rank")&&(t.rank-=n)})}function bn(e){var n=L(w(e.nodes(),function(o){return e.node(o).rank})),r=[];f(e.nodes(),function(o){var i=e.node(o).rank-n;r[i]||(r[i]=[]),r[i].push(o)});var t=0,a=e.graph().nodeRankFactor;f(r,function(o,i){b(o)&&i%a!==0?--t:t&&f(o,function(u){e.node(u).rank+=t})})}function re(e,n,r,t){var a={width:0,height:0};return arguments.length>=4&&(a.rank=r,a.order=t),O(e,"border",a,n)}function me(e){return y(w(e.nodes(),function(n){var r=e.node(n).rank;if(!b(r))return r}))}function gn(e,n){var r={lhs:[],rhs:[]};return f(e,function(t){n(t)?r.lhs.push(t):r.rhs.push(t)}),r}function yn(e,n){return n()}function kn(e){function n(r){var t=e.children(r),a=e.node(r);if(t.length&&f(t,n),Object.prototype.hasOwnProperty.call(a,"minRank")){a.borderLeft=[],a.borderRight=[];for(var o=a.minRank,i=a.maxRank+1;o<i;++o)te(e,"borderLeft","_bl",r,a,o),te(e,"borderRight","_br",r,a,o)}}f(e.children(),n)}function te(e,n,r,t,a,o){var i={width:0,height:0,rank:o,borderType:n},u=a[n][o-1],d=O(e,"border",i,r);a[n][o]=d,e.setParent(d,t),u&&e.setEdge(u,d,{weight:1})}function xn(e){var n=e.graph().rankdir.toLowerCase();(n==="lr"||n==="rl")&&be(e)}function En(e){var n=e.graph().rankdir.toLowerCase();(n==="bt"||n==="rl")&&On(e),(n==="lr"||n==="rl")&&(Nn(e),be(e))}function be(e){f(e.nodes(),function(n){ae(e.node(n))}),f(e.edges(),function(n){ae(e.edge(n))})}function ae(e){var n=e.width;e.width=e.height,e.height=n}function On(e){f(e.nodes(),function(n){A(e.node(n))}),f(e.edges(),function(n){var r=e.edge(n);f(r.points,A),Object.prototype.hasOwnProperty.call(r,"y")&&A(r)})}function A(e){e.y=-e.y}function Nn(e){f(e.nodes(),function(n){G(e.node(n))}),f(e.edges(),function(n){var r=e.edge(n);f(r.points,G),Object.prototype.hasOwnProperty.call(r,"x")&&G(r)})}function G(e){var n=e.x;e.x=e.y,e.y=n}function Ln(e){e.graph().dummyChains=[],f(e.edges(),function(n){Pn(e,n)})}function Pn(e,n){var r=n.v,t=e.node(r).rank,a=n.w,o=e.node(a).rank,i=n.name,u=e.edge(n),d=u.labelRank;if(o!==t+1){e.removeEdge(n);var s=void 0,c,l;for(l=0,++t;t<o;++l,++t)u.points=[],s={width:0,height:0,edgeLabel:u,edgeObj:n,rank:t},c=O(e,"edge",s,"_d"),t===d&&(s.width=u.width,s.height=u.height,s.dummy="edge-label",s.labelpos=u.labelpos),e.setEdge(r,c,{weight:u.weight},i),l===0&&e.graph().dummyChains.push(c),r=c;e.setEdge(r,a,{weight:u.weight},i)}}function _n(e){f(e.graph().dummyChains,function(n){var r=e.node(n),t=r.edgeLabel,a;for(e.setEdge(r.edgeObj,t);r.dummy;)a=e.successors(n)[0],e.removeNode(n),t.points.push({x:r.x,y:r.y}),r.dummy==="edge-label"&&(t.x=r.x,t.y=r.y,t.width=r.width,t.height=r.height),n=a,r=e.node(n)})}function H(e){var n={};function r(t){var a=e.node(t);if(Object.prototype.hasOwnProperty.call(n,t))return a.rank;n[t]=!0;var o=L(w(e.outEdges(t),function(i){return r(i.w)-e.edge(i).minlen}));return(o===Number.POSITIVE_INFINITY||o===void 0||o===null)&&(o=0),a.rank=o}f(e.sources(),r)}function P(e,n){return e.node(n.w).rank-e.node(n.v).rank-e.edge(n).minlen}function ge(e){var n=new g({directed:!1}),r=e.nodes()[0],t=e.nodeCount();n.setNode(r,{});for(var a,o;Cn(n,e)<t;)a=Rn(n,e),o=n.hasNode(a.v)?P(e,a):-P(e,a),In(n,e,o);return n}function Cn(e,n){function r(t){f(n.nodeEdges(t),function(a){var o=a.v,i=t===o?a.w:o;!e.hasNode(i)&&!P(n,a)&&(e.setNode(i,{}),e.setEdge(t,i,{}),r(i))})}return f(e.nodes(),r),e.nodeCount()}function Rn(e,n){return U(n.edges(),function(r){if(e.hasNode(r.v)!==e.hasNode(r.w))return P(n,r)})}function In(e,n,r){f(e.nodes(),function(t){n.node(t).rank+=r})}function Tn(){}Tn.prototype=new Error;function ye(e,n,r){he(n)||(n=[n]);var t=(e.isDirected()?e.successors:e.neighbors).bind(e),a=[],o={};return f(n,function(i){if(!e.hasNode(i))throw new Error("Graph does not have node: "+i);ke(e,i,r==="post",o,t,a)}),a}function ke(e,n,r,t,a,o){Object.prototype.hasOwnProperty.call(t,n)||(t[n]=!0,r||o.push(n),f(a(n),function(i){ke(e,i,r,t,a,o)}),r&&o.push(n))}function Mn(e,n){return ye(e,n,"post")}function Fn(e,n){return ye(e,n,"pre")}k.initLowLimValues=Z;k.initCutValues=J;k.calcCutValue=xe;k.leaveEdge=Oe;k.enterEdge=Ne;k.exchangeEdges=Le;function k(e){e=wn(e),H(e);var n=ge(e);Z(n),J(n,e);for(var r,t;r=Oe(n);)t=Ne(n,e,r),Le(n,e,r,t)}function J(e,n){var r=Mn(e,e.nodes());r=r.slice(0,r.length-1),f(r,function(t){Sn(e,n,t)})}function Sn(e,n,r){var t=e.node(r),a=t.parent;e.edge(r,a).cutvalue=xe(e,n,r)}function xe(e,n,r){var t=e.node(r),a=t.parent,o=!0,i=n.edge(r,a),u=0;return i||(o=!1,i=n.edge(a,r)),u=i.weight,f(n.nodeEdges(r),function(d){var s=d.v===r,c=s?d.w:d.v;if(c!==a){var l=s===o,h=n.edge(d).weight;if(u+=l?h:-h,Vn(e,r,c)){var v=e.edge(r,c).cutvalue;u+=l?-v:v}}}),u}function Z(e,n){arguments.length<2&&(n=e.nodes()[0]),Ee(e,{},1,n)}function Ee(e,n,r,t,a){var o=r,i=e.node(t);return n[t]=!0,f(e.neighbors(t),function(u){Object.prototype.hasOwnProperty.call(n,u)||(r=Ee(e,n,r,u,t))}),i.low=o,i.lim=r++,a?i.parent=a:delete i.parent,r}function Oe(e){return X(e.edges(),function(n){return e.edge(n).cutvalue<0})}function Ne(e,n,r){var t=r.v,a=r.w;n.hasEdge(t,a)||(t=r.w,a=r.v);var o=e.node(t),i=e.node(a),u=o,d=!1;o.lim>i.lim&&(u=i,d=!0);var s=C(n.edges(),function(c){return d===ie(e,e.node(c.v),u)&&d!==ie(e,e.node(c.w),u)});return U(s,function(c){return P(n,c)})}function Le(e,n,r,t){var a=r.v,o=r.w;e.removeEdge(a,o),e.setEdge(t.v,t.w,{}),Z(e),J(e,n),jn(e,n)}function jn(e,n){var r=X(e.nodes(),function(a){return!n.node(a).parent}),t=Fn(e,r);t=t.slice(1),f(t,function(a){var o=e.node(a).parent,i=n.edge(a,o),u=!1;i||(i=n.edge(o,a),u=!0),n.node(a).rank=n.node(o).rank+(u?i.minlen:-i.minlen)})}function Vn(e,n,r){return e.hasEdge(n,r)}function ie(e,n,r){return r.low<=n.lim&&n.lim<=r.lim}function Bn(e){switch(e.graph().ranker){case"network-simplex":oe(e);break;case"tight-tree":Gn(e);break;case"longest-path":An(e);break;default:oe(e)}}var An=H;function Gn(e){H(e),ge(e)}function oe(e){k(e)}function Dn(e){var n=O(e,"root",{},"_root"),r=Yn(e),t=y(x(r))-1,a=2*t+1;e.graph().nestingRoot=n,f(e.edges(),function(i){e.edge(i).minlen*=a});var o=$n(e)+1;f(e.children(),function(i){Pe(e,n,a,o,t,r,i)}),e.graph().nodeRankFactor=a}function Pe(e,n,r,t,a,o,i){var u=e.children(i);if(!u.length){i!==n&&e.setEdge(n,i,{weight:0,minlen:r});return}var d=re(e,"_bt"),s=re(e,"_bb"),c=e.node(i);e.setParent(d,i),c.borderTop=d,e.setParent(s,i),c.borderBottom=s,f(u,function(l){Pe(e,n,r,t,a,o,l);var h=e.node(l),v=h.borderTop?h.borderTop:l,p=h.borderBottom?h.borderBottom:l,m=h.borderTop?t:2*t,N=v!==p?1:a-o[i]+1;e.setEdge(d,v,{weight:m,minlen:N,nestingEdge:!0}),e.setEdge(p,s,{weight:m,minlen:N,nestingEdge:!0})}),e.parent(i)||e.setEdge(n,d,{weight:0,minlen:a+o[i]})}function Yn(e){var n={};function r(t,a){var o=e.children(t);o&&o.length&&f(o,function(i){r(i,a+1)}),n[t]=a}return f(e.children(),function(t){r(t,1)}),n}function $n(e){return F(e.edges(),function(n,r){return n+e.edge(r).weight},0)}function qn(e){var n=e.graph();e.removeNode(n.nestingRoot),delete n.nestingRoot,f(e.edges(),function(r){var t=e.edge(r);t.nestingEdge&&e.removeEdge(r)})}function Wn(e,n,r){var t={},a;f(r,function(o){for(var i=e.parent(o),u,d;i;){if(u=e.parent(i),u?(d=t[u],t[u]=i):(d=a,a=i),d&&d!==i){n.setEdge(d,i);return}i=u}})}function zn(e,n,r){var t=Xn(e),a=new g({compound:!0}).setGraph({root:t}).setDefaultNodeLabel(function(o){return e.node(o)});return f(e.nodes(),function(o){var i=e.node(o),u=e.parent(o);(i.rank===n||i.minRank<=n&&n<=i.maxRank)&&(a.setNode(o),a.setParent(o,u||t),f(e[r](o),function(d){var s=d.v===o?d.w:d.v,c=a.edge(s,o),l=b(c)?0:c.weight;a.setEdge(s,o,{weight:e.edge(d).weight+l})}),Object.prototype.hasOwnProperty.call(i,"minRank")&&a.setNode(o,{borderLeft:i.borderLeft[n],borderRight:i.borderRight[n]}))}),a}function Xn(e){for(var n;e.hasNode(n=z("_root")););return n}function Un(e,n){for(var r=0,t=1;t<n.length;++t)r+=Hn(e,n[t-1],n[t]);return r}function Hn(e,n,r){for(var t=on(r,w(r,function(s,c){return c})),a=_(w(n,function(s){return R(w(e.outEdges(s),function(c){return{pos:t[c.w],weight:e.edge(c).weight}}),"pos")})),o=1;o<r.length;)o<<=1;var i=2*o-1;o-=1;var u=w(new Array(i),function(){return 0}),d=0;return f(a.forEach(function(s){var c=s.pos+o;u[c]+=s.weight;for(var l=0;c>0;)c%2&&(l+=u[c+1]),c=c-1>>1,u[c]+=s.weight;d+=s.weight*l})),d}function Jn(e){var n={},r=C(e.nodes(),function(u){return!e.children(u).length}),t=y(w(r,function(u){return e.node(u).rank})),a=w(E(t+1),function(){return[]});function o(u){if(!pe(n,u)){n[u]=!0;var d=e.node(u);a[d.rank].push(u),f(e.successors(u),o)}}var i=R(r,function(u){return e.node(u).rank});return f(i,o),a}function Zn(e,n){return w(n,function(r){var t=e.inEdges(r);if(t.length){var a=F(t,function(o,i){var u=e.edge(i),d=e.node(i.v);return{sum:o.sum+u.weight*d.order,weight:o.weight+u.weight}},{sum:0,weight:0});return{v:r,barycenter:a.sum/a.weight,weight:a.weight}}else return{v:r}})}function Kn(e,n){var r={};f(e,function(a,o){var i=r[a.v]={indegree:0,in:[],out:[],vs:[a.v],i:o};b(a.barycenter)||(i.barycenter=a.barycenter,i.weight=a.weight)}),f(n.edges(),function(a){var o=r[a.v],i=r[a.w];!b(o)&&!b(i)&&(i.indegree++,o.out.push(r[a.w]))});var t=C(r,function(a){return!a.indegree});return Qn(t)}function Qn(e){var n=[];function r(o){return function(i){i.merged||(b(i.barycenter)||b(o.barycenter)||i.barycenter>=o.barycenter)&&er(o,i)}}function t(o){return function(i){i.in.push(o),--i.indegree===0&&e.push(i)}}for(;e.length;){var a=e.pop();n.push(a),f(a.in.reverse(),r(a)),f(a.out,t(a))}return w(C(n,function(o){return!o.merged}),function(o){return T(o,["vs","i","barycenter","weight"])})}function er(e,n){var r=0,t=0;e.weight&&(r+=e.barycenter*e.weight,t+=e.weight),n.weight&&(r+=n.barycenter*n.weight,t+=n.weight),e.vs=n.vs.concat(e.vs),e.barycenter=r/t,e.weight=t,e.i=Math.min(n.i,e.i),n.merged=!0}function nr(e,n){var r=gn(e,function(c){return Object.prototype.hasOwnProperty.call(c,"barycenter")}),t=r.lhs,a=R(r.rhs,function(c){return-c.i}),o=[],i=0,u=0,d=0;t.sort(rr(!!n)),d=ue(o,a,d),f(t,function(c){d+=c.vs.length,o.push(c.vs),i+=c.barycenter*c.weight,u+=c.weight,d=ue(o,a,d)});var s={vs:_(o)};return u&&(s.barycenter=i/u,s.weight=u),s}function ue(e,n,r){for(var t;n.length&&(t=I(n)).i<=r;)n.pop(),e.push(t.vs),r++;return r}function rr(e){return function(n,r){return n.barycenter<r.barycenter?-1:n.barycenter>r.barycenter?1:e?r.i-n.i:n.i-r.i}}function _e(e,n,r,t){var a=e.children(n),o=e.node(n),i=o?o.borderLeft:void 0,u=o?o.borderRight:void 0,d={};i&&(a=C(a,function(p){return p!==i&&p!==u}));var s=Zn(e,a);f(s,function(p){if(e.children(p.v).length){var m=_e(e,p.v,r,t);d[p.v]=m,Object.prototype.hasOwnProperty.call(m,"barycenter")&&ar(p,m)}});var c=Kn(s,r);tr(c,d);var l=nr(c,t);if(i&&(l.vs=_([i,l.vs,u]),e.predecessors(i).length)){var h=e.node(e.predecessors(i)[0]),v=e.node(e.predecessors(u)[0]);Object.prototype.hasOwnProperty.call(l,"barycenter")||(l.barycenter=0,l.weight=0),l.barycenter=(l.barycenter*l.weight+h.order+v.order)/(l.weight+2),l.weight+=2}return l}function tr(e,n){f(e,function(r){r.vs=_(r.vs.map(function(t){return n[t]?n[t].vs:t}))})}function ar(e,n){b(e.barycenter)?(e.barycenter=n.barycenter,e.weight=n.weight):(e.barycenter=(e.barycenter*e.weight+n.barycenter*n.weight)/(e.weight+n.weight),e.weight+=n.weight)}function ir(e){var n=me(e),r=de(e,E(1,n+1),"inEdges"),t=de(e,E(n-1,-1,-1),"outEdges"),a=Jn(e);se(e,a);for(var o=Number.POSITIVE_INFINITY,i,u=0,d=0;d<4;++u,++d){or(u%2?r:t,u%4>=2),a=S(e);var s=Un(e,a);s<o&&(d=0,i=$e(a),o=s)}se(e,i)}function de(e,n,r){return w(n,function(t){return zn(e,t,r)})}function or(e,n){var r=new g;f(e,function(t){var a=t.graph().root,o=_e(t,a,r,n);f(o.vs,function(i,u){t.node(i).order=u}),Wn(t,r,o.vs)})}function se(e,n){f(n,function(r){f(r,function(t,a){e.node(t).order=a})})}function ur(e){var n=sr(e);f(e.graph().dummyChains,function(r){for(var t=e.node(r),a=t.edgeObj,o=dr(e,n,a.v,a.w),i=o.path,u=o.lca,d=0,s=i[d],c=!0;r!==a.w;){if(t=e.node(r),c){for(;(s=i[d])!==u&&e.node(s).maxRank<t.rank;)d++;s===u&&(c=!1)}if(!c){for(;d<i.length-1&&e.node(s=i[d+1]).minRank<=t.rank;)d++;s=i[d]}e.setParent(r,s),r=e.successors(r)[0]}})}function dr(e,n,r,t){var a=[],o=[],i=Math.min(n[r].low,n[t].low),u=Math.max(n[r].lim,n[t].lim),d,s;d=r;do d=e.parent(d),a.push(d);while(d&&(n[d].low>i||u>n[d].lim));for(s=d,d=t;(d=e.parent(d))!==s;)o.push(d);return{path:a.concat(o.reverse()),lca:s}}function sr(e){var n={},r=0;function t(a){var o=r;f(e.children(a),t),n[a]={low:o,lim:r++}}return f(e.children(),t),n}function fr(e,n){var r={};function t(a,o){var i=0,u=0,d=a.length,s=I(o);return f(o,function(c,l){var h=lr(e,c),v=h?e.node(h).order:d;(h||c===s)&&(f(o.slice(u,l+1),function(p){f(e.predecessors(p),function(m){var N=e.node(m),K=N.order;(K<i||v<K)&&!(N.dummy&&e.node(p).dummy)&&Ce(r,m,p)})}),u=l+1,i=v)}),o}return F(n,t),r}function cr(e,n){var r={};function t(o,i,u,d,s){var c;f(E(i,u),function(l){c=o[l],e.node(c).dummy&&f(e.predecessors(c),function(h){var v=e.node(h);v.dummy&&(v.order<d||v.order>s)&&Ce(r,h,c)})})}function a(o,i){var u=-1,d,s=0;return f(i,function(c,l){if(e.node(c).dummy==="border"){var h=e.predecessors(c);h.length&&(d=e.node(h[0]).order,t(i,s,l,u,d),s=l,u=d)}t(i,s,i.length,d,o.length)}),i}return F(n,a),r}function lr(e,n){if(e.node(n).dummy)return X(e.predecessors(n),function(r){return e.node(r).dummy})}function Ce(e,n,r){if(n>r){var t=n;n=r,r=t}var a=e[n];a||(e[n]=a={}),a[r]=!0}function hr(e,n,r){if(n>r){var t=n;n=r,r=t}return!!e[n]&&Object.prototype.hasOwnProperty.call(e[n],r)}function vr(e,n,r,t){var a={},o={},i={};return f(n,function(u){f(u,function(d,s){a[d]=d,o[d]=d,i[d]=s})}),f(n,function(u){var d=-1;f(u,function(s){var c=t(s);if(c.length){c=R(c,function(m){return i[m]});for(var l=(c.length-1)/2,h=Math.floor(l),v=Math.ceil(l);h<=v;++h){var p=c[h];o[s]===s&&d<i[p]&&!hr(r,s,p)&&(o[p]=s,o[s]=a[s]=a[p],d=i[p])}}})}),{root:a,align:o}}function pr(e,n,r,t,a){var o={},i=wr(e,n,r,a),u=a?"borderLeft":"borderRight";function d(l,h){for(var v=i.nodes(),p=v.pop(),m={};p;)m[p]?l(p):(m[p]=!0,v.push(p),v=v.concat(h(p))),p=v.pop()}function s(l){o[l]=i.inEdges(l).reduce(function(h,v){return Math.max(h,o[v.v]+i.edge(v))},0)}function c(l){var h=i.outEdges(l).reduce(function(p,m){return Math.min(p,o[m.w]-i.edge(m))},Number.POSITIVE_INFINITY),v=e.node(l);h!==Number.POSITIVE_INFINITY&&v.borderType!==u&&(o[l]=Math.max(o[l],h))}return d(s,i.predecessors.bind(i)),d(c,i.successors.bind(i)),f(t,function(l){o[l]=o[r[l]]}),o}function wr(e,n,r,t){var a=new g,o=e.graph(),i=kr(o.nodesep,o.edgesep,t);return f(n,function(u){var d;f(u,function(s){var c=r[s];if(a.setNode(c),d){var l=r[d],h=a.edge(l,c);a.setEdge(l,c,Math.max(i(e,s,d),h||0))}d=s})}),a}function mr(e,n){return U(x(n),function(r){var t=Number.NEGATIVE_INFINITY,a=Number.POSITIVE_INFINITY;return ze(r,function(o,i){var u=xr(e,i)/2;t=Math.max(o+u,t),a=Math.min(o-u,a)}),t-a})}function br(e,n){var r=x(n),t=L(r),a=y(r);f(["u","d"],function(o){f(["l","r"],function(i){var u=o+i,d=e[u],s;if(d!==n){var c=x(d);s=i==="l"?t-L(c):a-y(c),s&&(e[u]=M(d,function(l){return l+s}))}})})}function gr(e,n){return M(e.ul,function(r,t){if(n)return e[n.toLowerCase()][t];var a=R(w(e,t));return(a[1]+a[2])/2})}function yr(e){var n=S(e),r=q(fr(e,n),cr(e,n)),t={},a;f(["u","d"],function(i){a=i==="u"?n:x(n).reverse(),f(["l","r"],function(u){u==="r"&&(a=w(a,function(l){return x(l).reverse()}));var d=(i==="u"?e.predecessors:e.successors).bind(e),s=vr(e,a,r,d),c=pr(e,a,s.root,s.align,u==="r");u==="r"&&(c=M(c,function(l){return-l})),t[i+u]=c})});var o=mr(e,t);return br(t,o),gr(t,e.graph().align)}function kr(e,n,r){return function(t,a,o){var i=t.node(a),u=t.node(o),d=0,s;if(d+=i.width/2,Object.prototype.hasOwnProperty.call(i,"labelpos"))switch(i.labelpos.toLowerCase()){case"l":s=-i.width/2;break;case"r":s=i.width/2;break}if(s&&(d+=r?s:-s),s=0,d+=(i.dummy?n:e)/2,d+=(u.dummy?n:e)/2,d+=u.width/2,Object.prototype.hasOwnProperty.call(u,"labelpos"))switch(u.labelpos.toLowerCase()){case"l":s=u.width/2;break;case"r":s=-u.width/2;break}return s&&(d+=r?s:-s),s=0,d}}function xr(e,n){return e.node(n).width}function Er(e){e=we(e),Or(e),Xe(yr(e),function(n,r){e.node(r).x=n})}function Or(e){var n=S(e),r=e.graph().ranksep,t=0;f(n,function(a){var o=y(w(a,function(i){return e.node(i).height}));f(a,function(i){e.node(i).y=t+o/2}),t+=o+r})}function at(e,n){var r=yn;r("layout",()=>{var t=r("  buildLayoutGraph",()=>Sr(e));r("  runLayout",()=>Nr(t,r)),r("  updateInputGraph",()=>Lr(e,t))})}function Nr(e,n){n("    makeSpaceForEdgeLabels",()=>jr(e)),n("    removeSelfEdges",()=>Wr(e)),n("    acyclic",()=>hn(e)),n("    nestingGraph.run",()=>Dn(e)),n("    rank",()=>Bn(we(e))),n("    injectEdgeLabelProxies",()=>Vr(e)),n("    removeEmptyRanks",()=>bn(e)),n("    nestingGraph.cleanup",()=>qn(e)),n("    normalizeRanks",()=>mn(e)),n("    assignRankMinMax",()=>Br(e)),n("    removeEdgeLabelProxies",()=>Ar(e)),n("    normalize.run",()=>Ln(e)),n("    parentDummyChains",()=>ur(e)),n("    addBorderSegments",()=>kn(e)),n("    order",()=>ir(e)),n("    insertSelfEdges",()=>zr(e)),n("    adjustCoordinateSystem",()=>xn(e)),n("    position",()=>Er(e)),n("    positionSelfEdges",()=>Xr(e)),n("    removeBorderNodes",()=>qr(e)),n("    normalize.undo",()=>_n(e)),n("    fixupEdgeLabelCoords",()=>Yr(e)),n("    undoCoordinateSystem",()=>En(e)),n("    translateGraph",()=>Gr(e)),n("    assignNodeIntersects",()=>Dr(e)),n("    reversePoints",()=>$r(e)),n("    acyclic.undo",()=>pn(e))}function Lr(e,n){f(e.nodes(),function(r){var t=e.node(r),a=n.node(r);t&&(t.x=a.x,t.y=a.y,n.children(r).length&&(t.width=a.width,t.height=a.height))}),f(e.edges(),function(r){var t=e.edge(r),a=n.edge(r);t.points=a.points,Object.prototype.hasOwnProperty.call(a,"x")&&(t.x=a.x,t.y=a.y)}),e.graph().width=n.graph().width,e.graph().height=n.graph().height}var Pr=["nodesep","edgesep","ranksep","marginx","marginy"],_r={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},Cr=["acyclicer","ranker","rankdir","align"],Rr=["width","height"],Ir={width:0,height:0},Tr=["minlen","weight","width","height","labeloffset"],Mr={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},Fr=["labelpos"];function Sr(e){var n=new g({multigraph:!0,compound:!0}),r=Y(e.graph());return n.setGraph(q({},_r,D(r,Pr),T(r,Cr))),f(e.nodes(),function(t){var a=Y(e.node(t));n.setNode(t,Ge(D(a,Rr),Ir)),n.setParent(t,e.parent(t))}),f(e.edges(),function(t){var a=Y(e.edge(t));n.setEdge(t,q({},Mr,D(a,Tr),T(a,Fr)))}),n}function jr(e){var n=e.graph();n.ranksep/=2,f(e.edges(),function(r){var t=e.edge(r);t.minlen*=2,t.labelpos.toLowerCase()!=="c"&&(n.rankdir==="TB"||n.rankdir==="BT"?t.width+=t.labeloffset:t.height+=t.labeloffset)})}function Vr(e){f(e.edges(),function(n){var r=e.edge(n);if(r.width&&r.height){var t=e.node(n.v),a=e.node(n.w),o={rank:(a.rank-t.rank)/2+t.rank,e:n};O(e,"edge-proxy",o,"_ep")}})}function Br(e){var n=0;f(e.nodes(),function(r){var t=e.node(r);t.borderTop&&(t.minRank=e.node(t.borderTop).rank,t.maxRank=e.node(t.borderBottom).rank,n=y(n,t.maxRank))}),e.graph().maxRank=n}function Ar(e){f(e.nodes(),function(n){var r=e.node(n);r.dummy==="edge-proxy"&&(e.edge(r.e).labelRank=r.rank,e.removeNode(n))})}function Gr(e){var n=Number.POSITIVE_INFINITY,r=0,t=Number.POSITIVE_INFINITY,a=0,o=e.graph(),i=o.marginx||0,u=o.marginy||0;function d(s){var c=s.x,l=s.y,h=s.width,v=s.height;n=Math.min(n,c-h/2),r=Math.max(r,c+h/2),t=Math.min(t,l-v/2),a=Math.max(a,l+v/2)}f(e.nodes(),function(s){d(e.node(s))}),f(e.edges(),function(s){var c=e.edge(s);Object.prototype.hasOwnProperty.call(c,"x")&&d(c)}),n-=i,t-=u,f(e.nodes(),function(s){var c=e.node(s);c.x-=n,c.y-=t}),f(e.edges(),function(s){var c=e.edge(s);f(c.points,function(l){l.x-=n,l.y-=t}),Object.prototype.hasOwnProperty.call(c,"x")&&(c.x-=n),Object.prototype.hasOwnProperty.call(c,"y")&&(c.y-=t)}),o.width=r-n+i,o.height=a-t+u}function Dr(e){f(e.edges(),function(n){var r=e.edge(n),t=e.node(n.v),a=e.node(n.w),o,i;r.points?(o=r.points[0],i=r.points[r.points.length-1]):(r.points=[],o=a,i=t),r.points.unshift(ne(t,o)),r.points.push(ne(a,i))})}function Yr(e){f(e.edges(),function(n){var r=e.edge(n);if(Object.prototype.hasOwnProperty.call(r,"x"))switch((r.labelpos==="l"||r.labelpos==="r")&&(r.width-=r.labeloffset),r.labelpos){case"l":r.x-=r.width/2+r.labeloffset;break;case"r":r.x+=r.width/2+r.labeloffset;break}})}function $r(e){f(e.edges(),function(n){var r=e.edge(n);r.reversed&&r.points.reverse()})}function qr(e){f(e.nodes(),function(n){if(e.children(n).length){var r=e.node(n),t=e.node(r.borderTop),a=e.node(r.borderBottom),o=e.node(I(r.borderLeft)),i=e.node(I(r.borderRight));r.width=Math.abs(i.x-o.x),r.height=Math.abs(a.y-t.y),r.x=o.x+r.width/2,r.y=t.y+r.height/2}}),f(e.nodes(),function(n){e.node(n).dummy==="border"&&e.removeNode(n)})}function Wr(e){f(e.edges(),function(n){if(n.v===n.w){var r=e.node(n.v);r.selfEdges||(r.selfEdges=[]),r.selfEdges.push({e:n,label:e.edge(n)}),e.removeEdge(n)}})}function zr(e){var n=S(e);f(n,function(r){var t=0;f(r,function(a,o){var i=e.node(a);i.order=o+t,f(i.selfEdges,function(u){O(e,"selfedge",{width:u.label.width,height:u.label.height,rank:i.rank,order:o+ ++t,e:u.e,label:u.label},"_se")}),delete i.selfEdges})})}function Xr(e){f(e.nodes(),function(n){var r=e.node(n);if(r.dummy==="selfedge"){var t=e.node(r.e.v),a=t.x+t.width/2,o=t.y,i=r.x-a,u=t.height/2;e.setEdge(r.e,r.label),e.removeNode(n),r.label.points=[{x:a+2*i/3,y:o-u},{x:a+5*i/6,y:o-u},{x:a+i,y:o},{x:a+5*i/6,y:o+u},{x:a+2*i/3,y:o+u}],r.label.x=r.x,r.label.y=r.y}})}function D(e,n){return M(T(e,n),Number)}function Y(e){var n={};return f(e,function(r,t){n[t.toLowerCase()]=r}),n}export{at as l};
//# sourceMappingURL=layout-C-6EmolC.js.map
