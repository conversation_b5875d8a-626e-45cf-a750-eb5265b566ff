[{"ua": "Mozilla/5.0 (Linux; Android 6.0; Lenovo K50a40 Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.2987.137 YaBrowser/17.4.1.352.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "17.4.1.352.00", "major": "17"}, "cpu": {}, "device": {"type": "mobile", "model": "K50a40", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "57.0.2987.137"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; X7pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.119 YaBrowser/19.3.1.327.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "19.3.1.327.00", "major": "19"}, "cpu": {}, "device": {"type": "mobile", "model": "X7pro"}, "engine": {"name": "Blink", "version": "72.0.3626.119"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; X7pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.119 YaBrowser/19.3.2.296.00 (beta) Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "19.3.2.296.00", "major": "19"}, "cpu": {}, "device": {"type": "mobile", "model": "X7pro"}, "engine": {"name": "Blink", "version": "72.0.3626.119"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; A574BL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.119 YaBrowser/19.3.3.132.00 (alpha) Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "19.3.3.132.00", "major": "19"}, "cpu": {}, "device": {"type": "tablet", "model": "A574", "vendor": "Acer"}, "engine": {"name": "Blink", "version": "72.0.3626.119"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SNE-LX3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 YaBrowser/19.4.0.535.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "19.4.0.535.00", "major": "19"}, "cpu": {}, "device": {"type": "mobile", "model": "SNE-LX3", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "73.0.3683.103"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0.1; ZTE BLADE A5 PRO) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 YaBrowser/19.4.0.535.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "19.4.0.535.00", "major": "19"}, "cpu": {}, "device": {"type": "mobile", "model": "BLADE A5 PRO", "vendor": "ZTE"}, "engine": {"name": "Blink", "version": "73.0.3683.103"}, "os": {"name": "Android", "version": "5.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; HUAWEI VNS-L23) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 YaBrowser/19.4.0.535.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "19.4.0.535.00", "major": "19"}, "cpu": {}, "device": {"type": "mobile", "model": " VNS-L23", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "73.0.3683.103"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SNE-LX3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 YaBrowser/19.4.0.535.00 Tablet Safari/537.36", "browser": {"name": "Yandex", "version": "19.4.0.535.00", "major": "19"}, "cpu": {}, "device": {"type": "mobile", "model": "SNE-LX3", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "73.0.3683.103"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0.1; ZTE BLADE A5 PRO) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 YaBrowser/19.4.0.535.00 Tablet Safari/537.36", "browser": {"name": "Yandex", "version": "19.4.0.535.00", "major": "19"}, "cpu": {}, "device": {"type": "mobile", "model": "BLADE A5 PRO", "vendor": "ZTE"}, "engine": {"name": "Blink", "version": "73.0.3683.103"}, "os": {"name": "Android", "version": "5.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; HUAWEI VNS-L23) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 YaBrowser/19.4.0.535.00 Tablet Safari/537.36", "browser": {"name": "Yandex", "version": "19.4.0.535.00", "major": "19"}, "cpu": {}, "device": {"type": "mobile", "model": " VNS-L23", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "73.0.3683.103"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; LG-TP260) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 YaBrowser/19.4.4.317.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "19.4.4.317.00", "major": "19"}, "cpu": {}, "device": {"type": "mobile", "model": "TP260", "vendor": "LG"}, "engine": {"name": "Blink", "version": "73.0.3683.103"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Lenovo K8 Note) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 YaBrowser/19.4.5.141.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "19.4.5.141.00", "major": "19"}, "cpu": {}, "device": {"type": "mobile", "model": "K8", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "73.0.3683.103"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 8.0.0; SM-N950U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 YaBrowser/20.2.2.127.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.2.127.00", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "SM-N950U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.117"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 10; SNE-LX3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 YaBrowser/20.2.2.127.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.2.127.00", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "SNE-LX3", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "79.0.3945.117"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; arm_64; RMX1971) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 YaBrowser/20.7.0.101.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "20.7.0.101.00", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "RMX1971", "vendor": "Realme"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; arm_64; MI 6X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 YaBrowser/20.7.1.60.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "20.7.1.60.00", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "MI 6X", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 8.1.0; SM-G610M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 YaBrowser/20.11.3.88.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "20.11.3.88.00", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "SM-G610M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; arm_64; RMX1971) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 YaBrowser/20.7.0.101.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "20.7.0.101.00", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "RMX1971", "vendor": "Realme"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; vivo X9Plus) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 YaBrowser/19.3.3.285.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "19.3.3.285.00", "major": "19"}, "cpu": {}, "device": {"type": "mobile", "model": "X9Plus", "vendor": "Vivo"}, "engine": {"name": "Blink", "version": "72.0.3626.121"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 6.0; PLK-TL01H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.182 YaBrowser/21.2.5.131.00 (beta) SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "21.2.5.131.00", "major": "21"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "PLK-TL01H"}, "engine": {"name": "Blink", "version": "88.0.4324.182"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; x86; Android 7.1.2; SM-G965F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.182 YaBrowser/21.2.4.139.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "21.2.4.139.00", "major": "21"}, "cpu": {"architecture": "ia32"}, "device": {"type": "mobile", "model": "SM-G965F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.182"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T585 Build/M1AJQ; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.101 YaBrowser/18.3.1.78 (lite) Safari/537.36", "browser": {"name": "Yandex", "version": "18.3.1.78", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T585", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 11; SM-G965F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 YaBrowser/20.11.5.113 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "20.11.5.113", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "SM-G965F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "90.0.4430.91"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.2.2; Tesla 7.0 3G Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.95 YaBrowser/17.1.0.412.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.1.0.412.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla 7.0 3G"}, "engine": {"name": "Blink", "version": "55.0.2883.95"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.2.2; Tesla 7.0 3G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 YaBrowser/18.11.1.1011.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.11.1.1011.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla 7.0 3G"}, "engine": {"name": "Blink", "version": "70.0.3538.102"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.2.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 YaBrowser/18.11.1.1011.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.11.1.1011.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "70.0.3538.102"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.2454.101 YaBrowser/15.10.2454.3845.01 Yowser/2.5.2 Safari/537.36", "browser": {"name": "Yandex", "version": "15.10.2454.3845.01", "major": "15"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "45.0.2454.101"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.86 YaBrowser/15.12.1.6403.01 Yowser/2.5.2 Safari/537.36", "browser": {"name": "Yandex", "version": "15.12.1.6403.01", "major": "15"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "46.0.2490.86"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.86 YaBrowser/15.12.2.6773.01 Yowser/2.5.2 Safari/537.36", "browser": {"name": "Yandex", "version": "15.12.2.6773.01", "major": "15"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "46.0.2490.86"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.110 YaBrowser/16.4.0.9404.01 Safari/537.36", "browser": {"name": "Yandex", "version": "16.4.0.9404.01", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "49.0.2623.110"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.106 YaBrowser/16.7.1.2925.01 Safari/537.36", "browser": {"name": "Yandex", "version": "16.7.1.2925.01", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "51.0.2704.106"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 YaBrowser/16.9.1.1616.01 Safari/537.36", "browser": {"name": "Yandex", "version": "16.9.1.1616.01", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "52.0.2743.116"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.116 YaBrowser/16.10.1.1443.01 Safari/537.36", "browser": {"name": "Yandex", "version": "16.10.1.1443.01", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "53.0.2785.116"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.90 YaBrowser/16.11.0.649.01 Safari/537.36", "browser": {"name": "Yandex", "version": "16.11.0.649.01", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "54.0.2840.90"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.95 YaBrowser/17.1.0.412.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.1.0.412.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "55.0.2883.95"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 YaBrowser/17.3.0.373.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.3.0.373.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "56.0.2924.87"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 YaBrowser/17.3.1.383.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.3.1.383.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "56.0.2924.87"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 YaBrowser/17.3.2.414.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.3.2.414.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "56.0.2924.87"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 YaBrowser/17.6.1.345.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.6.1.345.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "58.0.3029.110"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 YaBrowser/17.10.0.446.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.10.0.446.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "61.0.3163.100"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 YaBrowser/17.10.1.370.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.10.1.370.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "61.0.3163.100"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 YaBrowser/17.11.1.628.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.11.1.628.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "62.0.3202.94"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 YaBrowser/18.1.1.642.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.1.1.642.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "63.0.3239.132"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 YaBrowser/18.1.1.645.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.1.1.645.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "63.0.3239.132"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 YaBrowser/18.1.2.70.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.1.2.70.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "63.0.3239.132"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.186 YaBrowser/18.3.1.651.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.3.1.651.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "64.0.3282.186"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 YaBrowser/18.4.1.529.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.4.1.529.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "65.0.3325.181"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.181 YaBrowser/18.6.0.683.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.6.0.683.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "66.0.3359.181"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.103 YaBrowser/18.7.0.823.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.7.0.823.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "67.0.3396.103"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.106 YaBrowser/18.9.0.466.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.9.0.466.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "68.0.3440.106"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 YaBrowser/19.6.0.158 (lite) Safari/537.36", "browser": {"name": "Yandex", "version": "19.6.0.158", "major": "19"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 YaBrowser/19.6.0.179 (lite) Safari/537.36", "browser": {"name": "Yandex", "version": "19.6.0.179", "major": "19"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 YaBrowser/18.11.0.1462.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.11.0.1462.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "70.0.3538.102"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 YaBrowser/18.11.1.822.01 (alpha) Safari/537.36", "browser": {"name": "Yandex", "version": "18.11.1.822.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "70.0.3538.102"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 YaBrowser/18.11.1.1011.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.11.1.1011.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "70.0.3538.102"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 YaBrowser/18.11.1.1011.01 Safari/E7FBAF", "browser": {"name": "Yandex", "version": "18.11.1.1011.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "70.0.3538.102"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/8.70/apad YaSearchBrowser/8.70", "browser": {"name": "Yandex", "version": "8.70", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/9.05/apad YaSearchBrowser/9.05", "browser": {"name": "Yandex", "version": "9.05", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/9.20/apad YaSearchBrowser/9.20", "browser": {"name": "Yandex", "version": "9.20", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/9.35/apad YaSearchBrowser/9.35", "browser": {"name": "Yandex", "version": "9.35", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/9.50/apad YaSearchBrowser/9.50", "browser": {"name": "Yandex", "version": "9.50", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/9.65/apad YaSearchBrowser/9.65", "browser": {"name": "Yandex", "version": "9.65", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/9.75/apad YaSearchBrowser/9.75", "browser": {"name": "Yandex", "version": "9.75", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/9.80/apad YaSearchBrowser/9.80", "browser": {"name": "Yandex", "version": "9.80", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/9.85/apad YaSearchBrowser/9.85", "browser": {"name": "Yandex", "version": "9.85", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/10.10/apad YaSearchBrowser/10.10", "browser": {"name": "Yandex", "version": "10.10", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/10.30/apad YaSearchBrowser/10.30", "browser": {"name": "Yandex", "version": "10.30", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/10.41/apad YaSearchBrowser/10.41", "browser": {"name": "Yandex", "version": "10.41", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/10.61/apad YaSearchBrowser/10.61", "browser": {"name": "Yandex", "version": "10.61", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/10.70/apad YaSearchBrowser/10.70", "browser": {"name": "Yandex", "version": "10.70", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/11.10/apad YaSearchBrowser/11.10", "browser": {"name": "Yandex", "version": "11.10", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/11.20/apad YaSearchBrowser/11.20", "browser": {"name": "Yandex", "version": "11.20", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.2454.101 YaBrowser/15.10.2454.3845.01 Yowser/2.5.2 Safari/537.36", "browser": {"name": "Yandex", "version": "15.10.2454.3845.01", "major": "15"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "45.0.2454.101"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.86 YaBrowser/15.12.1.6403.01 Yowser/2.5.2 Safari/537.36", "browser": {"name": "Yandex", "version": "15.12.1.6403.01", "major": "15"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "46.0.2490.86"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.106 YaBrowser/16.7.0.2531.01 Safari/537.36", "browser": {"name": "Yandex", "version": "16.7.0.2531.01", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "51.0.2704.106"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.106 YaBrowser/16.7.1.2912.01 Safari/537.36", "browser": {"name": "Yandex", "version": "16.7.1.2912.01", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "51.0.2704.106"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 YaBrowser/16.9.1.1616.01 Safari/537.36", "browser": {"name": "Yandex", "version": "16.9.1.1616.01", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "52.0.2743.116"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.116 YaBrowser/16.10.0.1326.01 Safari/537.36", "browser": {"name": "Yandex", "version": "16.10.0.1326.01", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "53.0.2785.116"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.95 YaBrowser/17.1.1.359.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.1.1.359.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "55.0.2883.95"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 YaBrowser/17.6.1.345.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.6.1.345.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "58.0.3029.110"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 YaBrowser/17.9.0.523.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.9.0.523.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "60.0.3112.113"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 YaBrowser/17.10.2.145.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.10.2.145.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "61.0.3163.100"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 YaBrowser/17.11.1.628.01 Safari/537.36", "browser": {"name": "Yandex", "version": "17.11.1.628.01", "major": "17"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "62.0.3202.94"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 YaBrowser/18.1.1.642.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.1.1.642.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "63.0.3239.132"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.181 YaBrowser/18.6.0.683.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.6.0.683.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "66.0.3359.181"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47I) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Mobile Safari/537.36 YaApp_Android/9.50/apad YaSearchBrowser/9.50", "browser": {"name": "Yandex", "version": "9.50", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47I) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.136 Mobile Safari/537.36 YaApp_Android/10.20/apad YaSearchBrowser/10.20", "browser": {"name": "Yandex", "version": "10.20", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.169 YaBrowser/19.6.4.349.01 Safari/537.36", "browser": {"name": "Yandex", "version": "19.6.4.349.01", "major": "19"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "74.0.3729.169"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; arm; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 YaBrowser/20.7.0.101.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.7.0.101.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; arm; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 YaBrowser/20.7.1.60.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.7.1.60.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; arm; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 YaBrowser/20.7.2.70.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.7.2.70.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; arm; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 YaBrowser/20.7.4.108.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.7.4.108.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; arm; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 YaBrowser/20.7.5.84.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.7.5.84.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; arm_64; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 YaBrowser/20.7.3.99.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.7.3.99.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Mobile Safari/537.36 YaApp_Android/9.50 YaSearchBrowser/9.50", "browser": {"name": "Yandex", "version": "9.50", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.93 Mobile Safari/537.36 YaApp_Android/9.75 YaSearchBrowser/9.75", "browser": {"name": "Yandex", "version": "9.75", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.136 Mobile Safari/537.36 YaApp_Android/9.99/apad YaSearchBrowser/9.99", "browser": {"name": "Yandex", "version": "9.99", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.136 Mobile Safari/537.36 YaApp_Android/10.20 YaSearchBrowser/10.20", "browser": {"name": "Yandex", "version": "10.20", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36 YaApp_Android/10.44/apad YaSearchBrowser/10.44", "browser": {"name": "Yandex", "version": "10.44", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.162"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Mobile Safari/537.36 YaApp_Android/10.61 YaSearchBrowser/10.61", "browser": {"name": "Yandex", "version": "10.61", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36 YaApp_Android/10.91 YaSearchBrowser/10.91", "browser": {"name": "Yandex", "version": "10.91", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 YaBrowser/19.4.0.535.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "19.4.0.535.00", "major": "19"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "73.0.3683.103"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 YaBrowser/18.11.1.979.01 Safari/537.36", "browser": {"name": "Yandex", "version": "18.11.1.979.01", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "70.0.3538.102"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; arm_64; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 YaBrowser/20.7.1.60.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.7.1.60.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; arm_64; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 YaBrowser/20.7.3.99.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.7.3.99.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.132 YaBrowser/19.9.4.104.01 Safari/537.36", "browser": {"name": "Yandex", "version": "19.9.4.104.01", "major": "19"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.92 YaBrowser/19.10.1.81.01 Safari/537.36", "browser": {"name": "Yandex", "version": "19.10.1.81.01", "major": "19"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "77.0.3865.92"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.120 YaBrowser/19.10.2.116.01 Safari/537.36", "browser": {"name": "Yandex", "version": "19.10.2.116.01", "major": "19"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "77.0.3865.120"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.120 YaBrowser/19.10.3.139.01 Safari/537.36", "browser": {"name": "Yandex", "version": "19.10.3.139.01", "major": "19"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "77.0.3865.120"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 YaBrowser/19.12.0.250.01 Safari/537.36", "browser": {"name": "Yandex", "version": "19.12.0.250.01", "major": "19"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "78.0.3904.97"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 YaBrowser/19.12.1.121.01 Safari/537.36", "browser": {"name": "Yandex", "version": "19.12.1.121.01", "major": "19"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "78.0.3904.97"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 YaBrowser/19.12.3.101.01 Safari/537.36", "browser": {"name": "Yandex", "version": "19.12.3.101.01", "major": "19"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 YaBrowser/19.12.4.87.01 Safari/537.36", "browser": {"name": "Yandex", "version": "19.12.4.87.01", "major": "19"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 YaBrowser/20.2.0.215.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.0.215.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.117"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 YaBrowser/20.2.1.120.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.1.120.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.117"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 YaBrowser/20.2.1.120.01 Yptp/1.62 Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.1.120.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.117"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 YaBrowser/20.2.2.127.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.2.127.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.117"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 YaBrowser/20.2.2.127.01 Yptp/1.62 Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.2.127.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.117"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.136 YaBrowser/20.2.4.153.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.4.153.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.136 YaBrowser/20.2.6.114.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.6.114.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.122 YaBrowser/20.3.0.276.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.3.0.276.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.122"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 YaBrowser/20.3.2.107.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.3.2.107.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 YaBrowser/20.3.3.92.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.3.3.92.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 YaBrowser/20.3.4.98.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.3.4.98.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 YaBrowser/20.3.5.90.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.3.5.90.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.96 YaBrowser/20.4.0.237.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.4.0.237.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.96"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.96 YaBrowser/20.4.1.144.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.4.1.144.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.96"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 YaBrowser/20.4.2.101.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.4.2.101.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 YaBrowser/20.4.3.90.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.4.3.90.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 YaBrowser/20.4.5.63.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.4.5.63.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 YaBrowser/20.6.0.182.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.6.0.182.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 YaBrowser/20.6.1.73.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.6.1.73.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 YaBrowser/20.6.2.104.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.6.2.104.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 YaBrowser/20.6.3.54.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.6.3.54.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.135 YaBrowser/20.8.0.174.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.8.0.174.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "84.0.4147.135"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.135 YaBrowser/20.8.1.71.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.8.1.71.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "84.0.4147.135"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.135 YaBrowser/20.8.2.90.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.8.2.90.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "84.0.4147.135"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.135 YaBrowser/20.8.5.97.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.8.5.97.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "84.0.4147.135"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.121 YaBrowser/20.9.0.159.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.9.0.159.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "85.0.4183.121"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 YaBrowser/20.9.1.66.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.9.1.66.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 YaBrowser/20.9.2.95.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.9.2.95.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 YaBrowser/20.9.3.85.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.9.3.85.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 YaBrowser/20.9.4.99.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.9.4.99.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 YaBrowser/20.11.1.88.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.11.1.88.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 YaBrowser/20.11.2.69.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.11.2.69.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 YaBrowser/20.11.3.88.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.11.3.88.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 YaBrowser/20.11.5.124.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.11.5.124.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 YaBrowser/20.12.3.116.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.12.3.116.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 YaBrowser/20.12.4.100.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.12.4.100.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.182 YaBrowser/21.2.0.223.01 Safari/537.36", "browser": {"name": "Yandex", "version": "21.2.0.223.01", "major": "21"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "88.0.4324.182"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 6.0; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.132 YaBrowser/19.9.6.88.01 Safari/537.36", "browser": {"name": "Yandex", "version": "19.9.6.88.01", "major": "19"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 6.0; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 YaBrowser/19.12.4.87.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "19.12.4.87.00", "major": "19"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 6.0; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 YaBrowser/20.2.1.120.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.1.120.00", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.117"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 6.0; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 YaBrowser/20.2.2.127.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.2.127.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.117"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 6.0; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.136 YaBrowser/20.2.6.114.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.6.114.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 6.0; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 YaBrowser/20.3.4.98.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.3.4.98.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 6.0; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.135 YaApp_Android/20.80.0 YaSearchBrowser/20.80.0 BroPP/1.0 SA/1 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "20.80.0", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "84.0.4147.135"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 6.0; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 YaBrowser/20.9.2.95.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.9.2.95.01", "major": "20"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.132 YaBrowser/19.9.6.88.01 Safari/537.36", "browser": {"name": "Yandex", "version": "19.9.6.88.01", "major": "19"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.120 YaBrowser/19.10.2.116.01 Safari/537.36", "browser": {"name": "Yandex", "version": "19.10.2.116.01", "major": "19"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "77.0.3865.120"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 YaBrowser/20.2.1.120.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.1.120.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.117"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.136 YaBrowser/20.2.4.153.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.2.4.153.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 YaBrowser/20.3.2.107.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.3.2.107.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 YaBrowser/20.3.3.92.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.3.3.92.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 YaBrowser/20.4.3.90.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.4.3.90.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 YaBrowser/20.4.4.76.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.4.4.76.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 YaBrowser/20.9.3.85.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.9.3.85.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 8.1.0; NEOSR620) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 BroPP/1.0 SA/3 YaSearchBrowser/20.120.1 YaApp_Android/20.120.1 ru.yandex.searchplugin/20.120.201200023 (TeslaGroup NEOSR620; Android 8.1.0) ZenKit/20.12.0.0-internalNewdesign-Zen.1 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "20.120.1", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "NEOSR620"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.120 YaBrowser/19.10.2.116.01 Safari/537.36", "browser": {"name": "Yandex", "version": "19.10.2.116.01", "major": "19"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "77.0.3865.120"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.122 YaBrowser/20.3.0.276.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.3.0.276.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.122"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 YaBrowser/20.3.4.98.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.3.4.98.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.135 YaBrowser/20.8.0.174.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.8.0.174.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.135"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.135 YaBrowser/20.8.3.71.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.8.3.71.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.135"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 YaBrowser/20.9.1.66.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.9.1.66.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 YaBrowser/20.9.3.85.01 Safari/537.36", "browser": {"name": "Yandex", "version": "20.9.3.85.01", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 8.1.0; Tesla_SP9_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.135 YaBrowser/20.8.2.90.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "20.8.2.90.00", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "84.0.4147.135"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 10; Artel Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 YaApp_Android/20.92.0 YaSearchBrowser/20.92.0 BroPP/1.0 SA/1 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "20.92.0", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 10; Artel Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 YaBrowser/20.11.1.88.00 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "20.11.1.88.00", "major": "20"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 10; Infinix X680) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.182 YaBrowser/21.2.4.139.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "21.2.4.139.00", "major": "21"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "X680", "vendor": "Infinix"}, "engine": {"name": "Blink", "version": "88.0.4324.182"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 8.0.0; SM-A810F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.216 YaBrowser/21.5.2.110.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "21.5.2.110.00", "major": "21"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "SM-A810F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "90.0.4430.216"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 11; SM-G970U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 YaBrowser/21.9.0.359.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "21.9.0.359.00", "major": "21"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "SM-G970U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "93.0.4577.82"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 10; SM-N9600) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.85 YaBrowser/21.11.0.250.01 (beta) Safari/537.36", "browser": {"name": "Yandex", "version": "21.11.0.250.01", "major": "21"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "SM-N9600", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "94.0.4606.85"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 10; Redmi Note 7S) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.143 YaBrowser/22.5.1.143.00 (beta) SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "22.5.1.143.00", "major": "22"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "Redmi Note 7S", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "100.0.4896.143"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 9; SM-A305FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 YaApp_Android/22.50.1 YaSearchBrowser/22.50.1 BroPP/1.0 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "22.50.1", "major": "22"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "SM-A305FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "100.0.4896.127"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 12; SM-A515F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.141 YaBrowser/22.3.6.61.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "22.3.6.61.00", "major": "22"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "SM-A515F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "98.0.4758.141"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; x86_64; Android 12; IN2025) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 YaBrowser/*********.00 SA/3 Mobile Safari/537.36  uacq", "browser": {"name": "Yandex", "version": "*********.00", "major": "23"}, "cpu": {"architecture": "amd64"}, "device": {"type": "mobile", "model": "IN2025", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "110.0.0.0"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 13; SM-S918B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 YaBrowser/*********.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "*********.00", "major": "23"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "SM-S918B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "110.0.0.0"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 13; iPlay50_mini_Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.26 YaBrowser/*********.01 (beta) Safari/537.36", "browser": {"name": "Yandex", "version": "*********.01", "major": "24"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "iPlay50_mini_Pro"}, "engine": {"name": "Blink", "version": "122.0.6261.26"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 13; 23054RA19C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.6367.82 YaBrowser/*********.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "*********.00", "major": "24"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "23054RA19C", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "124.0.6367.82"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 9; VONTAR X3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.51 YaBrowser/*********.01 Safari/537.36", "browser": {"name": "Yandex", "version": "*********.01", "major": "24"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "VONTAR X3"}, "engine": {"name": "Blink", "version": "122.0.6261.51"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 14; SM-S911B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.124 YaBrowser/24.7.5.124.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "24.7.5.124.00", "major": "24"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "SM-S911B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "126.0.6478.124"}, "os": {"name": "Android", "version": "14"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 14; 2107113SG) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.51 YaBrowser/24.7.5.51.00 (alpha) SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "24.7.5.51.00", "major": "24"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "2107113SG", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "126.0.6478.51"}, "os": {"name": "Android", "version": "14"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 13; 2201116SG) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.142 YaBrowser/24.7.4.142.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "24.7.4.142.00", "major": "24"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "2201116SG", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "126.0.6478.142"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 12; Redmi Note 9S) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.99 YaBrowser/24.4.4.99.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "24.4.4.99.00", "major": "24"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "Redmi Note 9S", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "122.0.6261.99"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 14; CPH2413) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.6099.35 YaBrowser/24.2.0.35.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "24.2.0.35.00", "major": "24"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "CPH2413", "vendor": "OPPO"}, "engine": {"name": "Blink", "version": "120.0.6099.35"}, "os": {"name": "Android", "version": "14"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 13; T40HD) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.100 YaBrowser/24.12.3.100.01 Safari/537.36", "browser": {"name": "Yandex", "version": "24.12.3.100.01", "major": "24"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "T40HD"}, "engine": {"name": "Blink", "version": "130.0.6723.100"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 10; POCOPHONE F1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.70 YaBrowser/24.12.4.70.00 (alpha) SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "24.12.4.70.00", "major": "24"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "POCOPHONE F1", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "130.0.6723.70"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 14; P30T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.100 YaBrowser/24.12.3.100.01 Safari/537.36", "browser": {"name": "Yandex", "version": "24.12.3.100.01", "major": "24"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "P30T"}, "engine": {"name": "Blink", "version": "130.0.6723.100"}, "os": {"name": "Android", "version": "14"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 10; SM-M205F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.100 YaBrowser/24.12.3.100.01 Safari/537.36", "browser": {"name": "Yandex", "version": "24.12.3.100.01", "major": "24"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "SM-M205F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "130.0.6723.100"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.0.0 Mobile Safari/537.36 YaApp_Android/8.70/apad YaSearchBrowser/8.70", "browser": {"name": "Yandex", "version": "8.70", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 11; ONEPLUS A6010) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.100 YaBrowser/25.4.1.100.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "25.4.1.100.00", "major": "25"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "A6010", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "134.0.6998.100"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 11; UT8) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.100 YaBrowser/25.4.1.100.01 Safari/537.36", "browser": {"name": "Yandex", "version": "25.4.1.100.01", "major": "25"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "UT8"}, "engine": {"name": "Blink", "version": "134.0.6998.100"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; arm; Android 9; VONTAR X3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.51 YaBrowser/*********.01 Safari/537.36", "browser": {"name": "Yandex", "version": "*********.01", "major": "24"}, "cpu": {"architecture": "arm"}, "device": {"type": "tablet", "model": "VONTAR X3"}, "engine": {"name": "Blink", "version": "122.0.6261.51"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 13; TECNO KI7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.100 YaBrowser/25.4.1.100.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "25.4.1.100.00", "major": "25"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "KI7", "vendor": "TECNO"}, "engine": {"name": "Blink", "version": "134.0.6998.100"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 14; SM-T225) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 YaBrowser/23.7.0.271.01 Safari/537.36", "browser": {"name": "Yandex", "version": "23.7.0.271.01", "major": "23"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "SM-T225", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "114.0.0.0"}, "os": {"name": "Android", "version": "14"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 14; SCG15) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.100 YaBrowser/25.4.1.100.00 SA/3 Mobile Safari/537.36", "browser": {"name": "Yandex", "version": "25.4.1.100.00", "major": "25"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "SCG15", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "134.0.6998.100"}, "os": {"name": "Android", "version": "14"}}, {"ua": "Mozilla/5.0 (Linux; arm_64; Android 13; ELN-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6834.33 YaBrowser/25.3.0.33.01 Safari/537.36", "browser": {"name": "Yandex", "version": "25.3.0.33.01", "major": "25"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "ELN-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "132.0.6834.33"}, "os": {"name": "Android", "version": "13"}}]