import{s as W,S as N,a as P}from"./chunk-AEK57VVT-bd3CVrhI.js";import{_ as u,d as t,j as H,l as S,k as C,e as z,Q as U,R as D,u as F}from"./MermaidPreview-DN0CF7bz.js";import{G as I}from"./graph-Cuz86YKM.js";import{l as O}from"./layout-C-6EmolC.js";import"./chunk-RZ5BOZE2-DXAHhXmf.js";import"./premium-CrQDER7x.js";import"./user-config-BrqC82sm.js";import"./merge-CG3iZ9md.js";import"./reduce-d3EV_kCa.js";import"./_baseUniq-CDn7CWnQ.js";import"./map-CPMUsqym.js";import"./min-1QEMBxke.js";import"./_flatRest-DPsYhEwM.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},i=new Error().stack;i&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[i]="2c0604eb-4d51-44b3-bfaa-e6240b321b9e",e._sentryDebugIdIdentifier="sentry-dbid-2c0604eb-4d51-44b3-bfaa-e6240b321b9e")}catch{}})();var L={},J=u((e,i)=>{L[e]=i},"set"),X=u(e=>L[e],"get"),A=u(()=>Object.keys(L),"keys"),Y=u(()=>A().length,"size"),$={get:X,set:J,keys:A,size:Y},j=u(e=>e.append("circle").attr("class","start-state").attr("r",t().state.sizeUnit).attr("cx",t().state.padding+t().state.sizeUnit).attr("cy",t().state.padding+t().state.sizeUnit),"drawStartState"),q=u(e=>e.append("line").style("stroke","grey").style("stroke-dasharray","3").attr("x1",t().state.textHeight).attr("class","divider").attr("x2",t().state.textHeight*2).attr("y1",0).attr("y2",0),"drawDivider"),Q=u((e,i)=>{const n=e.append("text").attr("x",2*t().state.padding).attr("y",t().state.textHeight+2*t().state.padding).attr("font-size",t().state.fontSize).attr("class","state-title").text(i.id),o=n.node().getBBox();return e.insert("rect",":first-child").attr("x",t().state.padding).attr("y",t().state.padding).attr("width",o.width+2*t().state.padding).attr("height",o.height+2*t().state.padding).attr("rx",t().state.radius),n},"drawSimpleState"),Z=u((e,i)=>{const n=u(function(l,m,b){const k=l.append("tspan").attr("x",2*t().state.padding).text(m);b||k.attr("dy",t().state.textHeight)},"addTspan"),s=e.append("text").attr("x",2*t().state.padding).attr("y",t().state.textHeight+1.3*t().state.padding).attr("font-size",t().state.fontSize).attr("class","state-title").text(i.descriptions[0]).node().getBBox(),g=s.height,x=e.append("text").attr("x",t().state.padding).attr("y",g+t().state.padding*.4+t().state.dividerMargin+t().state.textHeight).attr("class","state-description");let a=!0,d=!0;i.descriptions.forEach(function(l){a||(n(x,l,d),d=!1),a=!1});const w=e.append("line").attr("x1",t().state.padding).attr("y1",t().state.padding+g+t().state.dividerMargin/2).attr("y2",t().state.padding+g+t().state.dividerMargin/2).attr("class","descr-divider"),p=x.node().getBBox(),c=Math.max(p.width,s.width);return w.attr("x2",c+3*t().state.padding),e.insert("rect",":first-child").attr("x",t().state.padding).attr("y",t().state.padding).attr("width",c+2*t().state.padding).attr("height",p.height+g+2*t().state.padding).attr("rx",t().state.radius),e},"drawDescrState"),K=u((e,i,n)=>{const o=t().state.padding,s=2*t().state.padding,g=e.node().getBBox(),x=g.width,a=g.x,d=e.append("text").attr("x",0).attr("y",t().state.titleShift).attr("font-size",t().state.fontSize).attr("class","state-title").text(i.id),p=d.node().getBBox().width+s;let c=Math.max(p,x);c===x&&(c=c+s);let l;const m=e.node().getBBox();i.doc,l=a-o,p>x&&(l=(x-c)/2+o),Math.abs(a-m.x)<o&&p>x&&(l=a-(p-x)/2);const b=1-t().state.textHeight;return e.insert("rect",":first-child").attr("x",l).attr("y",b).attr("class",n?"alt-composit":"composit").attr("width",c).attr("height",m.height+t().state.textHeight+t().state.titleShift+1).attr("rx","0"),d.attr("x",l+o),p<=x&&d.attr("x",a+(c-s)/2-p/2+o),e.insert("rect",":first-child").attr("x",l).attr("y",t().state.titleShift-t().state.textHeight-t().state.padding).attr("width",c).attr("height",t().state.textHeight*3).attr("rx",t().state.radius),e.insert("rect",":first-child").attr("x",l).attr("y",t().state.titleShift-t().state.textHeight-t().state.padding).attr("width",c).attr("height",m.height+3+2*t().state.textHeight).attr("rx",t().state.radius),e},"addTitleAndBox"),V=u(e=>(e.append("circle").attr("class","end-state-outer").attr("r",t().state.sizeUnit+t().state.miniPadding).attr("cx",t().state.padding+t().state.sizeUnit+t().state.miniPadding).attr("cy",t().state.padding+t().state.sizeUnit+t().state.miniPadding),e.append("circle").attr("class","end-state-inner").attr("r",t().state.sizeUnit).attr("cx",t().state.padding+t().state.sizeUnit+2).attr("cy",t().state.padding+t().state.sizeUnit+2)),"drawEndState"),tt=u((e,i)=>{let n=t().state.forkWidth,o=t().state.forkHeight;if(i.parentId){let s=n;n=o,o=s}return e.append("rect").style("stroke","black").style("fill","black").attr("width",n).attr("height",o).attr("x",t().state.padding).attr("y",t().state.padding)},"drawForkJoinState"),et=u((e,i,n,o)=>{let s=0;const g=o.append("text");g.style("text-anchor","start"),g.attr("class","noteText");let x=e.replace(/\r\n/g,"<br/>");x=x.replace(/\n/g,"<br/>");const a=x.split(z.lineBreakRegex);let d=1.25*t().state.noteMargin;for(const w of a){const p=w.trim();if(p.length>0){const c=g.append("tspan");if(c.text(p),d===0){const l=c.node().getBBox();d+=l.height}s+=d,c.attr("x",i+t().state.noteMargin),c.attr("y",n+s+1.25*t().state.noteMargin)}}return{textWidth:g.node().getBBox().width,textHeight:s}},"_drawLongText"),at=u((e,i)=>{i.attr("class","state-note");const n=i.append("rect").attr("x",0).attr("y",t().state.padding),o=i.append("g"),{textWidth:s,textHeight:g}=et(e,0,0,o);return n.attr("height",g+2*t().state.noteMargin),n.attr("width",s+t().state.noteMargin*2),n},"drawNote"),_=u(function(e,i){const n=i.id,o={id:n,label:i.id,width:0,height:0},s=e.append("g").attr("id",n).attr("class","stateGroup");i.type==="start"&&j(s),i.type==="end"&&V(s),(i.type==="fork"||i.type==="join")&&tt(s,i),i.type==="note"&&at(i.note.text,s),i.type==="divider"&&q(s),i.type==="default"&&i.descriptions.length===0&&Q(s,i),i.type==="default"&&i.descriptions.length>0&&Z(s,i);const g=s.node().getBBox();return o.width=g.width+2*t().state.padding,o.height=g.height+2*t().state.padding,$.set(n,o),o},"drawState"),R=0,it=u(function(e,i,n){const o=u(function(d){switch(d){case N.relationType.AGGREGATION:return"aggregation";case N.relationType.EXTENSION:return"extension";case N.relationType.COMPOSITION:return"composition";case N.relationType.DEPENDENCY:return"dependency"}},"getRelationType");i.points=i.points.filter(d=>!Number.isNaN(d.y));const s=i.points,g=U().x(function(d){return d.x}).y(function(d){return d.y}).curve(D),x=e.append("path").attr("d",g(s)).attr("id","edge"+R).attr("class","transition");let a="";if(t().state.arrowMarkerAbsolute&&(a=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,a=a.replace(/\(/g,"\\("),a=a.replace(/\)/g,"\\)")),x.attr("marker-end","url("+a+"#"+o(N.relationType.DEPENDENCY)+"End)"),n.title!==void 0){const d=e.append("g").attr("class","stateLabel"),{x:w,y:p}=F.calcLabelPosition(i.points),c=z.getRows(n.title);let l=0;const m=[];let b=0,k=0;for(let f=0;f<=c.length;f++){const h=d.append("text").attr("text-anchor","middle").text(c[f]).attr("x",w).attr("y",p+l),y=h.node().getBBox();b=Math.max(b,y.width),k=Math.min(k,y.x),S.info(y.x,w,p+l),l===0&&(l=h.node().getBBox().height,S.info("Title height",l,p)),m.push(h)}let E=l*c.length;if(c.length>1){const f=(c.length-1)*l*.5;m.forEach((h,y)=>h.attr("y",p+y*l-f)),E=l*c.length}const r=d.node().getBBox();d.insert("rect",":first-child").attr("class","box").attr("x",w-b/2-t().state.padding/2).attr("y",p-E/2-t().state.padding/2-3.5).attr("width",b+t().state.padding).attr("height",E+t().state.padding),S.info(r)}R++},"drawEdge"),B,T={},rt=u(function(){},"setConf"),nt=u(function(e){e.append("defs").append("marker").attr("id","dependencyEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"insertMarkers"),st=u(function(e,i,n,o){B=t().state;const s=t().securityLevel;let g;s==="sandbox"&&(g=H("#i"+i));const x=s==="sandbox"?H(g.nodes()[0].contentDocument.body):H("body"),a=s==="sandbox"?g.nodes()[0].contentDocument:document;S.debug("Rendering diagram "+e);const d=x.select(`[id='${i}']`);nt(d);const w=o.db.getRootDoc();G(w,d,void 0,!1,x,a,o);const p=B.padding,c=d.node().getBBox(),l=c.width+p*2,m=c.height+p*2,b=l*1.75;C(d,m,b,B.useMaxWidth),d.attr("viewBox",`${c.x-B.padding}  ${c.y-B.padding} `+l+" "+m)},"draw"),dt=u(e=>e?e.length*B.fontSizeFactor:1,"getLabelWidth"),G=u((e,i,n,o,s,g,x)=>{const a=new I({compound:!0,multigraph:!0});let d,w=!0;for(d=0;d<e.length;d++)if(e[d].stmt==="relation"){w=!1;break}n?a.setGraph({rankdir:"LR",multigraph:!0,compound:!0,ranker:"tight-tree",ranksep:w?1:B.edgeLengthFactor,nodeSep:w?1:50,isMultiGraph:!0}):a.setGraph({rankdir:"TB",multigraph:!0,compound:!0,ranksep:w?1:B.edgeLengthFactor,nodeSep:w?1:50,ranker:"tight-tree",isMultiGraph:!0}),a.setDefaultEdgeLabel(function(){return{}});const p=x.db.getStates(),c=x.db.getRelations(),l=Object.keys(p);for(const r of l){const f=p[r];n&&(f.parentId=n);let h;if(f.doc){let y=i.append("g").attr("id",f.id).attr("class","stateGroup");h=G(f.doc,y,f.id,!o,s,g,x);{y=K(y,f,o);let v=y.node().getBBox();h.width=v.width,h.height=v.height+B.padding/2,T[f.id]={y:B.compositTitleSize}}}else h=_(i,f,a);if(f.note){const y={descriptions:[],id:f.id+"-note",note:f.note,type:"note"},v=_(i,y,a);f.note.position==="left of"?(a.setNode(h.id+"-note",v),a.setNode(h.id,h)):(a.setNode(h.id,h),a.setNode(h.id+"-note",v)),a.setParent(h.id,h.id+"-group"),a.setParent(h.id+"-note",h.id+"-group")}else a.setNode(h.id,h)}S.debug("Count=",a.nodeCount(),a);let m=0;c.forEach(function(r){m++,S.debug("Setting edge",r),a.setEdge(r.id1,r.id2,{relation:r,width:dt(r.title),height:B.labelHeight*z.getRows(r.title).length,labelpos:"c"},"id"+m)}),O(a),S.debug("Graph after layout",a.nodes());const b=i.node();a.nodes().forEach(function(r){r!==void 0&&a.node(r)!==void 0?(S.warn("Node "+r+": "+JSON.stringify(a.node(r))),s.select("#"+b.id+" #"+r).attr("transform","translate("+(a.node(r).x-a.node(r).width/2)+","+(a.node(r).y+(T[r]?T[r].y:0)-a.node(r).height/2)+" )"),s.select("#"+b.id+" #"+r).attr("data-x-shift",a.node(r).x-a.node(r).width/2),g.querySelectorAll("#"+b.id+" #"+r+" .divider").forEach(h=>{const y=h.parentElement;let v=0,M=0;y&&(y.parentElement&&(v=y.parentElement.getBBox().width),M=parseInt(y.getAttribute("data-x-shift"),10),Number.isNaN(M)&&(M=0)),h.setAttribute("x1",0-M+8),h.setAttribute("x2",v-M-8)})):S.debug("No Node "+r+": "+JSON.stringify(a.node(r)))});let k=b.getBBox();a.edges().forEach(function(r){r!==void 0&&a.edge(r)!==void 0&&(S.debug("Edge "+r.v+" -> "+r.w+": "+JSON.stringify(a.edge(r))),it(i,a.edge(r),a.edge(r).relation))}),k=b.getBBox();const E={id:n||"root",label:n||"root",width:0,height:0};return E.width=k.width+2*B.padding,E.height=k.height+2*B.padding,S.debug("Doc rendered",E,a),E},"renderDoc"),ot={setConf:rt,draw:st},St={parser:W,get db(){return new N(1)},renderer:ot,styles:P,init:u(e=>{e.state||(e.state={}),e.state.arrowMarkerAbsolute=e.arrowMarkerAbsolute},"init")};export{St as diagram};
//# sourceMappingURL=stateDiagram-DGXRK772-CIPTzAy6.js.map
