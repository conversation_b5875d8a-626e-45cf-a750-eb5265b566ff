<!doctype html><html dir="$i18n{textdirection}" lang="$i18n{language}"><head><meta charset="utf-8"><meta version="58601172/32637 - 2025-07-21T12:18:29.243Z"><title>Wallet</title><script src="/webui-setup.js"></script><script src="/app-setup.js"></script><script src="/base-error-reporting.js"></script><script src="/wallet-error-reporting.js"></script><link rel="manifest" href="/manifest.webapp.json"><script src="chrome://resources/js/load_time_data.m.js" type="module"></script><script src="/strings.m.js" type="module"></script><style>/* Copyright (C) Microsoft Corporation. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file. */

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 14px;
  background-color: rgb(247, 247, 247);
  margin: 0;
}

@media (forced-colors:none) {
  input::selection {
    color: #FFF;
    background: #0078D4;
  }
}

@media (prefers-color-scheme: dark) {
  body {
    background-color: rgb(51, 51, 51);
  }
}
@media (forced-colors:none) and (prefers-color-scheme: dark) {
  input::selection {
    color: #000;
    /* RGBA because Blink applies an opacity otherwise */
    background: rgba(147, 184, 231, 0.996);
    opacity: 1;
  }
}</style><style>#modal-root {
      position: fixed;
    }
    #dialog-root {
      position: fixed;
      z-index: 6;
      top: 15px;
      right: 50%;
      transform: translate(50%, 0px);
    }
    #fluent-default-layer-host {
      z-index: 11000000 !important;
    }
    .web-components-root {
      display: flex;
      flex-direction: column;
      height: 100vh;
      overflow-y: hidden;
    }
    .wallet-hub-header {
      top: 0;
      position: static;
      width: calc(100vw - 16px);
      z-index: 2;
      height: 48px;
    }
    .wallet-hub-content {
      flex: 1;
      display: flex;
      position: relative;
      /* Overlap main content div with wallet-hub-header so that the scroll 
      thumb can go all the way till top */
      top: -48px
    }
    .wallet-hub-content-navbar {
      flex-basis: 300px;
      margin-top: 48px;
    }
    .wallet-hub-content-body-visible {
      flex-grow: 1;
    }</style></head><body style="margin: 0"><div id="app-root"></div><div id="modal-root"></div><div id="dialog-root" aria-live="assertive"></div><div id="web-components-root"><div id="wallet-hub-header"><wallet-hub-header/></div><div id="wallet-hub-content"><div id="wallet-hub-content-navbar"><wallet-hub-navbar/></div><div id="wallet-hub-content-body-react" class="wallet-hub-content-body-visible"></div><div id="wallet-hub-content-body-web-components"><router-view></router-view></div></div></div><script defer="defer" src="/runtime.bundle.js"></script><script defer="defer" src="/crypto.bundle.js"></script><script defer="defer" src="/vendor.bundle.js"></script><script defer="defer" src="/load-hub-i18n.bundle.js"></script><script defer="defer" src="/wallet.bundle.js"></script></body></html>