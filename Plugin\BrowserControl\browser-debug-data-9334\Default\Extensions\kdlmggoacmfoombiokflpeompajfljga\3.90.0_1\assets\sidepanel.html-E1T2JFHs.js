import{u as l,d,e as u,f as h,r,j as e,C as f,B as g,g as x,h as p,k as b,H as j,l as y,m as w,n as i,s as v,o as C,p as k,t as N,q as I}from"./premium-CrQDER7x.js";import{B}from"./user-config-BrqC82sm.js";(function(){try{var s=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new Error().stack;t&&(s._sentryDebugIds=s._sentryDebugIds||{},s._sentryDebugIds[t]="04114955-3c13-40ff-9a8b-efcc800bf4e1",s._sentryDebugIdIdentifier="sentry-dbid-04114955-3c13-40ff-9a8b-efcc800bf4e1")}catch{}})();globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(s,t){return this.cache.has(s)?this.cache.get(s):(this.cache.set(s,t),t)}};function P(){const{t:s}=l(),[t,c]=d(v),n=u(t),a=h(t),o=r.useCallback(()=>{a.generating||a.resetConversation()},[a]),m=r.useMemo(()=>({reset:o}),[o]);return e.jsx(f.Provider,{value:m,children:e.jsxs("div",{className:"bg-primary-background flex h-full flex-col overflow-hidden",children:[e.jsxs("div",{className:"border-primary-border mx-3 flex flex-row items-center justify-between gap-2 border-b border-solid py-3",children:[e.jsxs("div",{className:"flex flex-row items-center gap-3",children:[e.jsxs("div",{className:"flex flex-row items-center gap-2",children:[e.jsx(g,{text:n.name,src:n.avatar,className:"h-4 w-4"}),e.jsx(x,{botId:t,name:n.name,onSwitchBot:c})]}),a.messages.length>0&&e.jsx("img",{src:p,className:b("size-[17px]",a.generating?"cursor-not-allowed":"cursor-pointer"),onClick:o})]}),e.jsx("a",{href:"/app.html",target:"_blank",children:e.jsx(j,{className:"size-4 cursor-pointer"})})]}),e.jsx(y,{botId:t,messages:a.messages,generating:a.generating,className:"mx-3"}),e.jsxs("div",{className:"mx-3 my-3 flex flex-col gap-3",children:[e.jsx("hr",{className:"border-primary-border grow"}),e.jsx(w,{compactMode:!0,disabled:a.generating,autoFocus:!0,placeholder:"Ask me anything...",onSubmit:a.sendMessage,actionButton:a.generating?e.jsx(i,{text:s("Stop"),color:"flat",size:"small",onClick:a.stopGenerating}):e.jsx(i,{text:s("Send"),color:"primary",type:"submit",size:"small"})})]})]})})}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(s,t){return this.cache.has(s)?this.cache.get(s):(this.cache.set(s,t),t)}};function A(){const{t:s}=l(),t=r.useCallback(()=>{N("open_premium_from_sidepanel"),window.open(B.runtime.getURL("app.html#/premium?source=sidepanel"),"_blank")},[]);return e.jsxs("div",{className:"w-full h-full flex flex-col justify-center items-center gap-3",children:[e.jsx("img",{src:I,className:"w-10 h-10"}),e.jsx("div",{className:"text-xl font-bold",children:s("Premium Feature")}),e.jsx(i,{text:s("Upgrade to unlock"),color:"primary",onClick:t})]})}function _(){const s=k();return s.isLoading?null:s.activated?e.jsx(r.Suspense,{children:e.jsx(P,{})}):e.jsx(A,{})}const S=document.getElementById("app"),M=C(S);M.render(e.jsx(_,{}));
//# sourceMappingURL=sidepanel.html-E1T2JFHs.js.map
