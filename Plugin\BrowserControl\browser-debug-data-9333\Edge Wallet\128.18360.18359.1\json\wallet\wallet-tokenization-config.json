{"providers": {"visa": {"eligible_sites": ["microsoft.com", "skype.com", "github.com", "linkedin.com", "minecraft.net", "xbox.com"], "ineligible_sites": [], "eligible_bins": []}, "mastercard": {"eligible_sites": [], "ineligible_sites": ["1800petmeds.com", "2co.com", "6figurefba.com", "99designs.com", "aa.com", "aaa.com", "abusupport.com", "acornonline.com", "actblue.com", "adobe.com", "adventhealth.com", "affirm.com", "affordableseating.net", "afterpay.com", "agrimatics.com", "ahaslides.com", "airbnb.com", "aircanada.com", "aladdinbroadwaymerchandise.com", "alamo.com", "alaskaair.com", "aliabdaal.com", "aliexpress.us", "allegiantair.com", "allianz.com", "allposters.com", "allswellhome.com", "alzresearch.org", "american.com", "americanmusical.com", "ancestry.com", "annabellematson.com", "answerthepublic.com", "aol.com", "appliancesconnection.com", "arbys.com", "art.com", "asrsupport.com", "atlantagasequipment.com", "atlasphysicaltherapynj.com", "att.com", "audible.com", "aura.com", "autozone.com", "avanqst.com", "avis.com", "bagoames.com", "baps.store", "barnesandnoble.com", "basbleu.com", "baskinrobbins.com", "bayareafastrak.org", "bbhosted.com", "beautiful.ai", "beenverified.com", "beerandbrewing.com", "bestbuy.com", "bestbuybusiness.com", "bestfreescore.com", "bestvibe.com", "bidsquare.com", "bigmotionbicycleessentials.com", "blizzard.com", "bloomberg.com", "bloomingdales.com", "bluechew.com", "blurcharge.com", "boldllc.com", "bonobos.com", "booking.com", "bridgetohealinginc.com", "bringatrailer.com", "brownells.com", "bruthotel.com", "bsnsports.com", "buffalowildwings.com", "build.com", "bumper.com", "buymeacoffee.com", "caktus.ai", "camicb.org", "canva.com", "care.com", "carlabarneslpc.com", "carnival.com", "carrentals.com", "carters.com", "caseys.com", "cashstar.com", "catalogclassicsvipinsider.com", "cdkeys.com", "cebupacificair.com", "cedar.com", "centrohelp.com", "centrohelp.eu", "cfr.org", "charlotteobserver.com", "cheapfareguru.com", "checkfreesco.com", "chegg.com", "chicagobusiness.com", "chumbacasino.com", "cigarsinternational.com", "classicvacations.com", "clcahlp.com", "clearchoice.com", "clearme.com", "cleverbridge.com", "clickbank.com", "clickfreescore.com", "clickfunnels.com", "clipstudio.net", "colouredpencilacademy.com", "cometeer.com", "consumercellular.com", "consumerreports.org", "consumersenergy.com", "coursehero.com", "coursera.org", "craigmedical.com", "credit.com", "creditrepair.com", "creditreview.co", "cryptorevolution.com", "curascriptsd.com", "cvmaker.com", "cvs.com", "cyclebar.com", "d23.com", "daedalusbooks.com", "datacamp.com", "dealstarr.com", "dentalplans.com", "digitalriver.com", "dish.com", "disney.com", "disneydrop.com", "disneyonbroadway.com", "disneyplus.com", "dollargeneral.com", "dollarshaveclub.com", "dominos.com", "draftkings.com", "dreamhost.com", "driversupport.com", "dsw.com", "dunkindonuts.com", "durable.co", "easy-host.us", "ecompanystore.com", "economist.com", "ecsi.net", "edisonmail.com", "eduzz.com", "eg-america.com", "elcapitantheatre.com", "eloquii.com", "embroiderybytm-designsbyteresa.com", "enterprise.com", "entropystream.live", "epicgames.com", "epidemicsound.com", "epoch-group.com", "espn.com", "eu.wargaming.net", "expedia.com", "experian.com", "exposedskincare.com", "express-scripts.com", "express-scripts.com", "expressvpn.com", "eztexting.com", "fabletics.com", "fabulousfit.com", "facebook.com", "factor75.com", "famepay.com", "fantasypros.com", "fbalaunchpad.com", "fcdrycleaners.com", "fedex.com", "ffnhelp.com", "finviz.com", "firehousesubs.com", "firekeeperscasino.com", "firstlinebenefits.com", "flertul.com", "flex-charge.com", "flexjobs.com", "flooranddecor.com", "flyfrontier.com", "focusrite.com", "foodnetwork.com", "footballguys.com", "foxit.com", "freedomamz.com", "freeshipping.com", "fubo.tv", "fxreplay.com", "g2g.com", "gannett.com", "gaysandgadgets.com", "geico.com", "genealogybank.com", "geodehealth.com", "getonce.com", "gndmgng.com", "go.com", "godaddy.com", "google.com", "gpmsigns.com", "grammarly.com", "greatcall.com", "greenchef.com", "grubhub.com", "gtfcharge.com", "gvlfashionweek.com", "hammacher.com", "hammitt.com", "hamptonseattlenorth.com", "handy.com", "happylimo.com", "hardaddy.com", "havertys.com", "hayneedle.com", "healthymeats.net", "hearingaidaccessories.co.uk", "hearst.com", "heartland-derm.com", "hellofresh.com", "help.max.com", "help.paymentico.com", "henryschein.com", "heptabase.com", "heroceoclub.com", "hidive.com", "hilton.com", "hims.com", "hmatchhelp.com", "hobbylobby.com", "holisticspeech.org", "homeagain.com", "homeaglow.com", "homechef.com", "hotels.com", "hotwire.com", "hpe.com", "hthlth.com", "hudhomesusa.org", "huel.com", "huffy.com", "hulu.com", "hulu.com", "humana.com", "icon-healthcare.com", "identityguard.com", "ikea.com", "imglobal.com", "instantcheckmate.com", "intuit.com", "iolo.com", "itch.io", "ivp.com", "ivycomputer.com", "jarocorp.com", "jcpenney.com", "jerseymikes.com", "jetblue.com", "jimmyjohns.com", "jobscan.co", "johnsphotography.ca", "jointherealworld.com", "jpplus.com", "judynordseth.com", "justanswer.com", "kalmbach.com", "keldecor.net", "kick.com", "kickstarter.com", "klike.net", "knowthycustomer.com", "kohls.com", "kupid.ai", "laposupport.com", "legalshield.com", "legalzoom.com", "legion.org", "lessons.com", "lexingtonlaw.com", "life-line-keto-acv-gummies.company.site", "linkedin.com", "linode.com", "littlecaesars.com", "livecareer.com", "lively.com", "lowes.com", "lucky-land-c.com", "lululemon.com", "lycamobile.com", "lyricstudio.net", "macys.com", "macysbackstage.com", "magicalpools.com", "mardel.com", "market-pay.com", "marriott.com", "marvel.com", "mattsflights.com", "mcafee.com", "medbusoffice.com", "medici.tv", "medium.com", "menards.com", "michaels.com", "microsoft.com", "midjourney.com", "milbstore.com", "milfroom.com", "minmcs.com", "mintmobile.com", "mngdte.com", "modcloth.com", "moonpay.com", "moosejaw.com", "mountainsteals.com", "msp-camp.com", "muc-ftf.com", "muc-td.com", "mugshotlook.com", "mutualart.com", "mwebelting.com", "my3bsc.com", "mycommunitymade.com", "myperfectresume.com", "mytacticalpromos.com", "nationalcar.com", "nationalcellulardirectory.com", "nationalgeographic.com", "nationsbenefits.com", "nav.com", "nbaudio.uk", "ncl.com", "nespresso.com", "net32.com", "netflix.com", "netgear.com", "nfl.com", "nightzookeeper.com", "nordstrom.ca", "nordstrom.com", "nordstromrack.com", "now.site", "numberguru.com", "nuubu.com", "nytimes.com", "oakland.edu", "office.com", "officedepot.com", "officialpayments.com", "olivegarden.com", "omnisend.com", "on-running.com", "onlyfans.com", "openai.com", "optimustracker.com", "optumpersonalcare.com", "optumrx.com", "orientaltrading.com", "orkin.com", "os-bot.com", "oshkosh.com", "outwrite.com", "ownerly.com", "pandabuy.com", "panerabread.com", "paramountplus.com", "patreon.com", "paybis.com", "payitgov.com", "paylocalgov.com", "paytient.com", "pcloud.com", "pdffiller.com", "peacocktv.com", "peek.com", "peoplelooker.com", "peoplesearch123.com", "peoplesmart.com", "performancegolf.com", "perlego.com", "pge.com", "pinevalleylodgewi.com", "pixar.com", "pizzahut.com", "playstation.com", "podcastle.ai", "portal-portail.apps.cic.gc.ca", "prana.com", "premier1supplies.com", "preparedhero.com", "prettylitter.com", "primallabs.com", "prime-sana-true.com", "primowater.com", "princess.com", "printify.com", "printtimeaz.com", "probiller.com", "prodigygame.com", "progressive.com", "progrexion.com", "publix.com", "pulsz.com", "pulszbingo.com", "pulundr.com", "pupbox.com", "questdiagnostics.com", "quimbee.com", "qvc.com", "radiax.com", "ramp.network", "real-debrid.com", "realtystore.com", "rebsev.com", "redbarradio.net", "regattaman.com", "remitly.com", "renttoown.org", "replit.com", "resume-now.com", "resume.com", "resumenerd.com", "ring.com", "robertrtg.com", "roblox.com", "rocketlawyer.com", "rocketmoney.com", "roll20.net", "royalcaribbean.com", "rustytaco.com", "safeway.com", "samsclub.com", "samsung.com", "schoolsavers.com", "scribd.com", "seatgeek.com", "seaworldentertainment.com", "shapermint.com", "shipmentsfree.com", "shopdisney.com", "shoppbs.org", "signals.com", "signalsvipinsider.com", "siriusxm.com", "skechers.com", "sketch.com", "skigroup.net", "skinport.com", "skiphop.com", "skylead.io", "slicelife.com", "smallpdf.com", "snow.com", "sonicdrivein.com", "spark.net", "speedpay.com", "spirit.com", "spocket.co", "spotify.com", "spyfu.com", "squarespace.com", "srpnet.com", "staples.com", "starbucks.ca", "starbucks.com", "starplus.com", "statefarm.com", "staxpayments.com", "stjude.org", "stokastic.com", "streamfab.me", "stripe.com", "sunpass.com", "supportpdffiller.com", "supportplus.com", "supportplusvipinsider.com", "surlatable.com", "t-mobile.com", "tagbgroup.com", "teachsimple.com", "techliquidators.com", "temu.com", "terminallance.com", "thalys.com", "thedallasgarden.com", "theguardian.com", "thekrogerco.com", "theoptionsmillionaire.com", "thevapemall.com", "thewirelesscatalog.com", "ticketphiladelphia.org", "tidalwaveautospa.com", "tiktok.com", "timcast.com", "time.com", "tomford.com", "topshelfjewelry.store", "totaladblock.com", "totalav.com", "tracfone.com", "tractorsupply.com", "tradingview.com", "travelocity.com", "travelscape.international", "triseptsolutions.com", "tropsunprod.com", "truthfinder.com", "turo.com", "twitch.tv", "txtag.org", "uber.com", "ucdp.net", "ucsd.edu", "uhaul.com", "united.com", "universalorlando.com", "universalorlandovacations.com", "universalscreenarts.com", "universalstudioshollywood.com", "universe.com", "university.com", "urbanoutfitters.com", "ushtickets.com", "ushtix.com", "usps.com", "usps.com", "va.gov", "vaxvacationaccess.com", "vend-o.com", "vendostore.com", "venmo.com", "viator.com", "vidangel.com", "vincecamuto.com", "vrbo.com", "wa.gov", "walmart.com", "waltdisneystudios.com", "webflow.co", "webstaurantstore.com", "wefunder.com", "wendys.com", "wepay.com", "whatonearthcatalog.com", "whatonearthvipinsider.com", "wholescripts.com", "winred.com", "wirelessvipinsider.com", "wix.com", "wmq.etimspayments.com", "wngsdream.com", "woodworkerexpress.com", "workoutanytime.com", "workspace.google.com", "wrangler.com", "writesonic.com", "www2.optumrx.com", "xbox.com", "xfinity.com", "xsolla.com", "xylabs.com", "yahooinc.com", "yelloh.com", "yelp.com", "yourmechanic.com", "yourscoreandmore.com", "youtube.com", "zalatpizza.com", "zappos.com"], "not_ready_sites": ["ae.com", "aerie.com", "blueapron.com", "gamestop.com"], "eligible_bins": ["510114", "510243", "510267", "510336", "510340", "510398", "510405", "510438", "510446", "510624", "510652", "510758", "510843", "510974", "511021", "511053", "511068", "511086", "511227", "511259", "511264", "511431", "511432", "511433", "511446", "511514", "511525", "511572", "511752", "511777", "511785", "511807", "511869", "511899", "511902", "511905", "511924", "511938", "511960", "511985", "512063", "512110", "512129", "512130", "512186", "512230", "512241", "512242", "512268", "512278", "512799", "512823", "512862", "512996", "513033", "513046", "513205", "513227", "513236", "513254", "513255", "513266", "513289", "513382", "513410", "513638", "513707", "513793", "513882", "513885", "513887", "513888", "513889", "513891", "513897", "513907", "513985", "514013", "514015", "514022", "514029", "514049", "514059", "514229", "514257", "514271", "514287", "514306", "514474", "514688", "514719", "514724", "514794", "514855", "514886", "514900", "514901", "514976", "514990", "515000", "515063", "515075", "515106", "515198", "515232", "515313", "515374", "515383", "515558", "515692", "515735", "515856", "515910", "515959", "515980", "516013", "516014", "516071", "516135", "516188", "516199", "516224", "516266", "516304", "516413", "516425", "516437", "516452", "516493", "516565", "516580", "516652", "516666", "516676", "516718", "517002", "517140", "517156", "517178", "517180", "517223", "517326", "517330", "517539", "517605", "517685", "517697", "517763", "517767", "517777", "517800", "517835", "517847", "517922", "517933", "517996", "518076", "518081", "518088", "518129", "518177", "518367", "518441", "518481", "518531", "518700", "518747", "518913", "518941", "519094", "519100", "519101", "519105", "519146", "519206", "519270", "519321", "519442", "519450", "519605", "519695", "519726", "519727", "519731", "519732", "519752", "519824", "519846", "519945", "519949", "519991", "520077", "520125", "520182", "520205", "520316", "520337", "520369", "520502", "520534", "520572", "520600", "520602", "520607", "520702", "520738", "521131", "521138", "521161", "521267", "521301", "521322", "521328", "521332", "521335", "521336", "521341", "521379", "521382", "521481", "521507", "521510", "521515", "521527", "521531", "521601", "521602", "521611", "521613", "521614", "521620", "521621", "521630", "521640", "521641", "521650", "521660", "521670", "521671", "521680", "521756", "521810", "521849", "521876", "521882", "522228", "522269", "522301", "522341", "522343", "522353", "522364", "522367", "522654", "522709", "522717", "522735", "522750", "522769", "522906", "522933", "522935", "522977", "522999", "523079", "523081", "523087", "523093", "523222", "523241", "523245", "523409", "523464", "523468", "523484", "523525", "523548", "523569", "523644", "523652", "523653", "523686", "523726", "523737", "523754", "523762", "523788", "523798", "523808", "523859", "523993", "524030", "524038", "524048", "524059", "524099", "524400", "524413", "524415", "524478", "524656", "524664", "524718", "524757", "524762", "524777", "524822", "524823", "524841", "524849", "524860", "524866", "524867", "524924", "524928", "524932", "524936", "524937", "525307", "525363", "525421", "525437", "525438", "525441", "525447", "525475", "525478", "525484", "525606", "525607", "525637", "525697", "525717", "525722", "525749", "525993", "526102", "526114", "526126", "526143", "526146", "526149", "526207", "526264", "526283", "526288", "526308", "526311", "526434", "526473", "526484", "526524", "526824", "526956", "527077", "527079", "527101", "527168", "527175", "527184", "527193", "527215", "527321", "527355", "527553", "527664", "527670", "527675", "527693", "527725", "527778", "527845", "527854", "527868", "527869", "528092", "528212", "528213", "528291", "528445", "528466", "528469", "528473", "528544", "528546", "528556", "528559", "528563", "528564", "528567", "528568", "528623", "528752", "528772", "528814", "528844", "528888", "528898", "528916", "528923", "528924", "528928", "528934", "528935", "528955", "529003", "529004", "529044", "529079", "529104", "529171", "529277", "529306", "529307", "529308", "529334", "529366", "529401", "529463", "529540", "529857", "529873", "529921", "529944", "530009", "530053", "530090", "530098", "530173", "530320", "530511", "530585", "530589", "530705", "530782", "530790", "530791", "530833", "530854", "530878", "530882", "530885", "530888", "530889", "530890", "530892", "530893", "530894", "530895", "530896", "530897", "530898", "530899", "530902", "530904", "530908", "530952", "530953", "530968", "531033", "531380", "531481", "531490", "531532", "531590", "531679", "531703", "531724", "531815", "531816", "531840", "531842", "531843", "531900", "531942", "531980", "531988", "532010", "532159", "532211", "532445", "532652", "532721", "532740", "532777", "532914", "533030", "533038", "533085", "533086", "533087", "533091", "533092", "533202", "533402", "533466", "533592", "533702", "533956", "533972", "533976", "534051", "534053", "534181", "534242", "534348", "534435", "534466", "534475", "534530", "534607", "534610", "534611", "534636", "534770", "534883", "535055", "535105", "535824", "535897", "535905", "536121", "536129", "536165", "536284", "536291", "536293", "536296", "536312", "536313", "536314", "536315", "536319", "536455", "536544", "536584", "536651", "536654", "536655", "536658", "536681", "536682", "536684", "536809", "536811", "536812", "536815", "536816", "536818", "536819", "536821", "536823", "536824", "536842", "536914", "536915", "536924", "536925", "536947", "536962", "537078", "537089", "537100", "537740", "537948", "537953", "537955", "537956", "537958", "537959", "537961", "537982", "538104", "538130", "538132", "538134", "538136", "538181", "538192", "538728", "538767", "538790", "538931", "538951", "538973", "539046", "539111", "539135", "539241", "539242", "539244", "539271", "539430", "539456", "539459", "539469", "539487", "539610", "539611", "539620", "539640", "539641", "539642", "539643", "539644", "539646", "539647", "539655", "539656", "539680", "539681", "539682", "539810", "539820", "539822", "539840", "539841", "539842", "539843", "539844", "539846", "539855", "539856", "539857", "539860", "539863", "539870", "539871", "539880", "539882", "539883", "540006", "540091", "540092", "540108", "540113", "540125", "540163", "540257", "540418", "540470", "540501", "540539", "540569", "540658", "540662", "540663", "540723", "540740", "540751", "540789", "540790", "540792", "540803", "540809", "540817", "540821", "540840", "540872", "540928", "540938", "540992", "541005", "541014", "541065", "541071", "541080", "541111", "541116", "541117", "541147", "541153", "541161", "541232", "541242", "541364", "541413", "541484", "541505", "541526", "541530", "541568", "541579", "541610", "541655", "541738", "541748", "541787", "541870", "541887", "541931", "541972", "542019", "542042", "542092", "542134", "542217", "542257", "542263", "542268", "542285", "542295", "542323", "542325", "542326", "542331", "542360", "542361", "542372", "542379", "542403", "542411", "542413", "542418", "542422", "542423", "542524", "542539", "542637", "542659", "542731", "542735", "542784", "542810", "542947", "543059", "543189", "543295", "543337", "543347", "543386", "543389", "543432", "543450", "543459", "543572", "543594", "543634", "543668", "543700", "543701", "543703", "543812", "543875", "543991", "544015", "544094", "544113", "544131", "544153", "544154", "544171", "544183", "544232", "544235", "544236", "544288", "544314", "544321", "544400", "544423", "544430", "544475", "544518", "544664", "544723", "544724", "544766", "544801", "544932", "544955", "544974", "545072", "545081", "545137", "545198", "545208", "545317", "545360", "545426", "545457", "545497", "545639", "545715", "545716", "546309", "546316", "546453", "546591", "546613", "546616", "546617", "546641", "546645", "546646", "546648", "546652", "546653", "546676", "546700", "546705", "546854", "546884", "546885", "547007", "547014", "547018", "547140", "547211", "547216", "547219", "547220", "547225", "547242", "547256", "547259", "547270", "547338", "547378", "547392", "547500", "547538", "547542", "547570", "547578", "547608", "547627", "547644", "547647", "547667", "547684", "547708", "547715", "547725", "547791", "547842", "547845", "547854", "547858", "547875", "547884", "547895", "547899", "547915", "548029", "548030", "548031", "548054", "548055", "548103", "548312", "548313", "548397", "548693", "548776", "548853", "549101", "549108", "549109", "549113", "549114", "549117", "549125", "549137", "549141", "549142", "549143", "549149", "549238", "549239", "549242", "549243", "549265", "549269", "549276", "549278", "549292", "549389", "549409", "549418", "549429", "549437", "549487", "549508", "549517", "549528", "549535", "549537", "549559", "549603", "549655", "549678", "549764", "549806", "549899", "549901", "549903", "549917", "549943", "549944", "550114", "550148", "550348", "551206", "551644", "551664", "551744", "552030", "552034", "552043", "552094", "552138", "552225", "552226", "552234", "552254", "552257", "552276", "552285", "552301", "552310", "552312", "552318", "552319", "552320", "552331", "552356", "552361", "552364", "552368", "552373", "552379", "552393", "552402", "552417", "552422", "552430", "552437", "552448", "552452", "552464", "552465", "552470", "552479", "552483", "552499", "552627", "552632", "552633", "552743", "552746", "552747", "552817", "552862", "552874", "552876", "552945", "552952", "553036", "553092", "553094", "553129", "553237", "553244", "553249", "553275", "553310", "553311", "553453", "553515", "553616", "553742", "554092", "554276", "554295", "554305", "554494", "554512", "554513", "554514", "554518", "554569", "554573", "554711", "554790", "554836", "554908", "554916", "554918", "554919", "554924", "554925", "554947", "554958", "554981", "555002", "555008", "555151", "555317", "555329", "555330", "555337", "555376", "555398", "555438", "555468", "555524", "555563", "555622", "555637", "555646", "555658", "555689", "555701", "555751", "555765", "555792", "555867", "555979", "556095", "556300", "556301", "556307", "556322", "556323", "556331", "556337", "556340", "556341", "556364", "556368", "556369", "556375", "556377", "556385", "556708", "556723", "556735", "556736", "556753", "556766", "556780", "556782", "556791", "556840", "556842", "556846", "556850", "556862", "556904", "556906", "556907", "556933", "556935", "556963", "556976", "557009", "557330", "557335", "557337", "557342", "557679", "557715", "557801", "557923", "558010", "558165", "558181", "558392", "558457", "558459", "558492", "558497", "558553", "558594", "558603", "558628", "558652", "558676", "558726", "558733", "558767", "558892", "558988", "559058", "559070", "559148", "559222", "559257", "559266", "559271", "559280", "559281", "559282", "559284", "559285", "559287", "559294", "559325", "559331", "559332", "559333", "559338", "559347", "559348", "559497", "559499", "559500", "559504", "559505", "559507", "559512", "559513", "559514", "559551", "559578", "559579", "559585", "559795", "559963", "559971"]}}, "virtual_card_ui": {"eligible_sites": [], "ineligible_sites": ["deruorderonline.com"]}}