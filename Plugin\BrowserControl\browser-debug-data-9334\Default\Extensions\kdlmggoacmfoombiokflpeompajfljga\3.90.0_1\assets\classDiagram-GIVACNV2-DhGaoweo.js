import{c as a,C as s,a as d,s as t}from"./chunk-A2AXSNBT-BhUSkrQF.js";import{_ as i}from"./MermaidPreview-DN0CF7bz.js";import"./chunk-RZ5BOZE2-DXAHhXmf.js";import"./premium-CrQDER7x.js";import"./user-config-BrqC82sm.js";import"./merge-CG3iZ9md.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},r=new Error().stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="36a11984-8fe8-4dad-bed4-f5d6bbbaef37",e._sentryDebugIdIdentifier="sentry-dbid-36a11984-8fe8-4dad-bed4-f5d6bbbaef37")}catch{}})();var m={parser:a,get db(){return new s},renderer:d,styles:t,init:i(e=>{e.class||(e.class={}),e.class.arrowMarkerAbsolute=e.arrowMarkerAbsolute},"init")};export{m as diagram};
//# sourceMappingURL=classDiagram-GIVACNV2-DhGaoweo.js.map
