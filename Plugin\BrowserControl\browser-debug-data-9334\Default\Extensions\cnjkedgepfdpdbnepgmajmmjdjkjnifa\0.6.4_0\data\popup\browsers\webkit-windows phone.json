[{"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Microsoft; Lumia 650) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, lik", "browser": {"name": "WebKit", "version": "537.36", "major": "537"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "WebKit", "version": "537.36"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, lik", "browser": {"name": "WebKit", "version": "537.36", "major": "537"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "WebKit", "version": "537.36"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 AppleWebKit/537.36 KHTML, lik", "browser": {"name": "WebKit", "version": "537.36", "major": "537"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 AppleWebKit", "vendor": "Microsoft"}, "engine": {"name": "WebKit", "version": "537.36"}, "os": {"name": "Windows Phone", "version": "10.0"}}]