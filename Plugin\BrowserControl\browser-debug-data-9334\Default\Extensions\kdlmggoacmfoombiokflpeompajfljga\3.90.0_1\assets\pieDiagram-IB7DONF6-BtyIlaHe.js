import{p as U}from"./chunk-4BMEZGHF-ClwwOidP.js";import{aa as S,a2 as M,aA as j,C as H,n as Z,o as q,s as J,g as K,c as Q,b as X,_ as u,l as _,t as Y,d as tt,D as et,H as at,O as rt,k as nt}from"./MermaidPreview-DN0CF7bz.js";import{p as it}from"./radar-MK3ICKWK-DiEKPfCW.js";import{d as W}from"./arc-CPgjmil3.js";import{o as st}from"./ordinal-JB92v_fC.js";import"./premium-CrQDER7x.js";import"./user-config-BrqC82sm.js";import"./merge-CG3iZ9md.js";import"./map-CPMUsqym.js";import"./_baseUniq-CDn7CWnQ.js";import"./uniqBy-Zyo4kdnp.js";import"./min-1QEMBxke.js";import"./reduce-d3EV_kCa.js";import"./clone-CpeMRwbc.js";import"./init-UKB2U-5-.js";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},a=new Error().stack;a&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[a]="5eab1586-8def-480b-88b9-7dad351bd7e7",t._sentryDebugIdIdentifier="sentry-dbid-5eab1586-8def-480b-88b9-7dad351bd7e7")}catch{}})();function ot(t,a){return a<t?-1:a>t?1:a>=t?0:NaN}function lt(t){return t}function ct(){var t=lt,a=ot,f=null,o=S(0),p=S(M),x=S(0);function i(e){var r,l=(e=j(e)).length,c,w,h=0,d=new Array(l),n=new Array(l),y=+o.apply(this,arguments),D=Math.min(M,Math.max(-M,p.apply(this,arguments)-y)),m,C=Math.min(Math.abs(D)/l,x.apply(this,arguments)),T=C*(D<0?-1:1),g;for(r=0;r<l;++r)(g=n[d[r]=r]=+t(e[r],r,e))>0&&(h+=g);for(a!=null?d.sort(function(v,A){return a(n[v],n[A])}):f!=null&&d.sort(function(v,A){return f(e[v],e[A])}),r=0,w=h?(D-l*T)/h:0;r<l;++r,y=m)c=d[r],g=n[c],m=y+(g>0?g*w:0)+T,n[c]={data:e[c],index:r,value:g,startAngle:y,endAngle:m,padAngle:C};return n}return i.value=function(e){return arguments.length?(t=typeof e=="function"?e:S(+e),i):t},i.sortValues=function(e){return arguments.length?(a=e,f=null,i):a},i.sort=function(e){return arguments.length?(f=e,a=null,i):f},i.startAngle=function(e){return arguments.length?(o=typeof e=="function"?e:S(+e),i):o},i.endAngle=function(e){return arguments.length?(p=typeof e=="function"?e:S(+e),i):p},i.padAngle=function(e){return arguments.length?(x=typeof e=="function"?e:S(+e),i):x},i}var N=H.pie,z={sections:new Map,showData:!1,config:N},E=z.sections,F=z.showData,dt=structuredClone(N),ut=u(()=>structuredClone(dt),"getConfig"),pt=u(()=>{E=new Map,F=z.showData,Y()},"clear"),gt=u(({label:t,value:a})=>{E.has(t)||(E.set(t,a),_.debug(`added new section: ${t}, with value: ${a}`))},"addSection"),ft=u(()=>E,"getSections"),mt=u(t=>{F=t},"setShowData"),ht=u(()=>F,"getShowData"),P={getConfig:ut,clear:pt,setDiagramTitle:Z,getDiagramTitle:q,setAccTitle:J,getAccTitle:K,setAccDescription:Q,getAccDescription:X,addSection:gt,getSections:ft,setShowData:mt,getShowData:ht},yt=u((t,a)=>{U(t,a),a.setShowData(t.showData),t.sections.map(a.addSection)},"populateDb"),vt={parse:u(async t=>{const a=await it("pie",t);_.debug(a),yt(a,P)},"parse")},St=u(t=>`
  .pieCircle{
    stroke: ${t.pieStrokeColor};
    stroke-width : ${t.pieStrokeWidth};
    opacity : ${t.pieOpacity};
  }
  .pieOuterCircle{
    stroke: ${t.pieOuterStrokeColor};
    stroke-width: ${t.pieOuterStrokeWidth};
    fill: none;
  }
  .pieTitleText {
    text-anchor: middle;
    font-size: ${t.pieTitleTextSize};
    fill: ${t.pieTitleTextColor};
    font-family: ${t.fontFamily};
  }
  .slice {
    font-family: ${t.fontFamily};
    fill: ${t.pieSectionTextColor};
    font-size:${t.pieSectionTextSize};
    // fill: white;
  }
  .legend text {
    fill: ${t.pieLegendTextColor};
    font-family: ${t.fontFamily};
    font-size: ${t.pieLegendTextSize};
  }
`,"getStyles"),xt=St,wt=u(t=>{const a=[...t.entries()].map(o=>({label:o[0],value:o[1]})).sort((o,p)=>p.value-o.value);return ct().value(o=>o.value)(a)},"createPieArcs"),Dt=u((t,a,f,o)=>{_.debug(`rendering pie chart
`+t);const p=o.db,x=tt(),i=et(p.getConfig(),x.pie),e=40,r=18,l=4,c=450,w=c,h=at(a),d=h.append("g");d.attr("transform","translate("+w/2+","+c/2+")");const{themeVariables:n}=x;let[y]=rt(n.pieOuterStrokeWidth);y??(y=2);const D=i.textPosition,m=Math.min(w,c)/2-e,C=W().innerRadius(0).outerRadius(m),T=W().innerRadius(m*D).outerRadius(m*D);d.append("circle").attr("cx",0).attr("cy",0).attr("r",m+y/2).attr("class","pieOuterCircle");const g=p.getSections(),v=wt(g),A=[n.pie1,n.pie2,n.pie3,n.pie4,n.pie5,n.pie6,n.pie7,n.pie8,n.pie9,n.pie10,n.pie11,n.pie12],b=st(A);d.selectAll("mySlices").data(v).enter().append("path").attr("d",C).attr("fill",s=>b(s.data.label)).attr("class","pieCircle");let G=0;g.forEach(s=>{G+=s}),d.selectAll("mySlices").data(v).enter().append("text").text(s=>(s.data.value/G*100).toFixed(0)+"%").attr("transform",s=>"translate("+T.centroid(s)+")").style("text-anchor","middle").attr("class","slice"),d.append("text").text(p.getDiagramTitle()).attr("x",0).attr("y",-(c-50)/2).attr("class","pieTitleText");const I=d.selectAll(".legend").data(b.domain()).enter().append("g").attr("class","legend").attr("transform",(s,$)=>{const k=r+l,L=k*b.domain().length/2,B=12*r,V=$*k-L;return"translate("+B+","+V+")"});I.append("rect").attr("width",r).attr("height",r).style("fill",b).style("stroke",b),I.data(v).append("text").attr("x",r+l).attr("y",r-l).text(s=>{const{label:$,value:k}=s.data;return p.getShowData()?`${$} [${k}]`:$});const R=Math.max(...I.selectAll("text").nodes().map(s=>(s==null?void 0:s.getBoundingClientRect().width)??0)),O=w+e+r+l+R;h.attr("viewBox",`0 0 ${O} ${c}`),nt(h,c,O,i.useMaxWidth)},"draw"),At={draw:Dt},Pt={parser:vt,db:P,renderer:At,styles:xt};export{Pt as diagram};
//# sourceMappingURL=pieDiagram-IB7DONF6-BtyIlaHe.js.map
