[{"ua": "NetSurf/2.0 (Linux; i686)", "browser": {"name": "NetSurf", "version": "2.0", "major": "2"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "NetSurf", "version": "2.0"}, "os": {"name": "Linux"}}, {"ua": "NetSurf/1.2 (Linux; x86_64)", "browser": {"name": "NetSurf", "version": "1.2", "major": "1"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "NetSurf", "version": "1.2"}, "os": {"name": "Linux"}}, {"ua": "NetSurf/1.2 (Linux; i686)", "browser": {"name": "NetSurf", "version": "1.2", "major": "1"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "NetSurf", "version": "1.2"}, "os": {"name": "Linux"}}, {"ua": "NetSurf/1.1 (Linux; i686)", "browser": {"name": "NetSurf", "version": "1.1", "major": "1"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "NetSurf", "version": "1.1"}, "os": {"name": "Linux"}}, {"ua": "NetSurf/1.0 (Linux; i686)", "browser": {"name": "NetSurf", "version": "1.0", "major": "1"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "NetSurf", "version": "1.0"}, "os": {"name": "Linux"}}]