[{"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/60.0.3112.78 Chrome/60.0.3112.78 Safari/537.36", "browser": {"name": "Chromium", "version": "60.0.3112.78", "major": "60"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "60.0.3112.78"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/60.0.3112.113 Chrome/60.0.3112.113 Safari/537.36", "browser": {"name": "Chromium", "version": "60.0.3112.113", "major": "60"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "60.0.3112.113"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.19 (KHTML, like Gecko) Ubuntu/11.10 Chromium/18.0.1025.142 Chrome/18.0.1025.142 Safari/535.19", "browser": {"name": "Chromium", "version": "18.0.1025.142", "major": "18"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "535.19"}, "os": {"name": "Ubuntu", "version": "11.10"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.11 (KHTML, like Gecko) Ubuntu/11.10 Chromium/17.0.963.65 Chrome/17.0.963.65 Safari/535.11", "browser": {"name": "Chromium", "version": "17.0.963.65", "major": "17"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "535.11"}, "os": {"name": "Ubuntu", "version": "11.10"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.11 (KHTML, like Gecko) Ubuntu/11.04 Chromium/17.0.963.65 Chrome/17.0.963.65 Safari/535.11", "browser": {"name": "Chromium", "version": "17.0.963.65", "major": "17"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "535.11"}, "os": {"name": "Ubuntu", "version": "11.04"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.11 (KHTML, like Gecko) Ubuntu/10.10 Chromium/17.0.963.65 Chrome/17.0.963.65 Safari/535.11", "browser": {"name": "Chromium", "version": "17.0.963.65", "major": "17"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "535.11"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/535.11 (KHTML, like Gecko) Ubuntu/11.10 Chromium/17.0.963.65 Chrome/17.0.963.65 Safari/535.11", "browser": {"name": "Chromium", "version": "17.0.963.65", "major": "17"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "535.11"}, "os": {"name": "Ubuntu", "version": "11.10"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.11 (KHTML, like Gecko) Ubuntu/11.04 Chromium/17.0.963.56 Chrome/17.0.963.56 Safari/535.11", "browser": {"name": "Chromium", "version": "17.0.963.56", "major": "17"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "535.11"}, "os": {"name": "Ubuntu", "version": "11.04"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/535.2 (KHTML, like Gecko) Ubuntu/11.10 Chromium/15.0.874.120 Chrome/15.0.874.120 Safari/535.2", "browser": {"name": "Chromium", "version": "15.0.874.120", "major": "15"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "535.2"}, "os": {"name": "Ubuntu", "version": "11.10"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.2 (KHTML, like Gecko) Ubuntu/11.04 Chromium/15.0.871.0 Chrome/15.0.871.0 Safari/535.2", "browser": {"name": "Chromium", "version": "15.0.871.0", "major": "15"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "535.2"}, "os": {"name": "Ubuntu", "version": "11.04"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/535.1 (KHTML, like Gecko) Ubuntu/11.04 Chromium/14.0.825.0 Chrome/14.0.825.0 Safari/535.1", "browser": {"name": "Chromium", "version": "14.0.825.0", "major": "14"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "535.1"}, "os": {"name": "Ubuntu", "version": "11.04"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/535.1 (KHTML, like Gecko) Ubuntu/11.04 Chromium/14.0.814.0 Chrome/14.0.814.0 Safari/535.1", "browser": {"name": "Chromium", "version": "14.0.814.0", "major": "14"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "535.1"}, "os": {"name": "Ubuntu", "version": "11.04"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/535.1 (KHTML, like Gecko) Ubuntu/10.04 Chromium/14.0.813.0 Chrome/14.0.813.0 Safari/535.1", "browser": {"name": "Chromium", "version": "14.0.813.0", "major": "14"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "535.1"}, "os": {"name": "Ubuntu", "version": "10.04"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.1 (KHTML, like Gecko) Ubuntu/10.10 Chromium/14.0.808.0 Chrome/14.0.808.0 Safari/535.1", "browser": {"name": "Chromium", "version": "14.0.808.0", "major": "14"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "535.1"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/535.1 (KHTML, like Gecko) Ubuntu/10.04 Chromium/14.0.808.0 Chrome/14.0.808.0 Safari/535.1", "browser": {"name": "Chromium", "version": "14.0.808.0", "major": "14"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "535.1"}, "os": {"name": "Ubuntu", "version": "10.04"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/535.1 (KHTML, like Gecko) Ubuntu/10.04 Chromium/14.0.804.0 Chrome/14.0.804.0 Safari/535.1", "browser": {"name": "Chromium", "version": "14.0.804.0", "major": "14"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "535.1"}, "os": {"name": "Ubuntu", "version": "10.04"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/535.1 (KHTML, like Gecko) Ubuntu/11.04 Chromium/14.0.803.0 Chrome/14.0.803.0 Safari/535.1", "browser": {"name": "Chromium", "version": "14.0.803.0", "major": "14"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "535.1"}, "os": {"name": "Ubuntu", "version": "11.04"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.1 (KHTML, like Gecko) Ubuntu/11.04 Chromium/13.0.782.41 Chrome/13.0.782.41 Safari/535.1", "browser": {"name": "Chromium", "version": "13.0.782.41", "major": "13"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "535.1"}, "os": {"name": "Ubuntu", "version": "11.04"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/534.35 (KHTML, like Gecko) Ubuntu/10.10 Chromium/13.0.764.0 Chrome/13.0.764.0 Safari/534.35", "browser": {"name": "Chromium", "version": "13.0.764.0", "major": "13"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "534.35"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/534.33 (KHTML, like Gecko) Ubuntu/9.10 Chromium/13.0.752.0 Chrome/13.0.752.0 Safari/534.33", "browser": {"name": "Chromium", "version": "13.0.752.0", "major": "13"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "534.33"}, "os": {"name": "Ubuntu", "version": "9.10"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/534.30 (KHTML, like Gecko) Ubuntu/11.04 Chromium/12.0.742.112 Chrome/12.0.742.112 Safari/534.30", "browser": {"name": "Chromium", "version": "12.0.742.112", "major": "12"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Ubuntu", "version": "11.04"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/534.30 (KHTML, like Gecko) Ubuntu/10.10 Chromium/12.0.742.112 Chrome/12.0.742.112 Safari/534.30", "browser": {"name": "Chromium", "version": "12.0.742.112", "major": "12"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/534.30 (KHTML, like Gecko) Ubuntu/10.04 Chromium/12.0.742.112 Chrome/12.0.742.112 Safari/534.30", "browser": {"name": "Chromium", "version": "12.0.742.112", "major": "12"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Ubuntu", "version": "10.04"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/534.30 (KHTML, like Gecko) Ubuntu/11.04 Chromium/12.0.742.112 Chrome/12.0.742.112 Safari/534.30", "browser": {"name": "Chromium", "version": "12.0.742.112", "major": "12"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Ubuntu", "version": "11.04"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/534.30 (KHTML, like Gecko) Ubuntu/10.10 Chromium/12.0.742.112 Chrome/12.0.742.112 Safari/534.30", "browser": {"name": "Chromium", "version": "12.0.742.112", "major": "12"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/534.30 (KHTML, like Gecko) Ubuntu/10.04 Chromium/12.0.742.112 Chrome/12.0.742.112 Safari/534.30", "browser": {"name": "Chromium", "version": "12.0.742.112", "major": "12"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Ubuntu", "version": "10.04"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/534.24 (KHTML, like Gecko) Ubuntu/10.10 Chromium/12.0.703.0 Chrome/12.0.703.0 Safari/534.24", "browser": {"name": "Chromium", "version": "12.0.703.0", "major": "12"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "534.24"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/534.24 (KHTML, like Gecko) Ubuntu/10.10 Chromium/12.0.702.0 Chrome/12.0.702.0 Safari/534.24", "browser": {"name": "Chromium", "version": "12.0.702.0", "major": "12"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "534.24"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/534.24 (KHTML, like Gecko) Ubuntu/10.04 Chromium/11.0.696.0 Chrome/11.0.696.0 Safari/534.24", "browser": {"name": "Chromium", "version": "11.0.696.0", "major": "11"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "534.24"}, "os": {"name": "Ubuntu", "version": "10.04"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/534.16 (KHTML, like Gecko) Ubuntu/10.10 Chromium/10.0.648.133 Chrome/10.0.648.133 Safari/534.16", "browser": {"name": "Chromium", "version": "10.0.648.133", "major": "10"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "534.16"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en-US) AppleWebKit/534.16 (KHTML, like Gecko) Ubuntu/10.10 Chromium/10.0.648.133 Chrome/10.0.648.133 Safari/534.16", "browser": {"name": "Chromium", "version": "10.0.648.133", "major": "10"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "534.16"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/534.16 (KHTML, like Gecko) Ubuntu/10.10 Chromium/10.0.648.127 Chrome/10.0.648.127 Safari/534.16", "browser": {"name": "Chromium", "version": "10.0.648.127", "major": "10"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "534.16"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/534.16 (KHTML, like Gecko) Ubuntu/10.10 Chromium/10.0.648.0 Chrome/10.0.648.0 Safari/534.16", "browser": {"name": "Chromium", "version": "10.0.648.0", "major": "10"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "534.16"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en-US) AppleWebKit/534.16 (KHTML, like Gecko) Ubuntu/10.10 Chromium/10.0.648.0 Chrome/10.0.648.0 Safari/534.16", "browser": {"name": "Chromium", "version": "10.0.648.0", "major": "10"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "534.16"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/534.16 (KHTML, like Gecko) Ubuntu/10.10 Chromium/10.0.642.0 Chrome/10.0.642.0 Safari/534.16", "browser": {"name": "Chromium", "version": "10.0.642.0", "major": "10"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "534.16"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en-US) AppleWebKit/534.15 (KHTML, like Gecko) Ubuntu/10.10 Chromium/10.0.613.0 Chrome/10.0.613.0 Safari/534.15", "browser": {"name": "Chromium", "version": "10.0.613.0", "major": "10"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "534.15"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en-US) AppleWebKit/534.15 (KHTML, like Gecko) Ubuntu/10.04 Chromium/10.0.612.3 Chrome/10.0.612.3 Safari/534.15", "browser": {"name": "Chromium", "version": "10.0.612.3", "major": "10"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "534.15"}, "os": {"name": "Ubuntu", "version": "10.04"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en-US) AppleWebKit/534.15 (KHTML, like Gecko) Ubuntu/10.10 Chromium/10.0.611.0 Chrome/10.0.611.0 Safari/534.15", "browser": {"name": "Chromium", "version": "10.0.611.0", "major": "10"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "534.15"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/534.14 (KHTML, like Gecko) Ubuntu/10.10 Chromium/9.0.600.0 Chrome/9.0.600.0 Safari/534.14", "browser": {"name": "Chromium", "version": "9.0.600.0", "major": "9"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "534.14"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/534.13 (KHTML, like Gecko) Ubuntu/10.04 Chromium/9.0.595.0 Chrome/9.0.595.0 Safari/534.13", "browser": {"name": "Chromium", "version": "9.0.595.0", "major": "9"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "534.13"}, "os": {"name": "Ubuntu", "version": "10.04"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en-US) AppleWebKit/534.13 (KHTML, like Gecko) Ubuntu/9.10 Chromium/9.0.592.0 Chrome/9.0.592.0 Safari/534.13", "browser": {"name": "Chromium", "version": "9.0.592.0", "major": "9"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "534.13"}, "os": {"name": "Ubuntu", "version": "9.10"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/534.10 (KHTML, like Gecko) Ubuntu/10.10 Chromium/8.0.552.237 Chrome/8.0.552.237 Safari/534.10", "browser": {"name": "Chromium", "version": "8.0.552.237", "major": "8"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "534.10"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/68.0.3440.75 Chrome/68.0.3440.75 Safari/537.36", "browser": {"name": "Chromium", "version": "68.0.3440.75", "major": "68"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "68.0.3440.75"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/65.0.3325.181 Chrome/65.0.3325.181 Safari/537.36", "browser": {"name": "Chromium", "version": "65.0.3325.181", "major": "65"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Blink", "version": "65.0.3325.181"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/68.0.3440.106 Chrome/68.0.3440.106 Safari/537.36", "browser": {"name": "Chromium", "version": "68.0.3440.106", "major": "68"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "68.0.3440.106"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/72.0.3626.121 Chrome/72.0.3626.121 Safari/537.36", "browser": {"name": "Chromium", "version": "72.0.3626.121", "major": "72"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "72.0.3626.121"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/71.0.3578.98 Chrome/71.0.3578.98 Safari/537.36", "browser": {"name": "Chromium", "version": "71.0.3578.98", "major": "71"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "71.0.3578.98"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/73.0.3683.86 Chrome/73.0.3683.86 Safari/537.36", "browser": {"name": "Chromium", "version": "73.0.3683.86", "major": "73"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "73.0.3683.86"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/73.0.3683.86 Chrome/73.0.3683.86 Safari/537.36", "browser": {"name": "Chromium", "version": "73.0.3683.86", "major": "73"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Blink", "version": "73.0.3683.86"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/75.0.3770.90 Chrome/75.0.3770.90 Safari/537.36", "browser": {"name": "Chromium", "version": "75.0.3770.90", "major": "75"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "75.0.3770.90"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/73.0.3683.86 Chrome/73.0.3683.86 Safari/537.36", "browser": {"name": "Chromium", "version": "73.0.3683.86", "major": "73"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "73.0.3683.86"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/74.0.3729.169 Chrome/74.0.3729.169 Safari/537.36", "browser": {"name": "Chromium", "version": "74.0.3729.169", "major": "74"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Blink", "version": "74.0.3729.169"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/80.0.3987.87 Chrome/80.0.3987.87 Safari/537.36", "browser": {"name": "Chromium", "version": "80.0.3987.87", "major": "80"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "80.0.3987.87"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/80.0.3987.87 Chrome/80.0.3987.87 Safari/537.36", "browser": {"name": "Chromium", "version": "80.0.3987.87", "major": "80"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "80.0.3987.87"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/63.0.3239.84 Chrome/63.0.3239.84 Safari/537.36", "browser": {"name": "Chromium", "version": "63.0.3239.84", "major": "63"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "63.0.3239.84"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/80.0.3987.87 Chrome/80.0.3987.87 Safari/537.36", "browser": {"name": "Chromium", "version": "80.0.3987.87", "major": "80"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Blink", "version": "80.0.3987.87"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/79.0.3945.79 Chrome/79.0.3945.79 Safari/537.36", "browser": {"name": "Chromium", "version": "79.0.3945.79", "major": "79"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "79.0.3945.79"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/83.0.4103.61 Chrome/83.0.4103.61 Safari/537.36", "browser": {"name": "Chromium", "version": "83.0.4103.61", "major": "83"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "83.0.4103.61"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/85.0.4173.0 Chrome/85.0.4173.0 Safari/537.36", "browser": {"name": "Chromium", "version": "85.0.4173.0", "major": "85"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "85.0.4173.0"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/79.0.3945.79 Chrome/79.0.3945.79 Safari/537.36", "browser": {"name": "Chromium", "version": "79.0.3945.79", "major": "79"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "79.0.3945.79"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/65.0.3325.181 Chrome/65.0.3325.181 Safari/537.36", "browser": {"name": "Chromium", "version": "65.0.3325.181", "major": "65"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Blink", "version": "65.0.3325.181"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/67.0.3396.99 Chrome/67.0.3396.99 Safari/537.36", "browser": {"name": "Chromium", "version": "67.0.3396.99", "major": "67"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Blink", "version": "67.0.3396.99"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/96.0.4683.105 Chrome/96.0.4683.105 Safari/537.36", "browser": {"name": "Chromium", "version": "96.0.4683.105", "major": "96"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Blink", "version": "96.0.4683.105"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/99.0.4852.191 Chrome/99.0.4852.191 Safari/537.36", "browser": {"name": "Chromium", "version": "99.0.4852.191", "major": "99"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "99.0.4852.191"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/100.0.4852.204 Chrome/100.0.4852.204 Safari/537.36", "browser": {"name": "Chromium", "version": "100.0.4852.204", "major": "100"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "100.0.4852.204"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/100.0.4871.149 Chrome/100.0.4871.149 Safari/537.36", "browser": {"name": "Chromium", "version": "100.0.4871.149", "major": "100"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Blink", "version": "100.0.4871.149"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/99.0.4852.94 Chrome/99.0.4852.94 Safari/537.36", "browser": {"name": "Chromium", "version": "99.0.4852.94", "major": "99"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "99.0.4852.94"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.11 (KHTML, like Gecko) Ubuntu/11.10 Chromium/27.0.1453.93 Chrome/27.0.1453.93 Safari/537.36", "browser": {"name": "Chromium", "version": "27.0.1453.93", "major": "27"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "535.11"}, "os": {"name": "Ubuntu", "version": "11.10"}}]