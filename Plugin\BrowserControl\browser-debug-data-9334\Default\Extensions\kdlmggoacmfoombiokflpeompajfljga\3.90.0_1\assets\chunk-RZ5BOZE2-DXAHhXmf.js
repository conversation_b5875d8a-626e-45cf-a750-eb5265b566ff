import{_ as r,j as s,k as g,l as u}from"./MermaidPreview-DN0CF7bz.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d73f898c-f964-4c6e-9824-c2aeff9f3ab7",e._sentryDebugIdIdentifier="sentry-dbid-d73f898c-f964-4c6e-9824-c2aeff9f3ab7")}catch{}})();var h=r((e,t)=>{let o;return t==="sandbox"&&(o=s("#i"+e)),(t==="sandbox"?s(o.nodes()[0].contentDocument.body):s("body")).select(`[id="${e}"]`)},"getDiagramElement"),x=r((e,t,o,n)=>{e.attr("class",o);const{width:i,height:a,x:c,y:f}=l(e,t);g(e,a,i,n);const d=w(c,f,i,a,t);e.attr("viewBox",d),u.debug(`viewBox configured: ${d} with padding: ${t}`)},"setupViewPortForSVG"),l=r((e,t)=>{var n;const o=((n=e.node())==null?void 0:n.getBBox())||{width:0,height:0,x:0,y:0};return{width:o.width+t*2,height:o.height+t*2,x:o.x,y:o.y}},"calculateDimensionsWithPadding"),w=r((e,t,o,n,i)=>`${e-i} ${t-i} ${o} ${n}`,"createViewBox");export{h as g,x as s};
//# sourceMappingURL=chunk-RZ5BOZE2-DXAHhXmf.js.map
