{"appVersionTitle": {"message": "Это поле только для чтения. Используйте страницу настроек для парсинга вручную."}, "applyActiveWindow": {"message": "Применить (активное окно)"}, "applyActiveWindowTitle": {"message": "Установить эту строку User-Agent для всех вкладок внутри текущего окна"}, "applyAllWindows": {"message": "Применить (все окна)"}, "applyAllWindowsTitle": {"message": "Установить эту строку User-Agent в качестве основной строки браузера"}, "applyContainer": {"message": "Применить (контейнер)"}, "applyContainerTitle": {"message": "Установить эту строку User-Agent в качестве строки текущего контейнера"}, "applyContainerWindow": {"message": "Применить (контейнер в окне)"}, "applyContainerWindowTitle": {"message": "Установить эту строку User-Agent для всех вкладок внутри текущего окна контейнера"}, "atoz": {"message": "От A до Z"}, "blackListMode": {"message": "Режим черного списка"}, "blackListModeDescription": {"message": "Применить установленную строку User-Agent ко всем вкладкам за исключением содержащих следующие домены верхнего уровня (список доменов разделяется запятыми). Важно: даже если применяемая к окну строка User-Agent установлена из всплывающего окна, для этого списка будет использоваться строка User-Agent вашего браузера по умолчанию."}, "cache": {"message": "Использовать кэширование для улучшения производительности (рекомендуемое значение true). Отключите эту опцию только при использовании режима настройки, когда нужно изменять строку User-Agent с помощью списка при каждом запросе."}, "considerContainers": {"message": "Учитывать контейнеры"}, "considerContainersTitle": {"message": "Позволять расширению иметь доступ к контейнерам вашего браузера. При наличии доступа вкладки внутри изолированных контейнеров не используют строку User-Agent контейнера по умолчанию. Вам нужно устанавливать эту строку для каждого нового контейнера."}, "customMode": {"message": "Режим настройки"}, "customModeDescription": {"message": "Пробовать считывание строки User-Agent из объекта JSON; в другом случае использовать строку User-Agent по умолчанию или установленную во всплывающем окне. Используйте \"*\" в качестве сервера для применения ко всем доменам. Вы можете случайно выбирать из нескольких строк User-Agent с помощью массива вместо фиксированной строки. Если в объекте JSON присутствует ключ \"_\", отсылающий к массиву серверов, то расширение будет случайно выбирать строку User-Agent для каждого сервера в списке. Это полезно, если вы не желаете случайно менять User-Agent до перезапуска браузера."}, "customUserAgentParsing": {"message": "Парсинг установленного User-Agent"}, "customUserAgentParsingDescription": {"message": "Объект JSON для обхода внутреннего метода парсинга строки User-Agent. Ключи — настоящие строки User-Agent. Значение каждого ключа- объект ключей, требуемых для установки объекта \"navigator\". Вы можете использовать ключевое слово \"[delete]\", если вам нужен ключ в объекте \"navigator\" для удаления."}, "dbReset": {"message": "Щелкните 2 раза левой кнопкой мыши для сброса!"}, "description": {"message": "Описание"}, "disableSpoofing": {"message": "Отключить подмену User-Agent"}, "disableSpoofingDescription": {"message": "Разделяемый запятыми список ключевых слов, на которых расширение не должно подменять заголовок User-Agent. Используйте этот список для защиты URL, содержащих эти ключевые слова. Каждое ключевое слово должно быть длиной не менее 5 символов."}, "donate": {"message": "Поддержать разработку"}, "exactMatch": {"message": "Использовать точное совпадение (если включено, вам нужно добавлять все поддомены в черный и белый списки как задумано. Если выключено, все поддомены проходят проверку на совпадение (например, www.google.com проходит проверку, если google.com находится в списке))"}, "exportSettings": {"message": "Экспортировать настройки"}, "exportSettingsTitle": {"message": "Чтобы сгенерировать уменьшенную версию, нажмите эту кнопку, удерживая клавишу Shift"}, "extensionDescription": {"description": "Description of the extension.", "message": "Подменяет User-Agent на веб-сайтах, пытающихся собрать информацию о ваших путешествиях по сети и выдать не нужное вам содержимое"}, "extensionName": {"description": "Name of the extension.", "message": "User-Agent Switcher and Manager"}, "faqs": {"message": "Открывать страницу FAQ при обновлении"}, "filterAgents": {"message": "Выбрать UA"}, "filterAmong": {"message": "Выбрать из $1"}, "help": {"message": "Страница FAQ (Помощь)"}, "importSettings": {"message": "Импортировать настройки"}, "insertSample": {"message": "Вставить пример"}, "log": {"message": "Отобр<PERSON><PERSON><PERSON><PERSON>ь отладочную информацию в консоли браузера"}, "managedStorage": {"message": "Данное расширение поддерживает Managed Storage, и эти настройки могут быть изменены автоматически или установлены изначально администратором домена. Прочтите FAQ для получения более подробной информации."}, "msgDefaultUA": {"message": "User-Agent по умолчанию, вместо этого нажмите кнопку 'Сбросить'"}, "msgDisabled": {"message": "Отключено. Используется строка User-Agent по умолчанию."}, "msgDisabledOnContainer": {"message": "Отключено в этом контейнере. Используется строка User-Agent по умолчанию."}, "msgUASet": {"message": "User-Agent установлен"}, "noMatch": {"message": "Для данного запроса нет строк User-Agent"}, "options": {"message": "Настройки"}, "optionsSaved": {"message": "Настройки сохранены."}, "optionsTitle": {"message": "Открыть страницу настроек"}, "oscpuTitle": {"message": "Это поле только для чтения. Используйте страницу настроек для парсинга вручную."}, "platformTitle": {"message": "Это поле только для чтения. Используйте страницу настроек для парсинга вручную."}, "productTitle": {"message": "Это поле только для чтения. Используйте страницу настроек для парсинга вручную."}, "refreshTab": {"message": "Обновить вкладку"}, "refreshTabTitle": {"message": "Обновить текущую страницу"}, "reset": {"message": "Сбросить"}, "resetContainer": {"message": "Сбросить (контейнер)"}, "resetContainerTitle": {"message": "Сбросить строку User-Agent контейнера на значение по умолчанию. Это не сбросит строки User-Agent, примененные к окну. Чтобы сбросить их, нажмите кнопку 'Перезапустить'."}, "resetTitle": {"message": "Сбросить строку User-Agent браузера на значение по умолчанию. Это не сбросит строки User-Agent, примененные к окну. Чтобы сбросить их, нажмите кнопку 'Перезапустить'."}, "restart": {"message": "Перезапустить"}, "restartTitle": {"message": "Нажмите, чтобы перезапустить расширение. Это очистит все строки User-Agent, примененные к окну."}, "save": {"message": "Сохранить"}, "siblingHostnames": {"message": "Родственные домены"}, "siblingHostnamesDescription": {"message": "Массив JSON, содержащий одну или больше групп серверов с отдельной строкой User-Agent на группу. Для всех серверов в одной группе вычисление строки User-Agent проводится только один раз, все остальные члены группы используют ту же самую строку. С помощью этого можно убедиться, что група связанных веб-сайтов имеет доступ только к одной и той же строке User-Agent."}, "testUA": {"message": "Тест UA"}, "testUATitle": {"message": "Тестировать вашу строку User-Agent"}, "uaPlaceholder": {"message": "Ваша предпочитаемая строка User-Agent"}, "uaTitle": {"message": "Чтобы установить пустую строку User-Agent, используйте ключевое слово 'empty'. Чтобы составить собственную строку User-Agent, основанную на текущем объекте navigator браузера, используйте нотацию ${}. Все внутри этой нотации будет прочитано из объекта 'navigator'. На<PERSON>ример, чтобы присоединить строку к User-Agent по умолчанию, используйте '${userAgent} ПРИСОЕДИНЕННАЯ СТРОКА'"}, "userAgentSwitcherandManagerOptions": {"message": "User-Agent Switcher and Manager :: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "vendorTitle": {"message": "Это поле только для чтения. Используйте страницу настроек для парсинга вручную."}, "whiteListMode": {"message": "Режим белого списка"}, "whiteListModeDescription": {"message": "Применять установленную строку User-Agent только к вкладкам, содержащим следующие домены верхнего уровня. Важно: даже если применяемая к окну строка User-Agent установлена из всплывающего окна, данный User-Agent заменит собой глобальный."}, "ztoa": {"message": "От Z до A"}}