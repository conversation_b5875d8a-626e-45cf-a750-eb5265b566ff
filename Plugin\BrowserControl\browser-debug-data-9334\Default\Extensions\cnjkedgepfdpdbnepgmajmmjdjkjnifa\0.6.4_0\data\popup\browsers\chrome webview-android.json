[{"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi Note 5A Build/N2G47H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; RedMi Note 5 Build/RB3N5C; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/68.0.3440.91 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "68.0.3440.91", "major": "68"}, "cpu": {}, "device": {"type": "mobile", "model": "RedMi Note 5", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "68.0.3440.91"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; AFTMM Build/NS6264; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/59.0.3071.125 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "59.0.3071.125", "major": "59"}, "cpu": {}, "device": {"type": "smarttv", "model": "MM", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G950F Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "74.0.3729.157", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G930F Build/R16NW; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "74.0.3729.157", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; CPH1607 Build/MMB29M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "CPH1607", "vendor": "OPPO"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Redmi 4A Build/MMB29M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/60.0.3112.116 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "60.0.3112.116", "major": "60"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 4A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "60.0.3112.116"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; CAM-L21 Build/HUAWEICAM-L21; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/62.0.3202.84 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "62.0.3202.84", "major": "62"}, "cpu": {}, "device": {"type": "mobile", "model": "CAM-L21", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "62.0.3202.84"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi Note 5A Build/N2G47H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SM-G532M Build/MMB29T; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/55.0.2883.91 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "55.0.2883.91", "major": "55"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G532M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "55.0.2883.91"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; Nexus 5 Build/LMY48B; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/43.0.2357.65 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "43.0.2357.65", "major": "43"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus 5", "vendor": "LG"}, "engine": {"name": "Blink", "version": "43.0.2357.65"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G955F Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "74.0.3729.157", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G955F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G973F Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "74.0.3729.157", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; KFKAWI Build/NS6312; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/70.0.3538.110 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "70.0.3538.110", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "KFKAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "70.0.3538.110"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; HTC One X10 Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/61.0.3163.98 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "61.0.3163.98", "major": "61"}, "cpu": {}, "device": {"type": "mobile", "model": "One X10", "vendor": "HTC"}, "engine": {"name": "Blink", "version": "61.0.3163.98"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; HTC One X10 Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/61.0.3163.98 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "61.0.3163.98", "major": "61"}, "cpu": {}, "device": {"type": "mobile", "model": "One X10", "vendor": "HTC"}, "engine": {"name": "Blink", "version": "61.0.3163.98"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 9; SM-A920F Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.143 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "75.0.3770.143", "major": "75"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A920F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47I; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.132 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "57.0.2987.132", "major": "57"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "57.0.2987.132"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47I; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "79.0.3945.93", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47I; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.119 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.119", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47I; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.149 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47I; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.111 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "81.0.4044.111", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.111"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47I; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.138 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47I; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla_SP9 Build/LMY47D; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.87 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201604999.12451000.1)", "browser": {"name": "Chrome WebView", "version": "80.0.3987.87", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9"}, "engine": {"name": "Blink", "version": "80.0.3987.87"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla_SP9 Build/LMY47D; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.101 Mobile Safari/537.36 (Mobile; afma-sdk-a-v202510999.201004000.1)", "browser": {"name": "Chrome WebView", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C Build/MXC14G; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/51.0.2704.81 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "51.0.2704.81", "major": "51"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "51.0.2704.81"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C Build/MXC89L; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/52.0.2743.98 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "52.0.2743.98", "major": "52"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "52.0.2743.98"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; TESLA Build/AD198_D_BP331_50; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2531.0 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "48.0.2531.0", "major": "48"}, "cpu": {}, "device": {"type": "mobile", "model": "TESLA"}, "engine": {"name": "Blink", "version": "48.0.2531.0"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/54.0.2840.85 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "54.0.2840.85", "major": "54"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "54.0.2840.85"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/70.0.3538.80 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "70.0.3538.80", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "70.0.3538.80"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/73.0.3683.90 Safari/537.36 YandexSearch/8.05/apad YandexSearchBrowser/8.05", "browser": {"name": "Chrome WebView", "version": "73.0.3683.90", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.101 Mobile Safari/537.36 YandexSearch/6.45", "browser": {"name": "Chrome WebView", "version": "75.0.3770.101", "major": "75"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/78.0.3904.90 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "78.0.3904.90", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "78.0.3904.90"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "79.0.3945.136", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.117 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.117", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.117"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.132 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.132", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.149 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.117 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "81.0.4044.117", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.117"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.89 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "84.0.4147.89", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.125 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.101 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.110 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.198 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.141 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.141", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "88.0.4324.141"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.66 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "90.0.4430.66", "major": "90"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "90.0.4430.66"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla SP3.2 Lite Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.116 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "77.0.3865.116", "major": "77"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla SP3.2 Lite"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla SP3.2 Lite Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "79.0.3945.136", "major": "79"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla SP3.2 Lite"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla SP3.2 Lite Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.99 Mobile Safari/537.36 (Mobile; afma-sdk-a-v20088999.15000000.1)", "browser": {"name": "Chrome WebView", "version": "80.0.3987.99", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla SP3.2 Lite"}, "engine": {"name": "Blink", "version": "80.0.3987.99"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla SP3.2 Lite Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.149 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla SP3.2 Lite"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla SP3.2 Lite Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla SP3.2 Lite"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla SP3.2 Lite Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "84.0.4147.111", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla SP3.2 Lite"}, "engine": {"name": "Blink", "version": "84.0.4147.111"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla SP3.2 Lite Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.127 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla SP3.2 Lite"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla SP3.2 Lite Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla SP3.2 Lite"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/52.0.2743.98 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "52.0.2743.98", "major": "52"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "52.0.2743.98"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.96 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "83.0.4103.96", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.96"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla L7.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/58.0.3029.83 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "58.0.3029.83", "major": "58"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla L7.1"}, "engine": {"name": "Blink", "version": "58.0.3029.83"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla L7.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.117 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.117", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla L7.1"}, "engine": {"name": "Blink", "version": "80.0.3987.117"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla L8.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.149 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla L8.1"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla L8.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Safari/537.36 (Mobile; afma-sdk-a-v204204999.14300000.1)", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla L8.1"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla L8.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Safari/537.36 (Mobile; afma-sdk-a-v204890999.14300000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla L8.1"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/64.0.3282.137 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "64.0.3282.137", "major": "64"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3"}, "engine": {"name": "Blink", "version": "64.0.3282.137"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.101 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201004999.14300000.1)", "browser": {"name": "Chrome WebView", "version": "75.0.3770.101", "major": "75"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.119 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.119", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.198 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Lite Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.116 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "79.0.3945.116", "major": "79"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3 Lite"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Lite Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.149 Mobile Safari/537.36 (Mobile; afma-sdk-a-v20088999.12451000.1)", "browser": {"name": "Chrome WebView", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3 Lite"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Lite Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36 (Mobile; afma-sdk-a-v202006999.201604000.1)", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3 Lite"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Lite Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.81 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.202006000.1)", "browser": {"name": "Chrome WebView", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3 Lite"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/72.0.3626.121 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "72.0.3626.121", "major": "72"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "72.0.3626.121"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/78.0.3904.62 Mobile Safari/537.36 (Mobile; afma-sdk-a-v19649999.12451000.1)", "browser": {"name": "Chrome WebView", "version": "78.0.3904.62", "major": "78"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "78.0.3904.62"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/78.0.3904.108 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "78.0.3904.108", "major": "78"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.136 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "79.0.3945.136", "major": "79"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.119 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.119", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.132 Mobile Safari/537.36 (Mobile; afma-sdk-a-v20088999.20088000.1)", "browser": {"name": "Chrome WebView", "version": "80.0.3987.132", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.162 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.162", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "80.0.3987.162"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.111 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201004999.12451000.1)", "browser": {"name": "Chrome WebView", "version": "81.0.4044.111", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "81.0.4044.111"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.117 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "81.0.4044.117", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "81.0.4044.117"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.138 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.96 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "83.0.4103.96", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "83.0.4103.96"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.96 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201604999.201004000.1)", "browser": {"name": "Chrome WebView", "version": "83.0.4103.96", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "83.0.4103.96"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36 (Mobile; afma-sdk-a-v202006999.201004000.1)", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.89 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "84.0.4147.89", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.89 Mobile Safari/537.36 (Mobile; afma-sdk-a-v202006999.201004000.1)", "browser": {"name": "Chrome WebView", "version": "84.0.4147.89", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.89 Mobile Safari/537.36 (Mobile; afma-sdk-a-v202510999.19649000.1)", "browser": {"name": "Chrome WebView", "version": "84.0.4147.89", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.105 Mobile Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "84.0.4147.105", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "84.0.4147.105"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.105 Mobile Safari/537.36 (Mobile; afma-sdk-a-v202006999.12451000.1)", "browser": {"name": "Chrome WebView", "version": "84.0.4147.105", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "84.0.4147.105"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "84.0.4147.111", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "84.0.4147.111"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.125 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.125 Mobile Safari/537.36 (Mobile; afma-sdk-a-v202510999.15301000.1)", "browser": {"name": "Chrome WebView", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.81 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.81 Mobile Safari/537.36 (Mobile; afma-sdk-a-v202510999.201004000.1)", "browser": {"name": "Chrome WebView", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.127 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.127 Mobile Safari/537.36 (Mobile; afma-sdk-a-v202510999.201004000.1)", "browser": {"name": "Chrome WebView", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.75 Mobile Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "86.0.4240.75", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "86.0.4240.75"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.99 Mobile Safari/537.36 (Mobile; afma-sdk-a-v203404999.201004000.1)", "browser": {"name": "Chrome WebView", "version": "86.0.4240.99", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "86.0.4240.99"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.110 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Mobile Safari/537.36 (Mobile; afma-sdk-a-v203404999.15301000.1)", "browser": {"name": "Chrome WebView", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.66 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204102999.201604000.1)", "browser": {"name": "Chrome WebView", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Mobile Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.204102000.1)", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Mobile Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.203404000.1)", "browser": {"name": "Chrome WebView", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.141 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.141", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "88.0.4324.141"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Mobile Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.86 Mobile Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Mobile Safari/537.36 (Mobile; afma-sdk-a-v210402999.202006000.1)", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Mobile Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Mobile Safari/537.36 (Mobile; afma-sdk-a-v210402999.202006000.1)", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Mobile Safari/537.36 (Mobile; afma-sdk-a-v210402999.204204000.1)", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.93 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "79.0.3945.93", "major": "79"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.162 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.162", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "80.0.3987.162"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.89 Mobile Safari/537.36 aanewsapp android mobile", "browser": {"name": "Chrome WebView", "version": "84.0.4147.89", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.125 Mobile Safari/537.36 (Mobile; afma-sdk-a-v202510999.202006000.1)", "browser": {"name": "Chrome WebView", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.81 Mobile Safari/537.36 (Mobile; afma-sdk-a-v202510999.202006000.1)", "browser": {"name": "Chrome WebView", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.127 Mobile Safari/537.36 aanewsapp android mobile aanewsapp android mobile", "browser": {"name": "Chrome WebView", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Mobile Safari/537.36 aanewsapp android mobile", "browser": {"name": "Chrome WebView", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.66 Mobile Safari/537.36 aanewsapp android mobile", "browser": {"name": "Chrome WebView", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.203404000.1)", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Mobile Safari/537.36 aanewsapp android mobile aanewsapp android mobile", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Mobile Safari/537.36 aanewsapp android mobile", "browser": {"name": "Chrome WebView", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.203404000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.141 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.141", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "88.0.4324.141"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.141 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.204204000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.141", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "88.0.4324.141"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Mobile Safari/537.36 aanewsapp android mobile", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Mobile Safari/537.36 aanewsapp android mobile", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.86 Mobile Safari/537.36 aanewsapp android mobile", "browser": {"name": "Chrome WebView", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Mobile Safari/537.36 aanewsapp android mobile", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Mobile Safari/537.36 aanewsapp android mobile aanewsapp android mobile aanewsapp android mobile aanewsapp android mobile", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Mobile Safari/537.36 aanewsapp android mobile", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1L Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/64.0.3282.137 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "64.0.3282.137", "major": "64"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1L"}, "engine": {"name": "Blink", "version": "64.0.3282.137"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1L Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.99 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.99", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1L"}, "engine": {"name": "Blink", "version": "80.0.3987.99"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1L Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.138 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1L"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1L Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1L"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1L Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Mobile Safari/537.36 (Mobile; afma-sdk-a-v210402999.202006000.1)", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1L"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C Build/NMF26H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/62.0.3202.84 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "62.0.3202.84", "major": "62"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "62.0.3202.84"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Pixel C Build/N2G47D; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.132 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "57.0.2987.132", "major": "57"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "57.0.2987.132"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Pixel C Build/N2G47O; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.116 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "79.0.3945.116", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Pixel C Build/N2G48B; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/60.0.3112.107 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "60.0.3112.107", "major": "60"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "60.0.3112.107"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C Build/OPR1.170623.032; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; NEOSR620 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.136 Mobile Safari/537.36 com.yandex.zen/4.6.0.2379 (TeslaGroup NEOSR620; Android 8.1.0) ZenKit/1.40.6.0-internalNewdesign-Zen.7936", "browser": {"name": "Chrome WebView", "version": "79.0.3945.136", "major": "79"}, "cpu": {}, "device": {"type": "mobile", "model": "NEOSR620"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; NEOSR620 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.110 Mobile Safari/537.36 com.yandex.zen/9.1.0.9399 (TeslaGroup NEOSR620; Android 8.1.0) ZenKit/2.9.1.0-internalNewdesign-Zen.9399", "browser": {"name": "Chrome WebView", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "NEOSR620"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "75.0.3770.101", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.117 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.117", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.117"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.016; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/73.0.3683.75 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "73.0.3683.75", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "73.0.3683.75"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.026; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/65.0.3325.109 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "65.0.3325.109", "major": "65"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "65.0.3325.109"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.016.C1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.158 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "66.0.3359.158", "major": "66"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "66.0.3359.158"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.016.C1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "67.0.3396.87", "major": "67"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.016.C1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/78.0.3904.108 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "78.0.3904.108", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.D1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/71.0.3578.98 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "71.0.3578.98", "major": "71"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "71.0.3578.98"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.D1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.93 Safari/537.36 (Mobile; afma-sdk-a-v19649999.19649000.1)", "browser": {"name": "Chrome WebView", "version": "79.0.3945.93", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.D1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.127 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.Y1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/68.0.3440.91 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "68.0.3440.91", "major": "68"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "68.0.3440.91"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.Z1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/69.0.3497.100 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM7.181205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.66 Safari/537.36 Flipboard/4.2.70/5060,4.2.70.5060", "browser": {"name": "Chrome WebView", "version": "90.0.4430.66", "major": "90"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "90.0.4430.66"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.181005.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/70.0.3538.80 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "70.0.3538.80", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "70.0.3538.80"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.181105.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/71.0.3578.83 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "71.0.3578.83", "major": "71"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "71.0.3578.83"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.181105.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.181205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/70.0.3538.110 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "70.0.3538.110", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "70.0.3538.110"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.181205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/71.0.3578.98 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "71.0.3578.98", "major": "71"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "71.0.3578.98"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.181205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "75.0.3770.101", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.181205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.138 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.181205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.111 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "84.0.4147.111", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.181205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190105.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/71.0.3578.99 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "71.0.3578.99", "major": "71"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190105.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.162 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.162", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.162"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/72.0.3626.96 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "72.0.3626.96", "major": "72"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "72.0.3626.96"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/78.0.3904.90 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "78.0.3904.90", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190305.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/72.0.3626.121 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "72.0.3626.121", "major": "72"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "72.0.3626.121"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190305.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "73.0.3683.90", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190305.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "74.0.3729.157", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190305.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/76.0.3809.111 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "76.0.3809.111", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "76.0.3809.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190405.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "73.0.3683.90", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190405.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.136 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "74.0.3729.136", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.136"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190405.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.138 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190505.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "73.0.3683.90", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190505.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.136 Safari/537.36 Flipboard/4.2.14/4603,4.2.14.4603", "browser": {"name": "Chrome WebView", "version": "74.0.3729.136", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.136"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190505.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.67 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "75.0.3770.67", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "75.0.3770.67"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190505.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "78.0.3904.108", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190505.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "74.0.3729.157", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "75.0.3770.143", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.132", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "76.0.3809.132", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.92 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "77.0.3865.92", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "77.0.3865.92"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/78.0.3904.62 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "78.0.3904.62", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.62"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/78.0.3904.90 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "78.0.3904.90", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/78.0.3904.96 Safari/537.36 GSA/10.83.10.21.arm64", "browser": {"name": "Chrome WebView", "version": "78.0.3904.96", "major": "78"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.96"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/78.0.3904.108 Safari/537.36 GSA/10.87.15.21.arm64", "browser": {"name": "Chrome WebView", "version": "78.0.3904.108", "major": "78"}, "cpu": {"architecture": "arm64"}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "79.0.3945.93", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.93 Safari/537.36/tonlineApp", "browser": {"name": "Chrome WebView", "version": "79.0.3945.93", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "79.0.3945.136", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.136 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "79.0.3945.136", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.99 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.99", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.119 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.119", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.132 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "80.0.3987.132", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.149 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.96 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "81.0.4044.96", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.96"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.117 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "81.0.4044.117", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.117"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.138 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.138 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.83 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "83.0.4103.83", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.83"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.101 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "83.0.4103.101", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.89 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "84.0.4147.89", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.89 Safari/537.36 (Mobile; afma-sdk-a-v202006999.201604000.1)", "browser": {"name": "Chrome WebView", "version": "84.0.4147.89", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.89 Safari/537.36 Flipboard/4.2.48/4913,4.2.48.4913", "browser": {"name": "Chrome WebView", "version": "84.0.4147.89", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.105 Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "84.0.4147.105", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.111 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "84.0.4147.111", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.125 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.125 Safari/537.36 newsbreak/7.9.0", "browser": {"name": "Chrome WebView", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.81 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.81 Safari/537.36 Flipboard/4.2.51/4948,4.2.51.4948", "browser": {"name": "Chrome WebView", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.81 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.101 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.101 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.101 Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.101 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.101 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.101 Safari/537.36 [Pinterest/Android] [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.120 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "85.0.4183.120", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.120"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.127 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.127 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.127 Safari/537.36 Flipboard/4.2.53/4956,4.2.53.4956", "browser": {"name": "Chrome WebView", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.127 Safari/537.36 Flipboard/4.2.54/4962,4.2.54.4962", "browser": {"name": "Chrome WebView", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.75 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.75", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.75"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.99 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.99", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.99 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "86.0.4240.99", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.99 Safari/537.36 Flipboard/4.2.54/4962,4.2.54.4962", "browser": {"name": "Chrome WebView", "version": "86.0.4240.99", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.99 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "86.0.4240.99", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.110 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.110 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.110 Safari/537.36 Flipboard/4.2.55/4966,4.2.55.4966", "browser": {"name": "Chrome WebView", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.110 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.114 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.114", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.114"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.198 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.198 Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.198 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.66 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.66 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.86 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "87.0.4280.86", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Safari/537.36 Flipboard/4.2.62/4998,4.2.62.4998", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Safari/537.36 Flipboard/4.2.63/5003,4.2.63.5003", "browser": {"name": "Chrome WebView", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Safari/537.36 Flipboard/4.2.63/5003,4.2.63.5003", "browser": {"name": "Chrome WebView", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Safari/537.36 Flipboard/4.2.64/5007,4.2.64.5007", "browser": {"name": "Chrome WebView", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Safari/537.36 JsSdk/2 TopBuzz/12.7.1 NetType/WIFI", "browser": {"name": "Chrome WebView", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.141 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.141", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.141 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "88.0.4324.141", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Safari/537.36 Flipboard/4.2.64/5007,4.2.64.5007", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Safari/537.36 KPMobile/4.34.2", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Safari/537.36 Flipboard/4.2.64/5007,4.2.64.5007", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Safari/537.36 Flipboard/4.2.66/5026,4.2.66.5026", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.86 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.86 Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.86 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.86 Safari/537.36 Flipboard/4.2.67/5037,4.2.67.5037", "browser": {"name": "Chrome WebView", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.86 Safari/537.36/tonlineApp", "browser": {"name": "Chrome WebView", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Safari/537.36 (Mobile; afma-sdk-a-v210402999.202006000.1)", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Safari/537.36 (Mobile; afma-sdk-a-v210402999.204102000.1)", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Safari/537.36 (Mobile; afma-sdk-a-v210402999.204204000.1)", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Safari/537.36/tonlineApp", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Safari/537.36 (Mobile; afma-sdk-a-v210402999.201004000.1)", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Safari/537.36 (Mobile; afma-sdk-a-v210402999.202510000.1)", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Safari/537.36 Flipboard/4.2.68/5046,4.2.68.5046", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Safari/537.36 Flipboard/4.2.70/5060,4.2.70.5060", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Safari/537.36/tonlineApp", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.66 Safari/537.36 Flipboard/4.2.70/5060,4.2.70.5060", "browser": {"name": "Chrome WebView", "version": "90.0.4430.66", "major": "90"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "90.0.4430.66"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_L8_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/44.0.2403.119 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "44.0.2403.119", "major": "44"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla_L8_2"}, "engine": {"name": "Blink", "version": "44.0.2403.119"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_L8_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.119 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "80.0.3987.119", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla_L8_2"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_L8_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.83 Safari/537.36 [Pinterest/Android]", "browser": {"name": "Chrome WebView", "version": "83.0.4103.83", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla_L8_2"}, "engine": {"name": "Blink", "version": "83.0.4103.83"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "66.0.3359.126", "major": "66"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "66.0.3359.126"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/68.0.3440.91 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "68.0.3440.91", "major": "68"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "68.0.3440.91"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.101 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "75.0.3770.101", "major": "75"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.92 Mobile Safari/537.36 relesys_web_client/1.3.6.0 (RelesysApp/1.3.33; net.relesysapp.universal)", "browser": {"name": "Chrome WebView", "version": "77.0.3865.92", "major": "77"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "77.0.3865.92"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.116 Mobile Safari/537.36 relesys_web_client/1.3.6.0 (RelesysApp/1.3.33; net.relesysapp.universal)", "browser": {"name": "Chrome WebView", "version": "77.0.3865.116", "major": "77"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.132 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.132", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.125 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.99 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.99", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "86.0.4240.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.198 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.198 Mobile Safari/537.36 NSTNWV/3.18.342287288.release.go", "browser": {"name": "Chrome WebView", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/68.0.3440.91 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204000.204204000.0)", "browser": {"name": "Chrome WebView", "version": "68.0.3440.91", "major": "68"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "68.0.3440.91"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "84.0.4147.111", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "84.0.4147.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.204102000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.72 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204890999.202006000.1)", "browser": {"name": "Chrome WebView", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Mobile Safari/537.36 (Mobile; afma-sdk-a-v210402999.201004000.1)", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.92 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "77.0.3865.92", "major": "77"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "77.0.3865.92"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.117 Mobile Safari/537.36 (Mobile; afma-sdk-a-v20088999.15601000.1)", "browser": {"name": "Chrome WebView", "version": "80.0.3987.117", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "80.0.3987.117"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.132 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "80.0.3987.132", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.132 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201004999.15000000.1)", "browser": {"name": "Chrome WebView", "version": "80.0.3987.132", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.149 Mobile Safari/537.36 (Mobile; afma-sdk-a-v20088999.15601000.1)", "browser": {"name": "Chrome WebView", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.149 Mobile Safari/537.36 (Mobile; afma-sdk-a-v20088999.19649000.1)", "browser": {"name": "Chrome WebView", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.162 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201004999.15601000.1)", "browser": {"name": "Chrome WebView", "version": "80.0.3987.162", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "80.0.3987.162"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.111 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201004999.12451000.1)", "browser": {"name": "Chrome WebView", "version": "81.0.4044.111", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "81.0.4044.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.117 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201004999.15000000.1)", "browser": {"name": "Chrome WebView", "version": "81.0.4044.117", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "81.0.4044.117"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.117 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201004999.20088000.1)", "browser": {"name": "Chrome WebView", "version": "81.0.4044.117", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "81.0.4044.117"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.117 Mobile Safari/537.36 PBIAB Android 20.04.10541 (680047815)", "browser": {"name": "Chrome WebView", "version": "81.0.4044.117", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "81.0.4044.117"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.138 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.138 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201004999.15601000.1)", "browser": {"name": "Chrome WebView", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.83 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201004999.15601000.1)", "browser": {"name": "Chrome WebView", "version": "83.0.4103.83", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "83.0.4103.83"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.96 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201604999.201004000.1)", "browser": {"name": "Chrome WebView", "version": "83.0.4103.96", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "83.0.4103.96"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201604999.201004000.1)", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36 (Mobile; afma-sdk-a-v201604999.201604000.1)", "browser": {"name": "Chrome WebView", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "84.0.4147.111", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "84.0.4147.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.125 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.101 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.101 Mobile Safari/537.36 (Mobile; afma-sdk-a-v202510999.201004000.1)", "browser": {"name": "Chrome WebView", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.127 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.75 Mobile Safari/537.36 (Mobile; afma-sdk-a-v203404999.202006000.1)", "browser": {"name": "Chrome WebView", "version": "86.0.4240.75", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "86.0.4240.75"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.110 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.198 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.198 Mobile Safari/537.36;native-20minDE;app-android-smartphone-2", "browser": {"name": "Chrome WebView", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.86 Mobile Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "87.0.4280.86", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "87.0.4280.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.203404000.1)", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Mobile Safari/537.36 EdgW/1.0", "browser": {"name": "Chrome WebView", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.202006000.1)", "browser": {"name": "Chrome WebView", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.203404000.1)", "browser": {"name": "Chrome WebView", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.204102000.1)", "browser": {"name": "Chrome WebView", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.12451000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.15000000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.202510000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.204102000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.203404000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.204102000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204890999.15000000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204204999.202006000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204890999.19649000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Mobile Safari/537.36 (Mobile; afma-sdk-a-v204890999.204102000.1)", "browser": {"name": "Chrome WebView", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Mobile Safari/537.36 (Mobile; afma-sdk-a-v210402999.201004000.1)", "browser": {"name": "Chrome WebView", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X900 Pro Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.158 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "66.0.3359.158", "major": "66"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X900 Pro"}, "engine": {"name": "Blink", "version": "66.0.3359.158"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C Build/RQ2A.210305.006; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C Build/RQ2A.210305.006; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Safari/537.36 5.122092", "browser": {"name": "Chrome WebView", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi Note 5A Build/N2G47H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi Note 5A Build/N2G47H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; GM8 go Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "67.0.3396.87", "major": "67"}, "cpu": {}, "device": {"type": "mobile", "model": "GM8 go"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi Note 5A Build/N2G47H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; moto g stylus 5G Build/RRE31.Q2-11-52-4; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/98.0.4758.87 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "98.0.4758.87", "major": "98"}, "cpu": {}, "device": {"type": "mobile", "model": "moto g stylus 5G", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "98.0.4758.87"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G973F Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.138 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi Note 5A Build/N2G47H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi Note 5A Build/N2G47H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi Note 5A Build/N2G47H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi Note 5A Build/N2G47H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi Note 5A Build/N2G47H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36 uacq", "browser": {"name": "Chrome WebView", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 14; SM-X110 Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.179 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "124.0.6367.179", "major": "124"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-X110", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "124.0.6367.179"}, "os": {"name": "Android", "version": "14"}}, {"ua": "Mozilla/5.0 (Linux; Android 14; SM-X110 Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.179 Safari/537.36", "browser": {"name": "Chrome WebView", "version": "124.0.6367.179", "major": "124"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-X110", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "124.0.6367.179"}, "os": {"name": "Android", "version": "14"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; TECNO BE7 Build/RP1A.200720.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/127.0.6533.103 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "127.0.6533.103", "major": "127"}, "cpu": {}, "device": {"type": "mobile", "model": "BE7", "vendor": "TECNO"}, "engine": {"name": "Blink", "version": "127.0.6533.103"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Redmi Note 9S Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/127.0.6533.103 Mobile Safari/537.36", "browser": {"name": "Chrome WebView", "version": "127.0.6533.103", "major": "127"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 9S", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "127.0.6533.103"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; HD1900 Build/QKQ1.190716.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.156 Mobile Safari/537.36  aweme_230400 JsSdk/1.0 NetType/WIFI  AppName/aweme app_version/23.4.0 ByteLocale/zh-CN Region/CN AppSkin/white AppTheme/light BytedanceWebview/d8a21c6 WebView/075113004008", "browser": {"name": "Chrome WebView", "version": "75.0.3770.156", "major": "75"}, "cpu": {}, "device": {"type": "mobile", "model": "HD1900"}, "engine": {"name": "Blink", "version": "75.0.3770.156"}, "os": {"name": "Android", "version": "10"}}]