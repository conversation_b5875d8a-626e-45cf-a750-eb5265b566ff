[{"ua": "Mozilla/5.0 (Linux; Android 4.4.2; DEXP Ixion ML2 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "DEXP Ixion ML2 5"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; GT-I9301I) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "GT-I9301I", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; GT-P5210) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "GT-P5210", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; LG-V500) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "V500", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; SM-G900F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G900F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; SM-T320) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T320", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.4; D2302) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "D2302", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "4.4.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.4; Nexus 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Nexus 7", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "4.4.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.4; SM-T560) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T560", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "4.4.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0.1; GT-I9505) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "GT-I9505", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0.1; YOGA Tablet 2-1050F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "YOGA Tablet 2-1050F"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0.2; LIFETAB_S1034X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "LIFETAB_S1034X"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.0.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0.2; SM-G850F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G850F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.0.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0.2; SM-T530) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T530", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.0.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0; SM-G900F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G900F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; A37f) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A37f"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; SGP311) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Xperia Tablet", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; SM-G531F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G531F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; SM-J320H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J320H", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; T6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "T6"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; A0001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A0001", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; D6603) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "D6603", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Lenovo TB2-X30F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "TB2-X30F", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; MotoG3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MotoG3", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Nexus 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Nexus 7", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; ONE E1003) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "E1003", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Redmi 4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 4", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SGP511) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Xperia Tablet", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SM-A500FU) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A500FU", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SM-G900F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G900F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SM-G903F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G903F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SM-J106F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J106F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SM-J500FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J500FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SM-N910V) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N910V", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SM-T800) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T800", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; STV100-3) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "STV100-3", "vendor": "BlackBerry"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; CAPTIVA) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "CAPTIVA"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; HUAWEI GRA-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": " GRA-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; LG-H815) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H815", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Lenovo TB3-X70F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "TB3-X70F", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; PLK-L01) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "PLK-L01", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; T08) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "T08"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; AGS-W09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "AGS-W09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; ASUS_Z012D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Z012D", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; BLL-L22) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "BLL-L22", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; BTV-W09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "BTV-W09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; EVA-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "EVA-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; FRD-L19) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "FRD-L19", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; HUAWEI VNS-L31) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": " VNS-L31", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; K10000 Max) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "K10000 Max"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; LG-H840) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H840", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; LG-M430) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "M430", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; LGMP450) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LGMP450"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Lenovo P2a42) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "P2a42", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; MHA-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MHA-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Moto C Plus) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto C Plus", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Moto G (5)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G (5)", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; P027) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "P027"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Redmi Note 4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 4", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG-SM-G890A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G890A", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SHIELD Tablet K1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SHIELD Tablet K1"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-A310F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A310F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-A520F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A520F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-G920F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G920F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-G920V) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G920V", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-G928F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G928F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-G935F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G935F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-G955F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G955F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-J327T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J327T", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-T713) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T713", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-T813) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T813", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-T819) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T819", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; TRT-LX2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "TRT-LX2", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Chromebook 14 (CB3-431)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Chromebook 14 (CB3-431)"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; E6633) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "E6633", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; E6853) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "E6853", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; G8141) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "G8141", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Lenovo TB-X704L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "TB-X704L", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Lenovo YT-X703L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "YT-X703L", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Moto E (4) Plus) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto E (4) Plus", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Moto G Play) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G Play", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Nexus 9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Nexus 9", "vendor": "HTC"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SGP712) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Xperia Tablet", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SM-J250G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J250G", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SM-J510FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J510FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SM-T555) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T555", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; ZTE A2017G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A2017G", "vendor": "ZTE"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; ZTE BLADE A0620) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "BLADE A0620", "vendor": "ZTE"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; FP2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "FP2", "vendor": "Fairphone"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; LIFETAB_P970X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "LIFETAB_P970X"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi 4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 4", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi 4X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 4X", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi 5A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 5A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Redmi Note 5A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; AGS2-W09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "AGS2-W09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; ANE-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "ANE-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; ANE-LX2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "ANE-LX2", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; ANE-LX2J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "ANE-LX2J"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; ASUS_Z01HD) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Z01HD", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; ASUS_Z01RD) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Z01RD", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; ASUS_Z017D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Z017D", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; ATU-L21) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "ATU-L21", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; AUM-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "AUM-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; AUM-L41) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "AUM-L41", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; BND-L21) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "BND-L21", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; BND-L34) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "BND-L34"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; CMR-AL09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "CMR-AL09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; F5121) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "F5121", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; F5121) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "F5121", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; F5321) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "F5321", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; F8331) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "F8331", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; FIG-LA1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "FIG-LA1"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; FIG-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "FIG-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; FRD-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "FRD-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; G3112) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "G3112", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; G3221) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "G3221", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; G3412) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "G3412", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; G8231) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "G8231", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; G8341) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "G8341", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; G8441) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "G8441", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; H8314) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H8314"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; HTC 10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "10", "vendor": "HTC"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; HTC U11) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "U11", "vendor": "HTC"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; HTC U12+) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "U12", "vendor": "HTC"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; HTC U Ultra) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "U Ultra", "vendor": "HTC"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Hi9Air) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Hi9Air"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LG-H831) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H831", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LG-H850) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H850", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LG-H870) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H870", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LG-H870DS) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H870DS", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LG-H872) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H872", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LG-H873) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H873", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LG-H930) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H930", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LG-H990) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H990", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LG-LS993) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LS993", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LG-US998) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "US998", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LLD-L31) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LLD-L31", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LM-G710) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LM-G710", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LON-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LON-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; MI 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MI 5", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; MI 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MI 6", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Mi MIX 2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi MIX 2"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Brave/1.0.5.9 Chromium/69.0.3497.100 Mobile Safari/537.36", "browser": {"name": "Brave", "version": "1.0.5.9", "major": "1"}, "cpu": {}, "device": {"type": "mobile", "model": "Mobile"}, "engine": {"name": "WebKit", "version": "537.36"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Moto Z2 Play) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto Z2 Play", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Moto Z (2)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto Z (2)", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Nexus 5X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus 5", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; ONEPLUS A3000) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A3000", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; ONEPLUS A3003) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A3003", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; PRA-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "PRA-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; PRA-LX2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "PRA-LX2", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; RNE-L21) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "RNE-L21", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; S41) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "S41"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG-SM-G891A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G891A", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG-SM-G930A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930A", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SHIELD Android TV) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "smarttv"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SHT-W09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SHT-W09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-A320FL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A320FL", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-A520F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A520F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-A530F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A530F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-A530W) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A530W", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-A600FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A600FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G930F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G930P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930P", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G930T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930T", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G930U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G930V) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930V", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G930VL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930VL", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G935F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G935F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G935T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G935T", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G950F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G950U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G955F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G955F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G955U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G960F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965U1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-J330F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J330F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-J337V) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J337V", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-N950F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N950F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-S767VL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-S767VL", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-T820) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T820", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-T825) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T825", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; STF-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "STF-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; VS988) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "VS988"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; VS995) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "VS995"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; VTR-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "VTR-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; WAS-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "WAS-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; WAS-LX1A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "WAS-LX1A", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; XT1635-01) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "XT1635-01"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; XT1635-02) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "XT1635-02"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; XT1650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "XT1650", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; moto e5 cruise) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "moto e5 cruise", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; moto e5 play) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "moto e5 play", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; moto e5 plus) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "moto e5 plus", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; moto g(6) (XT1925DL)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "moto g(6) (XT1925DL)", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; moto g(6)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "moto g(6)", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; ASUS_X00TD) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "X00TD", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; BBF100-2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "BBF100-2", "vendor": "BlackBerry"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; BBF100-6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "BBF100-6", "vendor": "BlackBerry"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; CLT-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "CLT-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; COL-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "COL-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "G"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; LG-M700) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "M700", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; LG-Q710AL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Q710AL", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; LM-Q710(FGN)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LM-Q710(FGN)"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; LM-V405) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LM-V405", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; LM-X210(G)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LM-X210(G)"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; LM-X212(G)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LM-X212(G)"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; LM-X410(FG)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LM-X410(FG)"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Lenovo TB-X304F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "TB-X304F", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; MI MAX 3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MI MAX 3", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; MI PAD 4 PLUS) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "MI PAD 4 PLUS", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; MI PAD 4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "MI PAD 4", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Moto G (4)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G (4)", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Moto G (5) Plus) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G (5) Plus", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Moto G (5)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G (5)", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Moto G (5S) Plus) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G (5S) Plus", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Moto G (5S)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G (5S)", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Nexus 5X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus 5", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Nexus 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus 6", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Nexus 6P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus 6P", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; ONEPLUS A5010) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A5010", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Phone) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Phone"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel XL", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Power_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Power_5"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; QS5509A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "QS5509A"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Redmi 5 Plus) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 5 Plus", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Redmi 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 5", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Redmi 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 6", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Redmi 6A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 6A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Redmi Note 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Redmi S2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi S2", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; S61) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "S61"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; S290) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "S290"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG-SM-J327A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J327A", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-G390F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G390F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-G610F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G610F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-J530F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J530F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-J701F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J701F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-J710F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J710F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-N960F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T380) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T380", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T580) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T580", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T585) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T585", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T590) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T590", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SNE-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SNE-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SNE-LX1) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SNE-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Xperia XA2 Dual (AOSP)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Xperia XA2 Dual (AOSP)"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; vivo 1806) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "1806", "vendor": "Vivo"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; A0001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A0001", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ALP-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "ALP-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ANE-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "ANE-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ASUS_I01WD) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "I01WD", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ASUS_X00QD) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "X00QD", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ASUS_X00TD) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "X00TD", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ASUS_Z01RD) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Z01RD", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; BKL-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "BKL-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; BLA-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "BLA-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; BLA-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "BLA-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; CLT-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "CLT-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; CLT-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "CLT-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; CMR-W09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "CMR-W09"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; COL-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "COL-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; COR-AL00) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "COR-AL00", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; COR-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "COR-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ELE-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "ELE-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; EML-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "EML-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; EML-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "EML-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; EML-L29) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "EML-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; FIG-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "FIG-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; G8141) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "G8141", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; G8341) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "G8341", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; G8441) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "G8441", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; G8441) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "G8441", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; GM1913) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "GM1913"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; GM1915) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "GM1915"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; GM1917) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "GM1917"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; H8216) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H8216"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; H8266) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H8266"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; H8324) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "H8324"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; HMA-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "HMA-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; HRY-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "HRY-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; JSN-L21) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "JSN-L21", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; LM-G710VM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LM-G710VM", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; LM-G820) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LM-G820", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; LM-V405) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LM-V405", "vendor": "LG"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; LON-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LON-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; LYA-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LYA-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; LYA-L29) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "LYA-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; MAR-LX1A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MAR-LX1A", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; MHA-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MHA-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; MI 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MI 6", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; MI 8) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MI 8", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; MI 9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MI 9", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; MI MAX 3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MI MAX 3", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; MIX 2S) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MIX 2S"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; MRD-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MRD-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; MRD-LX2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "MRD-LX2", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Mi 9 SE) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi 9 SE", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Mi A1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi A1", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Mi A2 Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi A2 Lite", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Mi A2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi A2", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Mi MIX 2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi MIX 2"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Mi MIX 2S) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi MIX 2S"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Mi MIX 3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi MIX 3"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Moto Z3 Play) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto Z3 Play", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Nexus 6P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus 6P", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Nokia 3.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "3.1", "vendor": "Nokia"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Nokia 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "6.1", "vendor": "Nokia"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Nokia 7 plus) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "7", "vendor": "Nokia"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Nokia 7.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "7.1", "vendor": "Nokia"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Nokia 8 Sirocco) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "8", "vendor": "Nokia"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ONE A2003) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A2003", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ONEPLUS A3000) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A3000", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ONEPLUS A3003) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A3003", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ONEPLUS A5000) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A5000", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ONEPLUS A5010) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A5010", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ONEPLUS A6000) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A6000", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ONEPLUS A6003) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A6003", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ONEPLUS A6003) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A6003", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ONEPLUS A6013) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "A6013", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; PCT-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "PCT-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; PH-1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "PH-1"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; POCO F1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "POCO F1", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; POCOPHONE F1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "POCOPHONE F1", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; POCOPHONE F1) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "POCOPHONE F1", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel 2 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 2 XL", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel 2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 2", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel 3 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3 XL", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel 3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel 3) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel 3a XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3a XL", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel 3a) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3a", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel XL", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Redmi Note 4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 4", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Redmi Note 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Redmi Note 6 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 6 Pro", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Redmi Note 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 7", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-A405FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A405FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-A505F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-A505FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-A530F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A530F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-A530W) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A530W", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-A600FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A600FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-A705FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A705FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-A705MN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A705MN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-A730F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A730F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-A750FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A750FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G950F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G950U1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950U1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G950U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G950W) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950W", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G955F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G955F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G955U1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G955U1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G955U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G955U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G960F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G960F) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G960U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G965F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G965F) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G965U1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965U1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G965U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G965W) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965W", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G970F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G970F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G970U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G970U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G973U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G975F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G975U1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G975U1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G975U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G975U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G9750) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G9750", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-J400F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J400F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-J415FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J415FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-J737V) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J737V", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-N950F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N950F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-N950U1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N950U1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-N950U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N950U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-N960F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-N960U1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N960U1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-N960U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N960U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-S367VL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-S367VL", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-T510) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T510", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-T720) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T720", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-T830) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T830", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SNE-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SNE-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; STF-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "STF-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel 2 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 2 XL", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel 3 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3 XL", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel 3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel 3a XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3a XL", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A505FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G960F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G960F) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G960U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G965F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G970U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G970U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G975U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G975U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; ASUS_X00TD) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 Brave/74", "browser": {"name": "Brave", "version": "74", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "X00TD", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}]