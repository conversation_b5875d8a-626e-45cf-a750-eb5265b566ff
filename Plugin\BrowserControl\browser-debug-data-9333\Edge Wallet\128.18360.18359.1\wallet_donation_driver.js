!function(){"use strict";let e,t;!function(e){e.checkCanOpenEdgeWalletDonation="checkCanOpenEdgeWalletDonation",e.openEdgeWalletDonation="openEdgeWalletDonation",e.openEdgeWallet="openEdgeWallet"}(e||(e={})),function(e){e.CheckEdgeWalletStatus="CheckEdgeWalletStatus"}(t||(t={}));window.edgeWalletDonationRuntime=new class{raiseMessageFromHost(e){const a=e.shift();if(a&&a===t.CheckEdgeWalletStatus)try{const t=e&&e[0]?JSON.parse(e[0]):{};window.postMessage({...t,type:a})}catch(e){}}postMessageToHost(e,t){try{"function"==typeof edgeWalletDonationNativeHandler?.sendMessageToHost&&edgeWalletDonationNativeHandler.sendMessageToHost(e,t)}catch(e){}}initialize(){return!0}},window.addEventListener("message",(function(t){if(t?.data?.type==e.openEdgeWallet){const e=[];e.push(t?.data?.path??""),e.push(t?.data?.inNewTab??"1"),window.edgeWalletDonationRuntime.postMessageToHost(t.data.type,e)}else if(t?.data?.type==e.openEdgeWalletDonation){const e=[];e.push(t?.data?.id??""),window.edgeWalletDonationRuntime.postMessageToHost(t.data.type,e)}else t?.data?.type==e.checkCanOpenEdgeWalletDonation&&window.edgeWalletDonationRuntime.postMessageToHost(t.data.type,[])}))}();