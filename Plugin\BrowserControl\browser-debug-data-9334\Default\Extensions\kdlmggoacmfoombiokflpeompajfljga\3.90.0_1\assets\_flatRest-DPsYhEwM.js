import{f as t}from"./map-CPMUsqym.js";import{s as n,o as r}from"./user-config-BrqC82sm.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},d=new Error().stack;d&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[d]="e08bf25e-c448-4a14-a5d2-33dcd5ead18e",e._sentryDebugIdIdentifier="sentry-dbid-e08bf25e-c448-4a14-a5d2-33dcd5ead18e")}catch{}})();function a(e){return n(r(e,void 0,t),e+"")}export{a as f};
//# sourceMappingURL=_flatRest-DPsYhEwM.js.map
