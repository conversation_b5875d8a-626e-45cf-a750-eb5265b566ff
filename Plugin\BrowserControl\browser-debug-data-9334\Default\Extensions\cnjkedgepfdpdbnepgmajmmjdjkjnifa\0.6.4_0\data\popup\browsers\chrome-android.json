[{"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Lenovo B8080-F Build/KVT49L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.137 Safari/537.36", "browser": {"name": "Chrome", "version": "64.0.3282.137", "major": "64"}, "cpu": {}, "device": {"type": "mobile", "model": "B8080-F", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "64.0.3282.137"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.2.2; Tesla TTH7 Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Safari/537.36", "browser": {"name": "Chrome", "version": "39.0.2171.93", "major": "39"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla TTH7"}, "engine": {"name": "Blink", "version": "39.0.2171.93"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.92 Safari/537.36", "browser": {"name": "Chrome", "version": "43.0.2357.92", "major": "43"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "43.0.2357.92"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "browser": {"name": "Chrome", "version": "56.0.2924.87", "major": "56"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "56.0.2924.87"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.71 Safari/537.36", "browser": {"name": "Chrome", "version": "63.0.3239.71", "major": "63"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "63.0.3239.71"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36", "browser": {"name": "Chrome", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.99 Safari/537.36", "browser": {"name": "Chrome", "version": "71.0.3578.99", "major": "71"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.136 Safari/537.36", "browser": {"name": "Chrome", "version": "74.0.3729.136", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "74.0.3729.136"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.62 Safari/537.36", "browser": {"name": "Chrome", "version": "78.0.3904.62", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "78.0.3904.62"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.116 Safari/537.36", "browser": {"name": "Chrome", "version": "79.0.3945.116", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.119 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.119", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.111 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.111", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.111"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.4; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "4.4.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4; Pixel C Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.100 Safari/537.36", "browser": {"name": "Chrome", "version": "76.0.3809.100", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "76.0.3809.100"}, "os": {"name": "Android", "version": "4.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0.2) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.182 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.182", "major": "88"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "88.0.4324.182"}, "os": {"name": "Android", "version": "5.0.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0; Pixel C Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.86 Safari/537.36", "browser": {"name": "Chrome", "version": "73.0.3683.86", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "73.0.3683.86"}, "os": {"name": "Android", "version": "5.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0; Pixel C Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36", "browser": {"name": "Chrome", "version": "77.0.3865.90", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "77.0.3865.90"}, "os": {"name": "Android", "version": "5.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.182 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.182", "major": "88"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "88.0.4324.182"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.111 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.111", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "86.0.4240.111"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Pixel C Build/LMY48Z) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36", "browser": {"name": "Chrome", "version": "72.0.3626.121", "major": "72"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "72.0.3626.121"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Pixel C Build/LMY48Z) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.100 Safari/537.36", "browser": {"name": "Chrome", "version": "76.0.3809.100", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "76.0.3809.100"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.85 Safari/537.36", "browser": {"name": "Chrome", "version": "54.0.2840.85", "major": "54"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "54.0.2840.85"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.116 Safari/537.36", "browser": {"name": "Chrome", "version": "79.0.3945.116", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.119 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.119", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.111 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.111", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.111"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.66 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.141 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.141", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "88.0.4324.141"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; X101) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "X101"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; JynxBox) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.81 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "JynxBox"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; MIDS145PXE) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.99 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.99", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "MIDS145PXE"}, "engine": {"name": "Blink", "version": "86.0.4240.99"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C Build/M5C14J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.2403.133 Safari/537.36", "browser": {"name": "Chrome", "version": "44.0.2403.133", "major": "44"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "44.0.2403.133"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C Build/MXC89F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.2403.133 Safari/537.36", "browser": {"name": "Chrome", "version": "44.0.2403.133", "major": "44"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "44.0.2403.133"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C Build/MXC89H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Safari/537.36", "browser": {"name": "Chrome", "version": "55.0.2883.91", "major": "55"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "55.0.2883.91"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.136 Safari/537.36", "browser": {"name": "Chrome", "version": "74.0.3729.136", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.136"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Chrome", "version": "79.0.3945.136", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.132", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.60 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.60", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.60"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.81 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.110 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.181 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; R9PLUS) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "R9PLUS"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; P00C) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "P00C", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Pixel C Build/MOB31E) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.131 Safari/537.36", "browser": {"name": "Chrome", "version": "74.0.3729.131", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.131"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Pixel C Build/MOB31E) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.70 Safari/537.36", "browser": {"name": "Chrome", "version": "78.0.3904.70", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.70"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Chrome", "version": "73.0.3683.90", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Chrome", "version": "78.0.3904.108", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; AGS-W09) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "AGS-W09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; BAH-W09) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "BAH-W09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Chrome", "version": "67.0.3396.87", "major": "67"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C Build/NRD91D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.124 Safari/537.36", "browser": {"name": "Chrome", "version": "53.0.2785.124", "major": "53"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "53.0.2785.124"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C Build/NRD91D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Safari/537.36", "browser": {"name": "Chrome", "version": "70.0.3538.110", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "70.0.3538.110"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C Build/NRD91D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.131 Safari/537.36", "browser": {"name": "Chrome", "version": "74.0.3729.131", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.131"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C Build/NRD91D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36", "browser": {"name": "Chrome", "version": "77.0.3865.90", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "77.0.3865.90"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C Build/NRD91N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.85 Safari/537.36", "browser": {"name": "Chrome", "version": "54.0.2840.85", "major": "54"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "54.0.2840.85"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.116 Safari/537.36", "browser": {"name": "Chrome", "version": "77.0.3865.116", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.132", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.96 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.96", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.96"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-T813) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.88 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.88", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T813", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.88"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.182 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.182", "major": "88"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "88.0.4324.182"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Lenovo YB1-X90L) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "YB1-X90L", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C Build/N4F26I) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Safari/537.36", "browser": {"name": "Chrome", "version": "55.0.2883.91", "major": "55"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "55.0.2883.91"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C Build/N4F26T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "browser": {"name": "Chrome", "version": "56.0.2924.87", "major": "56"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "56.0.2924.87"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C Build/NMF26H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.85 Safari/537.36", "browser": {"name": "Chrome", "version": "54.0.2840.85", "major": "54"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "54.0.2840.85"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C Build/NMF26H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.137 Safari/537.36", "browser": {"name": "Chrome", "version": "64.0.3282.137", "major": "64"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "64.0.3282.137"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.99 Safari/537.36", "browser": {"name": "Chrome", "version": "71.0.3578.99", "major": "71"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36", "browser": {"name": "Chrome", "version": "74.0.3729.157", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.132 Safari/537.36", "browser": {"name": "Chrome", "version": "76.0.3809.132", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.90 Safari/537.36", "browser": {"name": "Chrome", "version": "78.0.3904.90", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.90"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.87 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.87", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.87"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.125 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.93 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.86 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; SM-T550) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.67 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.67", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T550", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.67"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.182 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.182", "major": "88"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "88.0.4324.182"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Pixel C Build/N2G47O) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.2987.132 Safari/537.36", "browser": {"name": "Chrome", "version": "57.0.2987.132", "major": "57"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "57.0.2987.132"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Pixel C Build/N2G47W) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.125 Safari/537.36", "browser": {"name": "Chrome", "version": "59.0.3071.125", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "59.0.3071.125"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Pixel C Build/N2G48B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.126 Safari/537.36", "browser": {"name": "Chrome", "version": "66.0.3359.126", "major": "66"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "66.0.3359.126"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Chrome", "version": "73.0.3683.90", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Chrome", "version": "79.0.3945.93", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.105 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.105", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.105"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.101 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; TESLA MediaBox QX4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.96 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.96", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox QX4"}, "engine": {"name": "Blink", "version": "83.0.4103.96"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; TESLA MediaBox QX4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox QX4"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; TESLA MediaBox QX8 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.112 Safari/537.36", "browser": {"name": "Chrome", "version": "74.0.3729.112", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox QX8 Pro"}, "engine": {"name": "Blink", "version": "74.0.3729.112"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; TESLA MediaBox QX8 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox QX8 Pro"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; TESLA MediaBox QX8 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox QX8 Pro"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.119 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.119", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.83 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.83", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "83.0.4103.83"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.111 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.111", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "84.0.4147.111"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.120 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.120", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "85.0.4183.120"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.99 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.99", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "86.0.4240.99"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.185 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.86 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.86", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "87.0.4280.86"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1; Pixel C Build/N2G47H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Safari/537.36", "browser": {"name": "Chrome", "version": "70.0.3538.110", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "70.0.3538.110"}, "os": {"name": "Android", "version": "7.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1; Pixel C Build/N2G47H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.86 Safari/537.36", "browser": {"name": "Chrome", "version": "73.0.3683.86", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "73.0.3683.86"}, "os": {"name": "Android", "version": "7.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1; Pixel C Build/N2G47H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.90 Safari/537.36", "browser": {"name": "Chrome", "version": "75.0.3770.90", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "75.0.3770.90"}, "os": {"name": "Android", "version": "7.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1; Pixel C Build/N2G47H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36", "browser": {"name": "Chrome", "version": "77.0.3865.90", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "77.0.3865.90"}, "os": {"name": "Android", "version": "7.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1; Pixel C Build/N2G47H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36", "browser": {"name": "Chrome", "version": "79.0.3945.88", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.88"}, "os": {"name": "Android", "version": "7.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.182 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.182", "major": "88"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "88.0.4324.182"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; AGS2-W09) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "AGS2-W09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; AGS-W09) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "AGS-W09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; BAH2-L09) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "BAH2-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; BAH2-W19) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "BAH2-W19", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; BAH2-W19) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "BAH2-W19", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Google Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.105 Safari/537.36", "browser": {"name": "Chrome", "version": "72.0.3626.105", "major": "72"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "72.0.3626.105"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C Build/OPP3.170518.006) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C Build/OPP3.170518.006) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.66 Safari/537.36", "browser": {"name": "Chrome", "version": "90.0.4430.66", "major": "90"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "90.0.4430.66"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C Build/OPR1.170623.026) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.98 Safari/537.36", "browser": {"name": "Chrome", "version": "61.0.3163.98", "major": "61"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "61.0.3163.98"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C Build/OPR1.170623.027) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.98 Safari/537.36", "browser": {"name": "Chrome", "version": "61.0.3163.98", "major": "61"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "61.0.3163.98"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C Build/OPR1.170623.032) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.84 Safari/537.36", "browser": {"name": "Chrome", "version": "62.0.3202.84", "major": "62"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "62.0.3202.84"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C Build/OPR1.170623.032) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.70 Safari/537.36", "browser": {"name": "Chrome", "version": "68.0.3440.70", "major": "68"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "68.0.3440.70"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C Build/OPR6.170623.010) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Safari/537.36", "browser": {"name": "Chrome", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C Build/OPR6.170623.010) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.109 Safari/537.36", "browser": {"name": "Chrome", "version": "65.0.3325.109", "major": "65"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "65.0.3325.109"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.136 Safari/537.36", "browser": {"name": "Chrome", "version": "74.0.3729.136", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.136"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.67 Safari/537.36", "browser": {"name": "Chrome", "version": "75.0.3770.67", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "75.0.3770.67"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.143 Safari/537.36", "browser": {"name": "Chrome", "version": "75.0.3770.143", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "75.0.3770.143"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Chrome", "version": "78.0.3904.108", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Chrome", "version": "79.0.3945.136", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.132", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.162", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.162"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.96 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.96", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.96"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.89 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.89", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.125 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.101 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.181 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.182 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.182", "major": "88"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "88.0.4324.182"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.90 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.98 Safari/537.36", "browser": {"name": "Chrome", "version": "61.0.3163.98", "major": "61"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "61.0.3163.98"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.107 Safari/537.36", "browser": {"name": "Chrome", "version": "63.0.3239.107", "major": "63"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "63.0.3239.107"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.109 Safari/537.36", "browser": {"name": "Chrome", "version": "65.0.3325.109", "major": "65"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "65.0.3325.109"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Chrome", "version": "67.0.3396.87", "major": "67"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.015) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.123 Safari/537.36", "browser": {"name": "Chrome", "version": "64.0.3282.123", "major": "64"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "64.0.3282.123"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.015) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Chrome", "version": "67.0.3396.87", "major": "67"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.016) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.137 Safari/537.36", "browser": {"name": "Chrome", "version": "64.0.3282.137", "major": "64"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "64.0.3282.137"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.016) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.91 Safari/537.36", "browser": {"name": "Chrome", "version": "68.0.3440.91", "major": "68"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "68.0.3440.91"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.022.A1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.84 Safari/537.36", "browser": {"name": "Chrome", "version": "62.0.3202.84", "major": "62"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "62.0.3202.84"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.026) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.109 Safari/537.36", "browser": {"name": "Chrome", "version": "65.0.3325.109", "major": "65"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "65.0.3325.109"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.016.C1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.98 Safari/537.36", "browser": {"name": "Chrome", "version": "61.0.3163.98", "major": "61"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "61.0.3163.98"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.016.C1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Chrome", "version": "67.0.3396.87", "major": "67"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.016.C1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36", "browser": {"name": "Chrome", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.D1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Safari/537.36", "browser": {"name": "Chrome", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.D1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36", "browser": {"name": "Chrome", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.N1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Safari/537.36", "browser": {"name": "Chrome", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.N1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.91 Safari/537.36", "browser": {"name": "Chrome", "version": "68.0.3440.91", "major": "68"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "68.0.3440.91"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.N1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3983.2 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3983.2", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3983.2"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.Y1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Safari/537.36", "browser": {"name": "Chrome", "version": "67.0.3396.87", "major": "67"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.Y1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36", "browser": {"name": "Chrome", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.Z1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.91 Safari/537.36", "browser": {"name": "Chrome", "version": "68.0.3440.91", "major": "68"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "68.0.3440.91"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM4.171019.021.Z1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.91 Safari/537.36", "browser": {"name": "Chrome", "version": "69.0.3497.91", "major": "69"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "69.0.3497.91"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.181005.003) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.98 Safari/537.36", "browser": {"name": "Chrome", "version": "61.0.3163.98", "major": "61"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "61.0.3163.98"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.181005.003) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36", "browser": {"name": "Chrome", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.181105.002) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.91 Safari/537.36", "browser": {"name": "Chrome", "version": "68.0.3440.91", "major": "68"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "68.0.3440.91"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.98 Safari/537.36", "browser": {"name": "Chrome", "version": "61.0.3163.98", "major": "61"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "61.0.3163.98"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36", "browser": {"name": "Chrome", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.185 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.93 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.64 Safari/537.36", "browser": {"name": "Chrome", "version": "70.0.3538.64", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "70.0.3538.64"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Safari/537.36", "browser": {"name": "Chrome", "version": "70.0.3538.110", "major": "70"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "70.0.3538.110"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.99 Safari/537.36", "browser": {"name": "Chrome", "version": "71.0.3578.99", "major": "71"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.96 Safari/537.36", "browser": {"name": "Chrome", "version": "72.0.3626.96", "major": "72"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "72.0.3626.96"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36", "browser": {"name": "Chrome", "version": "72.0.3626.121", "major": "72"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "72.0.3626.121"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.75 Safari/537.36", "browser": {"name": "Chrome", "version": "73.0.3683.75", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "73.0.3683.75"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.112 Safari/537.36", "browser": {"name": "Chrome", "version": "74.0.3729.112", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.112"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.136 Safari/537.36", "browser": {"name": "Chrome", "version": "74.0.3729.136", "major": "74"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.136"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.67 Safari/537.36", "browser": {"name": "Chrome", "version": "75.0.3770.67", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "75.0.3770.67"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Chrome", "version": "75.0.3770.101", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.62 Safari/537.36", "browser": {"name": "Chrome", "version": "76.0.3809.62", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "76.0.3809.62"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.111 Safari/537.36", "browser": {"name": "Chrome", "version": "76.0.3809.111", "major": "76"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "76.0.3809.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.42 Safari/537.36", "browser": {"name": "Chrome", "version": "77.0.3865.42", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "77.0.3865.42"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.92 Safari/537.36", "browser": {"name": "Chrome", "version": "77.0.3865.92", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "77.0.3865.92"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.43 Safari/537.36", "browser": {"name": "Chrome", "version": "78.0.3904.43", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.43"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.90 Safari/537.36", "browser": {"name": "Chrome", "version": "78.0.3904.90", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Chrome", "version": "78.0.3904.108", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.36 Safari/537.36", "browser": {"name": "Chrome", "version": "79.0.3945.36", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.36"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.71 Safari/537.36", "browser": {"name": "Chrome", "version": "79.0.3945.71", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.71"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Chrome", "version": "79.0.3945.93", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Chrome", "version": "79.0.3945.136", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.99 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.99", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.119 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.119", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4035.5 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4035.5", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4035.5"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.34 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.34", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.34"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.96 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.96", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.96"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.117 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.117", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.117"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.142 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.142", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.142"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.44 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.44", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.44"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.60 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.60", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.60"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.96 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.96", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.96"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.116", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.44 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.44", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.44"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.69 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.69", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.69"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.89 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.89", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.111 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.111", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.127 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.127", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.38 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.38", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.38"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.78 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.78", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.78"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.101 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.120 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.120", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.120"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.30 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.30", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.30"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.68 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.68", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.68"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.99 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.99", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.111 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.111", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.114 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.114", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.114"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.27 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.27", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.27"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.49 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.49", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.49"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.86 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.86", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.29 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.29", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.29"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.51 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.51", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.51"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.93 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.141 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.141", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.153 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.153", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.153"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.186 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.186", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.186"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.32 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.32", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.32"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.48 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.48", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.48"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.69 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.69", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.69"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.86 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.30 Safari/537.36", "browser": {"name": "Chrome", "version": "90.0.4430.30", "major": "90"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "90.0.4430.30"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.51 Safari/537.36", "browser": {"name": "Chrome", "version": "90.0.4430.51", "major": "90"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "90.0.4430.51"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Chrome", "version": "75.0.3770.101", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/86.0.4240.198 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T580) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/80.0.3987.162 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.162", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T580", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "80.0.3987.162"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T580) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/85.0.4183.102 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.102", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T580", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "85.0.4183.102"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T580) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.75 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.75", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T580", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.75"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T580) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T580", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T580) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T580", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T580) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T580", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T585) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.111 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.111", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T585", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T585) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T585", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T830) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T830", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X300) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.132", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X300"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X300) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.111 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.111", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X300"}, "engine": {"name": "Blink", "version": "81.0.4044.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X300) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.83 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.83", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X300"}, "engine": {"name": "Blink", "version": "83.0.4103.83"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X300) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.81 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X300"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X300) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X300"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X700 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.92 Safari/537.36", "browser": {"name": "Chrome", "version": "77.0.3865.92", "major": "77"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X700 Pro"}, "engine": {"name": "Blink", "version": "77.0.3865.92"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X700 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.99 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.99", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X700 Pro"}, "engine": {"name": "Blink", "version": "80.0.3987.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X700 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.119 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.119", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X700 Pro"}, "engine": {"name": "Blink", "version": "80.0.3987.119"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X700 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X700 Pro"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X700 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.117 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.117", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X700 Pro"}, "engine": {"name": "Blink", "version": "81.0.4044.117"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X700 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.96 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.96", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X700 Pro"}, "engine": {"name": "Blink", "version": "83.0.4103.96"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X700 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.89 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.89", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X700 Pro"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X700 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.99 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.99", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X700 Pro"}, "engine": {"name": "Blink", "version": "86.0.4240.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; TESLA MediaBox X700 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X700 Pro"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_L8_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.116 Safari/537.36", "browser": {"name": "Chrome", "version": "79.0.3945.116", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla_L8_2"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_L8_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla_L8_2"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_L8_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.101 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla_L8_2"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_L8_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla_L8_2"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_L8_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla_L8_2"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_L8_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla_L8_2"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.182 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.182", "major": "88"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "88.0.4324.182"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X500) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.90 Safari/537.36", "browser": {"name": "Chrome", "version": "73.0.3683.90", "major": "73"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X500"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X500) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X500"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X500) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.96 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.96", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X500"}, "engine": {"name": "Blink", "version": "83.0.4103.96"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X500) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.111 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.111", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X500"}, "engine": {"name": "Blink", "version": "84.0.4147.111"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X500) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.110 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X500"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X900 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36", "browser": {"name": "Chrome", "version": "72.0.3626.121", "major": "72"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X900 Pro"}, "engine": {"name": "Blink", "version": "72.0.3626.121"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X900 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.93 Safari/537.36", "browser": {"name": "Chrome", "version": "79.0.3945.93", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X900 Pro"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X900 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.132", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X900 Pro"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X900 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X900 Pro"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X900 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X900 Pro"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X900 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.111 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.111", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X900 Pro"}, "engine": {"name": "Blink", "version": "84.0.4147.111"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X900 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.81 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X900 Pro"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X900 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X900 Pro"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X900 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.110 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X900 Pro"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X900 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X900 Pro"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; CMR-AL09) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.75 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.75", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "CMR-AL09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "86.0.4240.75"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; DL1023) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.193 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.193", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "DL1023"}, "engine": {"name": "Blink", "version": "86.0.4240.193"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; KFMAWI) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.67 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.67", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "87.0.4280.67"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; KFMAWI) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "KFMAWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Lenovo TB-X605F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "TB-X605F", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.101 Safari/537.36", "browser": {"name": "Chrome", "version": "75.0.3770.101", "major": "75"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.62 Safari/537.36", "browser": {"name": "Chrome", "version": "78.0.3904.62", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.62"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36", "browser": {"name": "Chrome", "version": "78.0.3904.108", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.125 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.101 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.75", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.75"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.110 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-T825) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T825", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.96 Safari/537.36", "browser": {"name": "Chrome", "version": "78.0.3904.96", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.99 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.99", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36", "browser": {"name": "Chrome", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.111 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.111", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.111"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.96 Safari/537.36", "browser": {"name": "Chrome", "version": "83.0.4103.96", "major": "83"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.89 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.89", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.89"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.111 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.111", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "84.0.4147.111"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.81 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.120 Safari/537.36", "browser": {"name": "Chrome", "version": "85.0.4183.120", "major": "85"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.120"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4208.3 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4208.3", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4208.3"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.30 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.30", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.30"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.110 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.93 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.86 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-P610) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.111 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.111", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-P610", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.111"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-P610) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-P610", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-P610) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-P610", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T500) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T500", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T500) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T500", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T510) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/84.0.4147.125 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T510", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T510) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.75 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.75", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T510", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.75"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T510) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.88 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.88", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T510", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.88"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T510) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T510", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T515) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T515", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T540) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T540", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T590) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T590", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T595) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T595", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T720) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T720", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T725) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T725", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T830) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/84.0.4147.125 Safari/537.36", "browser": {"name": "Chrome", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T830", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T830) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T830", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T835) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T835", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T860) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.111 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.111", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T860", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.111"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T860) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T860", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T865) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.99 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.99", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T865", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.99"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T970) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T970", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 11) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.90 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36", "browser": {"name": "Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.107 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.107", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.107"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.93 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.141 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.141", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.141"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.155 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.155", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.155"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.181 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.72 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.86 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.91 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.91", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.91"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.66 Safari/537.36", "browser": {"name": "Chrome", "version": "90.0.4430.66", "major": "90"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "90.0.4430.66"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-T870) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Safari/537.36", "browser": {"name": "Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T870", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Nexus 7 Build/MRA58V) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.2526.83 Safari/537.36", "browser": {"name": "Chrome", "version": "47.0.2526.83", "major": "47"}, "cpu": {}, "device": {"type": "tablet", "model": "Nexus 7", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "47.0.2526.83"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Aquaris M10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.85 Safari/537.36", "browser": {"name": "Chrome", "version": "94.0.4606.85", "major": "94"}, "cpu": {}, "device": {"type": "tablet", "model": "Aquaris M10"}, "engine": {"name": "Blink", "version": "94.0.4606.85"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-T860) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Iron Safari/537.36", "browser": {"name": "Chrome", "version": "91.0.4472.77", "major": "91"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T860", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "91.0.4472.77"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SCM-W09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.66 Safari/537.36", "browser": {"name": "Chrome", "version": "98.0.4758.66", "major": "98"}, "cpu": {}, "device": {"type": "tablet", "model": "SCM-W09"}, "engine": {"name": "Blink", "version": "98.0.4758.66"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; AGS-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.94 Safari/537.36", "browser": {"name": "Chrome", "version": "99.0.4844.94", "major": "99"}, "cpu": {}, "device": {"type": "tablet", "model": "AGS-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "99.0.4844.94"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; SM-X800) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.40 Safari/537.36", "browser": {"name": "Chrome", "version": "101.0.4951.40", "major": "101"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-X800", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "101.0.4951.40"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 11) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.90 Safari/537.36", "browser": {"name": "Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; SM-F926B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "107.0.0.0", "major": "107"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-F926B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "107.0.0.0"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Lenovo TB-7306F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "107.0.0.0", "major": "107"}, "cpu": {}, "device": {"type": "tablet", "model": "TB-7306F", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "107.0.0.0"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "124.0.0.0", "major": "124"}, "cpu": {}, "device": {"type": "tablet", "model": "K"}, "engine": {"name": "Blink", "version": "124.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "120.0.0.0", "major": "120"}, "cpu": {}, "device": {"type": "tablet", "model": "K"}, "engine": {"name": "Blink", "version": "120.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "124.0.0.0", "major": "124"}, "cpu": {}, "device": {"type": "tablet", "model": "K"}, "engine": {"name": "Blink", "version": "124.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.52 Safari/537.36", "browser": {"name": "Chrome", "version": "93.0.4577.52", "major": "93"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "93.0.4577.52"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "128.0.0.0", "major": "128"}, "cpu": {}, "device": {"type": "tablet", "model": "K"}, "engine": {"name": "Blink", "version": "128.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 14; PA2353) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "110.0.0.0", "major": "110"}, "cpu": {}, "device": {"type": "tablet", "model": "PA2353"}, "engine": {"name": "Blink", "version": "110.0.0.0"}, "os": {"name": "Android", "version": "14"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "124.0.0.0", "major": "124"}, "cpu": {}, "device": {"type": "tablet", "model": "K"}, "engine": {"name": "Blink", "version": "124.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "127.0.0.0", "major": "127"}, "cpu": {}, "device": {"type": "tablet", "model": "K"}, "engine": {"name": "Blink", "version": "127.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "124.0.0.0", "major": "124"}, "cpu": {}, "device": {"type": "tablet", "model": "K"}, "engine": {"name": "Blink", "version": "124.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 13; 21051182G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "110.0.0.0", "major": "110"}, "cpu": {}, "device": {"type": "mobile", "model": "21051182G", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "110.0.0.0"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "130.0.0.0", "major": "130"}, "cpu": {}, "device": {"type": "tablet", "model": "K"}, "engine": {"name": "Blink", "version": "130.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "127.0.0.0", "major": "127"}, "cpu": {}, "device": {"type": "tablet", "model": "K"}, "engine": {"name": "Blink", "version": "127.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "130.0.0.0", "major": "130"}, "cpu": {}, "device": {"type": "tablet", "model": "K"}, "engine": {"name": "Blink", "version": "130.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "120.0.0.0", "major": "120"}, "cpu": {}, "device": {"type": "tablet", "model": "K"}, "engine": {"name": "Blink", "version": "120.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36", "browser": {"name": "Chrome", "version": "137.0.0.0", "major": "137"}, "cpu": {}, "device": {"type": "tablet", "model": "K"}, "engine": {"name": "Blink", "version": "137.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.52 Safari/537.36", "browser": {"name": "Chrome", "version": "93.0.4577.52", "major": "93"}, "cpu": {}, "device": {"type": "tablet", "model": "KFSUWI", "vendor": "Amazon"}, "engine": {"name": "Blink", "version": "93.0.4577.52"}, "os": {"name": "Android", "version": "5.1.1"}}]