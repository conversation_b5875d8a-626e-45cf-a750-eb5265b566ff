import{_ as t,l as n,H as s,k as i,I as d}from"./MermaidPreview-DN0CF7bz.js";import{p}from"./radar-MK3ICKWK-DiEKPfCW.js";import"./premium-CrQDER7x.js";import"./user-config-BrqC82sm.js";import"./merge-CG3iZ9md.js";import"./map-CPMUsqym.js";import"./_baseUniq-CDn7CWnQ.js";import"./uniqBy-Zyo4kdnp.js";import"./min-1QEMBxke.js";import"./reduce-d3EV_kCa.js";import"./clone-CpeMRwbc.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},r=new Error().stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="9a93bc81-8f9f-46e8-8e9f-39ca049cc2d3",e._sentryDebugIdIdentifier="sentry-dbid-9a93bc81-8f9f-46e8-8e9f-39ca049cc2d3")}catch{}})();var f={parse:t(async e=>{const r=await p("info",e);n.debug(r)},"parse")},g={version:d.version},c=t(()=>g.version,"getVersion"),m={getVersion:c},v=t((e,r,a)=>{n.debug(`rendering info diagram
`+e);const o=s(r);i(o,100,400,!0),o.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${a}`)},"draw"),u={draw:v},z={parser:f,db:m,renderer:u};export{z as diagram};
//# sourceMappingURL=infoDiagram-PH2N3AL5-DAgfEy0p.js.map
