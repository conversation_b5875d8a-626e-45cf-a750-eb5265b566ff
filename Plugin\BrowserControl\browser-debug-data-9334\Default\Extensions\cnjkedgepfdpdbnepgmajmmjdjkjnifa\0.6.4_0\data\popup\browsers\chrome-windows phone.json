[{"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.85 Safari/537.36", "browser": {"name": "Chrome", "version": "54.0.2840.85", "major": "54"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "Blink", "version": "54.0.2840.85"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0", "browser": {"name": "Chrome", "version": "46.0.2486.0", "major": "46"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "Blink", "version": "46.0.2486.0"}, "os": {"name": "Windows Phone", "version": "10.0"}}]