[{"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_3_2 like Mac OS X) AppleWebKit/601.1 (KHTML, like Gecko) CriOS/51.0.2704.104 Mobile/13F69 Safari/601.1.46", "browser": {"name": "Mobile Chrome", "version": "51.0.2704.104", "major": "51"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1"}, "os": {"name": "iOS", "version": "9.3.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_1_1 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) CriOS/47.0.2526.70 Mobile/12B436 Safari/600.1.4 (000410)", "browser": {"name": "Mobile Chrome", "version": "47.0.2526.70", "major": "47"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.1.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/79.0.3945.73 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "79.0.3945.73", "major": "79"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/70.0.3538.75 Mobile/15E148 Safari/605.1", "browser": {"name": "Mobile Chrome", "version": "70.0.3538.75", "major": "70"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/78.0.3904.84 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "78.0.3904.84", "major": "78"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/69.0.3497.105 Mobile/15E148 Safari/605.1", "browser": {"name": "Mobile Chrome", "version": "69.0.3497.105", "major": "69"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/70.0.3538.75 Mobile/15E148 Safari/605.1", "browser": {"name": "Mobile Chrome", "version": "70.0.3538.75", "major": "70"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_4_1 like Mac OS X) AppleWebKit/604.1.34 (KHTML, like Gecko) CriOS/68.0.3440.83 Mobile/15G77 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "68.0.3440.83", "major": "68"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.1.34"}, "os": {"name": "iOS", "version": "11.4.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/80.0.3987.95 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.95", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_4_1 like Mac OS X) AppleWebKit/604.1.34 (KHTML, like Gecko) CriOS/67.0.3396.87 Mobile/15G77 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "67.0.3396.87", "major": "67"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.1.34"}, "os": {"name": "iOS", "version": "11.4.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_4 like Mac OS X) AppleWebKit/604.1.34 (KHTML, like Gecko) CriOS/67.0.3396.87 Mobile/15F79 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "67.0.3396.87", "major": "67"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.1.34"}, "os": {"name": "iOS", "version": "11.4"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/79.0.3945.73 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "79.0.3945.73", "major": "79"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/77.0.3865.103 Mobile/15E148 Safari/605.1", "browser": {"name": "Mobile Chrome", "version": "77.0.3865.103", "major": "77"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/75.0.3770.103 Mobile/15E148 Safari/605.1", "browser": {"name": "Mobile Chrome", "version": "75.0.3770.103", "major": "75"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/78.0.3904.84 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "78.0.3904.84", "major": "78"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/80.0.3987.95 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.95", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_3_2 like Mac OS X) AppleWebKit/601.1 (KHTML, like Gecko) CriOS/51.0.2704.104 Mobile/13F69 Safari/601.1.46", "browser": {"name": "Mobile Chrome", "version": "51.0.2704.104", "major": "51"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1"}, "os": {"name": "iOS", "version": "9.3.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/79.0.3945.73 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "79.0.3945.73", "major": "79"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/79.0.3945.73 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "79.0.3945.73", "major": "79"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.77", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "14.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.163 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.163", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "14.4"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.163 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.163", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "14.5"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 14_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.163 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.163", "major": "87"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "14.5"}}, {"ua": "Mozilla/5.0 (iPod; CPU iPhone OS 14_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.163 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.163", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "iPod", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "14.5"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/98.0.4758.85 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "98.0.4758.85", "major": "98"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "15.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/98.0.4758.85 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "98.0.4758.85", "major": "98"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "15.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.163 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.163", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "14.4"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/124.0.6367.111 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "124.0.6367.111", "major": "124"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.4"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/125.0.6422.51 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "125.0.6422.51", "major": "125"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.4"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/126.0.6478.54 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "126.0.6478.54", "major": "126"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.5"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/125.0.6422.51 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "125.0.6422.51", "major": "125"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/128.0.6613.98 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "128.0.6613.98", "major": "128"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.5"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/126.0.6478.54 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "126.0.6478.54", "major": "126"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.5"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/127.0.6533.77 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "127.0.6533.77", "major": "127"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.6"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/128.0.6613.98 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "128.0.6613.98", "major": "128"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.6"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/129.0.6668.21 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "129.0.6668.21", "major": "129"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "18.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/70.0.3538.75 Mobile/15E148 Safari/605.1", "browser": {"name": "Mobile Chrome", "version": "70.0.3538.75", "major": "70"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/98.0.4758.85 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "98.0.4758.85", "major": "98"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "15.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/125.0.6422.51 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "125.0.6422.51", "major": "125"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.5.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/125.0.6422.51 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "125.0.6422.51", "major": "125"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/131.0.6778.154 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "131.0.6778.154", "major": "131"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "18.1.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/126.0.6478.54 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "126.0.6478.54", "major": "126"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.5"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 17_7_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/131.0.6778.154 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "131.0.6778.154", "major": "131"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.7.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/124.0.6367.111 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "124.0.6367.111", "major": "124"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.4"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 17_7_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Chrome", "version": "135.0.7049.53", "major": "135"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.7.5"}}]