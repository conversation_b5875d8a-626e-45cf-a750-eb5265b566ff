[{"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Safari/537.36 Instagram 172.0.0.21.123 Android (27/8.1.0; 320dpi; 1800x2448; Google/google; Pixel C; dragon; dragon; ro_RO; 269790803)", "browser": {"name": "Instagram", "version": "172.0.0.21.123", "major": "172"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.86 Safari/537.36 Instagram 178.1.0.37.123 Android (27/8.1.0; 320dpi; 1800x2448; Google/google; Pixel C; dragon; dragon; ro_RO; 277249248)", "browser": {"name": "Instagram", "version": "178.1.0.37.123", "major": "178"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Safari/537.36 Instagram 179.0.0.31.132 Android (27/8.1.0; 320dpi; 1800x2448; Google/google; Pixel C; dragon; dragon; en_US; 278625444)", "browser": {"name": "Instagram", "version": "179.0.0.31.132", "major": "179"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; IN2017 Build/RP1A.201105.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/100.0.4896.58 Mobile Safari/537.36 Instagram 227.0.0.12.117 Android (30/11; 420dpi; 1180x2255; OnePlus; IN2017; OnePlus8TMO; qcom; nl_NL; 333717270)", "browser": {"name": "Instagram", "version": "227.0.0.12.117", "major": "227"}, "cpu": {}, "device": {"type": "mobile", "model": "IN2017"}, "engine": {"name": "Blink", "version": "100.0.4896.58"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-N985F Build/RP1A.200720.012; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/100.0.4896.58 Mobile Safari/537.36 Instagram 227.0.0.12.117 Android (30/11; 420dpi; 1180x2123; samsung; SM-N985F; c2s; exynos990; nl_NL; 333717270)", "browser": {"name": "Instagram", "version": "227.0.0.12.117", "major": "227"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N985F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "100.0.4896.58"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; IN2017 Build/RP1A.201105.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/100.0.4896.58 Mobile Safari/537.36 Instagram 227.0.0.12.117 Android (30/11; 420dpi; 1180x2255; OnePlus; IN2017; OnePlus8TMO; qcom; nl_NL; 333717270)", "browser": {"name": "Instagram", "version": "227.0.0.12.117", "major": "227"}, "cpu": {}, "device": {"type": "mobile", "model": "IN2017"}, "engine": {"name": "Blink", "version": "100.0.4896.58"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; IN2017 Build/RP1A.201105.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/100.0.4896.58 Mobile Safari/537.36 Instagram 227.0.0.12.117 Android (30/11; 420dpi; 1180x2255; OnePlus; IN2017; OnePlus8TMO; qcom; en_US; 333717270)", "browser": {"name": "Instagram", "version": "227.0.0.12.117", "major": "227"}, "cpu": {}, "device": {"type": "mobile", "model": "IN2017"}, "engine": {"name": "Blink", "version": "100.0.4896.58"}, "os": {"name": "Android", "version": "11"}}]