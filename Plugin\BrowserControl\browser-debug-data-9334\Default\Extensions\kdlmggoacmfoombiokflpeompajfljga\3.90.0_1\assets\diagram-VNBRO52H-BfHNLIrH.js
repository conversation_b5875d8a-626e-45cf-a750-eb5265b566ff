import{p as $}from"./chunk-4BMEZGHF-ClwwOidP.js";import{C as B,s as S,g as D,n as F,o as _,b as z,c as P,_ as l,l as y,D as v,E as W,t as E,H as T,k as A}from"./MermaidPreview-DN0CF7bz.js";import{p as I}from"./radar-MK3ICKWK-DiEKPfCW.js";import"./premium-CrQDER7x.js";import"./user-config-BrqC82sm.js";import"./merge-CG3iZ9md.js";import"./map-CPMUsqym.js";import"./_baseUniq-CDn7CWnQ.js";import"./uniqBy-Zyo4kdnp.js";import"./min-1QEMBxke.js";import"./reduce-d3EV_kCa.js";import"./clone-CpeMRwbc.js";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="bf749d73-44a1-4305-96c2-f06530bf8c89",t._sentryDebugIdIdentifier="sentry-dbid-bf749d73-44a1-4305-96c2-f06530bf8c89")}catch{}})();var x={packet:[]},h=structuredClone(x),N=B.packet,L=l(()=>{const t=v({...N,...W().packet});return t.showBits&&(t.paddingY+=10),t},"getConfig"),Y=l(()=>h.packet,"getPacket"),H=l(t=>{t.length>0&&h.packet.push(t)},"pushWord"),M=l(()=>{E(),h=structuredClone(x)},"clear"),m={pushWord:H,getPacket:Y,getConfig:L,clear:M,setAccTitle:S,getAccTitle:D,setDiagramTitle:F,getDiagramTitle:_,getAccDescription:z,setAccDescription:P},O=1e4,G=l(t=>{$(t,m);let e=-1,r=[],s=1;const{bitsPerRow:i}=m.getConfig();for(let{start:a,end:o,label:p}of t.blocks){if(o&&o<a)throw new Error(`Packet block ${a} - ${o} is invalid. End must be greater than start.`);if(a!==e+1)throw new Error(`Packet block ${a} - ${o??a} is not contiguous. It should start from ${e+1}.`);for(e=o??a,y.debug(`Packet block ${a} - ${e} with label ${p}`);r.length<=i+1&&m.getPacket().length<O;){const[b,c]=K({start:a,end:o,label:p},s,i);if(r.push(b),b.end+1===s*i&&(m.pushWord(r),r=[],s++),!c)break;({start:a,end:o,label:p}=c)}}m.pushWord(r)},"populate"),K=l((t,e,r)=>{if(t.end===void 0&&(t.end=t.start),t.start>t.end)throw new Error(`Block start ${t.start} is greater than block end ${t.end}.`);return t.end+1<=e*r?[t,void 0]:[{start:t.start,end:e*r-1,label:t.label},{start:e*r,end:t.end,label:t.label}]},"getNextFittingBlock"),R={parse:l(async t=>{const e=await I("packet",t);y.debug(e),G(e)},"parse")},U=l((t,e,r,s)=>{const i=s.db,a=i.getConfig(),{rowHeight:o,paddingY:p,bitWidth:b,bitsPerRow:c}=a,u=i.getPacket(),n=i.getDiagramTitle(),f=o+p,d=f*(u.length+1)-(n?0:o),g=b*c+2,k=T(e);k.attr("viewbox",`0 0 ${g} ${d}`),A(k,d,g,a.useMaxWidth);for(const[w,C]of u.entries())X(k,C,w,a);k.append("text").text(n).attr("x",g/2).attr("y",d-f/2).attr("dominant-baseline","middle").attr("text-anchor","middle").attr("class","packetTitle")},"draw"),X=l((t,e,r,{rowHeight:s,paddingX:i,paddingY:a,bitWidth:o,bitsPerRow:p,showBits:b})=>{const c=t.append("g"),u=r*(s+a)+a;for(const n of e){const f=n.start%p*o+1,d=(n.end-n.start+1)*o-i;if(c.append("rect").attr("x",f).attr("y",u).attr("width",d).attr("height",s).attr("class","packetBlock"),c.append("text").attr("x",f+d/2).attr("y",u+s/2).attr("class","packetLabel").attr("dominant-baseline","middle").attr("text-anchor","middle").text(n.label),!b)continue;const g=n.end===n.start,k=u-2;c.append("text").attr("x",f+(g?d/2:0)).attr("y",k).attr("class","packetByte start").attr("dominant-baseline","auto").attr("text-anchor",g?"middle":"start").text(n.start),g||c.append("text").attr("x",f+d).attr("y",k).attr("class","packetByte end").attr("dominant-baseline","auto").attr("text-anchor","end").text(n.end)}},"drawWord"),j={draw:U},q={byteFontSize:"10px",startByteColor:"black",endByteColor:"black",labelColor:"black",labelFontSize:"12px",titleColor:"black",titleFontSize:"14px",blockStrokeColor:"black",blockStrokeWidth:"1",blockFillColor:"#efefef"},J=l(({packet:t}={})=>{const e=v(q,t);return`
	.packetByte {
		font-size: ${e.byteFontSize};
	}
	.packetByte.start {
		fill: ${e.startByteColor};
	}
	.packetByte.end {
		fill: ${e.endByteColor};
	}
	.packetLabel {
		fill: ${e.labelColor};
		font-size: ${e.labelFontSize};
	}
	.packetTitle {
		fill: ${e.titleColor};
		font-size: ${e.titleFontSize};
	}
	.packetBlock {
		stroke: ${e.blockStrokeColor};
		stroke-width: ${e.blockStrokeWidth};
		fill: ${e.blockFillColor};
	}
	`},"styles"),ct={parser:R,db:m,renderer:j,styles:J};export{ct as diagram};
//# sourceMappingURL=diagram-VNBRO52H-BfHNLIrH.js.map
